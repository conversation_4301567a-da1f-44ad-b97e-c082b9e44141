/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.dao.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.LockModeType;
import javax.persistence.PersistenceContext;
import javax.persistence.PersistenceException;
import javax.persistence.Query;
import javax.validation.ConstraintViolationException;

import com.stpl.tech.scm.core.util.ValidationUtil;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.dao.SCMAbstractDao;

/**
 * Created by Rahul Singh on 04-05-2016.
 */
public abstract class SCMAbstractDaoImpl implements SCMAbstractDao {

    private static final Logger LOG = LoggerFactory.getLogger(SCMAbstractDaoImpl.class);
    private static final int MAX_BATCH_SIZE = 100;

    @PersistenceContext(unitName = "SCMDataSourcePUName")
    @Qualifier(value = "SCMDataSourceEMFactory")
    protected EntityManager manager;


    @Override
    public <T> T update(T t) {
        return update(t, true);
    }

    @Override
    public <T> T update(T data, boolean flush) {

        try {
            data = manager.merge(data);
            if (flush) {
                manager.flush();
            }

            return data;
        } catch (Exception e) {
            LOG.error("Error updating {}", data.getClass().getName(), e);
        }
        return null;
    }

    @Override
    public <T> List<T> update(List<T> dataList, boolean flush) {
        try {
            for (T data : dataList) {
                update(data, false);
            }
            manager.flush();
            return dataList;
        } catch (Exception e) {
            LOG.error("Error updating {}", dataList.getClass().getName(), e);
        }
        return new ArrayList<>();
    }


    @Override
    public <T> T add(T t) {
        try {
            return add(t, true);
        } catch (SumoException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public <T> T add(T data, boolean flush) throws SumoException {
        try {
            manager.persist(data);
            if (flush) {
                manager.flush();
            }
            return data;
        } catch (ConstraintViolationException e1) {
            throw new SumoException("Error while adding data", e1.getCause());
        } catch (PersistenceException e2) {
            throw new SumoException("Constraint Error while adding data", e2.getCause());
        } catch (Exception e) {
            LOG.error("Error adding {}", data.getClass().getName(), e);
        }
        return null;
    }

    @Override
    public <T> List<T> addAll(List<T> list) {
        List<T> l = new ArrayList<>();
        if (list == null || list.isEmpty()) {
            return l;
        }
        try {
            for (T data : list) {
                manager.persist(data);
                l.add(data);
            }
            manager.flush();
            return l;
        } catch (Exception e) {
            LOG.error("Error adding {}", list.getClass().getName(), e);
        }
        return l;
    }

    @Override
    public <T> List<T> findAll(Class<T> data) {
        Query query = manager.createQuery("FROM " + data.getName() + " T");
        return query.getResultList();
    }

    @Override
    public <T, R> T find(Class<T> data, R key) {
        return manager.find(data, key);
    }

    @Override
    public <T, R> T find(Class<T> data, R key, LockModeType lockModeType) {
        return manager.find(data, key, lockModeType);
    }

    @Override
    public <T> void delete(T data) {
        try {
            manager.remove(data);
            manager.flush();
        } catch (Exception e) {
            LOG.error("Error deleting {}", data.getClass().getName(), e);
        }
    }

    @Override
    public void flush() {
        manager.flush();
    }

    private void flushAndClear() {
        manager.flush();
        manager.clear();
    }

    @Override
    public void insertInBulk(List<?> entities) {
        // TODO --> point to batch Insert in AbstractDao later...
        addAllInBatch(entities);
    }

    @Override
    public void updateInBulk(List<?> entities) {
        // TODO --> point to batch Update in AbstractDao later...
        updateAllInBatch(entities);
    }

    private void addAllInBatch(List<?> entities) {
        if(!ValidationUtil.isNotEmpty(entities, "No entities to persist in addAllInBatch")) {
            return;
        }
        try {
            for (int i = 0; i < entities.size(); i++) {
                manager.persist(entities.get(i));
                if ((i + 1) % MAX_BATCH_SIZE == 0) {
                    flushAndClear();
                }
            }
            // flush and clear for the remaining entities
            flushAndClear();
        } catch (Exception e) {
            LOG.error("Error in batch adding {}", entities.getClass().getName(), e);
        }
    }

    private void updateAllInBatch(List<?> entities) {
        if(!ValidationUtil.isNotEmpty(entities, "No entities to persist in updateInBatch")) {
            return;
        }
        try {
            for (int i = 0; i < entities.size(); i++) {
                manager.merge(entities.get(i));
                if ((i + 1) % MAX_BATCH_SIZE == 0) {
                    flushAndClear();
                }
            }
            // flush and clear for the remaining entities
            flushAndClear();
        } catch (Exception e) {
            LOG.error("Error in batch updating {}", entities.getClass().getName(), e);
        }
    }


}
