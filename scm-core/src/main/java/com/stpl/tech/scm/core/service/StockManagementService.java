/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.core.service;

import com.stpl.tech.kettle.core.data.budget.vo.BudgetDetail;
import com.stpl.tech.kettle.core.data.budget.vo.ConsumablesAggregate;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.WebServiceCallException;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.scm.core.ThresholdType;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.NegativeStockException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.model.ReverseProductionBookingData;
import com.stpl.tech.scm.data.model.SCMDayCloseEventData;
import com.stpl.tech.scm.data.model.StockEventCalendarData;
import com.stpl.tech.scm.data.model.UnitDetailData;
import com.stpl.tech.scm.domain.model.ApprovalDetail;
import com.stpl.tech.scm.domain.model.ConsumptionData;
import com.stpl.tech.scm.domain.model.CostDetail;
import com.stpl.tech.scm.domain.model.DayCloseEvent;
import com.stpl.tech.scm.domain.model.DayWiseExpiryProduct;
import com.stpl.tech.scm.domain.model.ExpiryDataRequest;
import com.stpl.tech.scm.domain.model.FixedAssetDayCloseResponseObject;
import com.stpl.tech.scm.domain.model.PendingMilkBread;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductionBooking;
import com.stpl.tech.scm.domain.model.ReverseProductionBooking;
import com.stpl.tech.scm.domain.model.StockCalendarEventCheckVO;
import com.stpl.tech.scm.domain.model.StockEventType;
import com.stpl.tech.scm.domain.model.StockInventoryData;
import com.stpl.tech.scm.domain.model.StockTakeSumoDayCloseEventDTO;
import com.stpl.tech.scm.domain.model.StockTakeSumoDayCloseProductsDTO;
import com.stpl.tech.scm.domain.model.StockTakeType;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.scm.domain.model.VarianceAcknowledgementCheck;
import com.stpl.tech.scm.domain.model.VarianceAcknowledgementDetail;
import com.stpl.tech.scm.domain.model.VarianceEdit;
import com.stpl.tech.scm.domain.model.WastageAggregatedData;
import com.stpl.tech.scm.domain.model.WastageData;
import com.stpl.tech.scm.domain.model.WastageEvent;
import com.stpl.tech.scm.domain.vo.ConsumptionVO;
import com.stpl.tech.scm.reports.modal.VarianceModal;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.notification.model.ResponseData;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface StockManagementService {

    public List<WastageEvent> addWastageEvent(List<WastageEvent> wastageEvent) throws InventoryUpdateException, DataNotFoundException;

    public List<WastageEvent> addManualWastageEvent(List<WastageEvent> wastageEvent, boolean verifyInventory, Integer unitId) throws InventoryUpdateException, DataNotFoundException, SumoException;

    public Map<String, Map<Integer, Map<String, Float>>> getAcknowledgedRoProducts(Integer unitId, List<String> dates, Date lastDate, Date firstOrderingDate);

    public ExpiryDataRequest getTransferOrderItemExpiries(ExpiryDataRequest expiryDataRequest) throws InventoryUpdateException, DataNotFoundException;

    public Map<Integer, Map<String, Float>> getTotalStock(Map<Integer, Map<String, Float>> inStock, Map<Integer, Map<String, Float>> inTransit);

    public Map<Integer, Map<String, Float>> getInTransitProducts(Integer unitId, List<Date> dates, Date lastDate, Date firstDate, List<String> stringOfDates);

    public Map<Integer, Map<String, Float>> getInStockProducts(Integer unitId, List<String> dates, Date lastDate, Date firstDate) throws SumoException;

    public List<WastageEvent> verifyPriceData(List<WastageEvent> wastageEvent) throws InventoryUpdateException;

    public List<WastageEvent> getWastageEvents(int unitId, Date businessDate);

    public List<ProductDefinition> getAllProductsByFrequency(StockTakeType stockingFrequency);

    public Boolean checkOpeningRequired(int unitId);

    public ResponseData<ConsumptionData> initiateDayClose(ConsumptionData scmProductConsumption) throws Exception;

    public DayCloseEvent checkClosingInitiated(int unit);

    public boolean cancelDayClose(int unitId, int closureId);

    public ConsumptionData getTransfersForBusinessDate(int unitId, Date businessDate, Date lastBusinessDate, List<Integer> currentProductIds, ConsumptionData availableConsumptionData, Set<Integer> excludeProductIds, Map<Integer, BigDecimal> varianceTillNow);

    public ConsumptionData getGoodsReceivedForBusinessDate(int unitId, Date businessDate, Date lastBusinessDate, List<Integer> currentProductIds, ConsumptionData availableConsumptionData, Set<Integer> excludeProductIds, Map<Integer, BigDecimal> varianceTillNow);

    public ConsumptionData getWastageEventsForBusinessDate(int unitId, Date businessDate, Date lastBusinessDate, List<Integer> currentProductIds, ConsumptionData availableConsumptionData, Set<Integer> excludeProductIds, Map<Integer, BigDecimal> varianceTillNow);

    public void generateSummaryReport(Date previousDayCloseDate) throws IOException, EmailGenerationException;

    boolean validateCalendarEvent(StockInventoryData inventoryData, Date businessDate, StockTakeType stockType) throws InventoryUpdateException;

    public Boolean checkInventoryUpdated(Integer unit, Date businessDate);

    public boolean settleFixedAssetStockForUnit(Integer unit, Date businessDate) throws NegativeStockException, InventoryUpdateException, SumoException, DataNotFoundException;

    void deleteNegativeVarianceExpiryData(SCMDayCloseEventData dayCloseEvent, Integer eventId);

    public boolean settleDayCloseForUnit(Integer unit, Date businessDate, Integer stockTakeSumoDayCloseEventId) throws NegativeStockException, InventoryUpdateException, SumoException, DataNotFoundException;

    public boolean refreshKettleDayClose(Integer unitId) throws DataUpdationException, WebServiceCallException;

    public StockInventoryData getExpectedValues(int unitId, int userId, StockTakeType stockTickType, StockEventType stockEventType , Date businessDate) throws SumoException;

    int checkAndUpdateInventoryForUnit(StockInventoryData detail, StockTakeType stockType, Date businessDate,
                                       boolean runValidation) throws NegativeStockException, InventoryUpdateException, SumoException;

    StockCalendarEventCheckVO cheAvailableStockCalendarEvent(StockInventoryData detail, StockTakeType stockType, Date businessDate,
                                           boolean runValidation);

    List<StockCalendarEventCheckVO> cheAvailableStockCalendarEvents(StockInventoryData detail, Date businessDate,
                                                             boolean runValidation);

    List<StockEventCalendarData> getPendingCalendarEventForUnit(Integer unitId);

    List<StockEventCalendarData> getPlannedCalendarEventByType(Integer unitId, StockTakeType stockType);

    boolean updateStockEventCalendar(List<StockEventCalendarData> request) throws SumoException;

    public int updateInventoryForUnit(StockInventoryData data, StockTakeType stockType, Date businessDate, boolean runValidation) throws NegativeStockException;

    void settleDayCloseForUnit(UnitDetail unit, Date businessDate, Integer stockTakeSumoDayCloseEventId) throws InventoryUpdateException, NegativeStockException, DataNotFoundException, SumoException;

    void logVarianceError(Integer unitId, String unitName, Exception e);

    public Map<Integer, BigDecimal> getCurrentStock(Integer unit);

    public Boolean cancelWastageEvent(Integer wastageId);

    public boolean generateOpeningEvent(UnitDetailData unitDetailData) throws NegativeStockException, SumoException;

    Map<Integer, BigDecimal> getLiveStock(Integer unit) throws URISyntaxException;

    public void calculateWastageAggregate(BudgetDetail budgetDetail, UnitDetail unitDetail, Date businessDate, StockTakeType type);

	public void calculateInventoryAggregate(BudgetDetail b, UnitDetail u, Date businessDate, StockTakeType type);

	public void calculateConsumablesAggregate(BudgetDetail b, UnitDetail u, Date businessDate, StockTakeType type);

	public ConsumablesAggregate getConsumablesAggregateForMonth(int unitId);

	public ConsumablesAggregate getFixedAssetAggregateForMonth(int unitId);

	public BigDecimal getTaxAggregate(int unitId, Date businessDate, List<Integer> categories, StockTakeType type);

	public BigDecimal getFATaxAggregate(int unitId, Date businessDate, ThresholdType type, BigDecimal threshold, StockTakeType stockTakeType);

    Map<Integer, BigDecimal> getFATaxAggregate(int unitId, Date businessDate, ThresholdType type, BigDecimal threshold,
                                 StockTakeType stockTakeType, Date handoverDate, boolean beforehandOver);

    public Integer checkSpecialOrders(int unitId);

    public boolean checkInventoryUpdated(StockInventoryData inventoryData, Date currentBusinessDate, StockTakeType stockType) throws NegativeStockException;

	public DayCloseEvent fetchUnitLastDayClose(int unitId);

	public List<Integer> getUnitsWithMonthlyDone(Date parseDate);

    public List<DayCloseEvent> getInitiatedStockEvents(Integer unitId);
    @Deprecated
	int removeDuplicateKey();

	public StockEventCalendarData addStockCalendarEvent(Integer unitId, StockTakeType stockType, Date date) throws SumoException;

	public Map<Integer, BigDecimal> getInTransitStock(int unitId, Boolean isF9);

	ConsumptionData loadConsumptionData(int unitId, Date businessDate,int closureId);

	List<Integer> fetchDuplicateKey(Boolean isCafe);

	int updateDuplicateKeyLatestFlag(List<Integer> ids , Boolean isCafe);

    public List<WastageEvent> getWastageEventsForDates(String startDate, String endDate, Integer unitId);

    public List<WastageAggregatedData> getWastageAggregatedData(List<WastageData> wastageDataList, Boolean isKitchenOrWH);

    public DayWiseExpiryProduct getDayWiseExpiryProduct(Integer unitId, List<String> dates, Boolean isAggregated, Date firstDate) throws SumoException;

    public void insertMonthlyDumpData();



    SCMDayCloseEventData getSumoDayCloseStatus(Integer unitId, Date previousDate);

    void verifyDayCloses(List<Integer> unitIds);

    public Map<Integer, BigDecimal> getScmProductInventory(Integer unitId, Boolean removeExpired, String keyType);

    public PendingMilkBread getPendingMilkBread(Integer unitId);

    List<WastageEvent> addReverseWastageEvent(ProductionBooking productionBooking, ReverseProductionBooking booking, ReverseProductionBookingData reverseProductionBookingData) throws DataNotFoundException, InventoryUpdateException, SumoException;

    public FixedAssetDayCloseResponseObject getAssetsToDayCloseToday(Integer unitId);

    public Map<String, List<ApprovalDetail>> checkForAssetApprovals(HttpServletRequest request,Integer unitId) throws SumoException;

    public Boolean processApprovalRequest(Integer assetId, Integer userId, Boolean response) throws SumoException;

    public VarianceEdit getCafeVarianceEditProducts(Integer unitId);

    SCMDayCloseEventData getLastSuccessfulDayCloseEventData(Integer unitId);

    VarianceEdit submitCafeVarianceEdit(VarianceEdit varianceEdit) throws SumoException;

    List<WastageEvent> addReverseWastageEvent(ProductionBooking productionBooking, ReverseProductionBooking booking) throws DataNotFoundException, InventoryUpdateException, SumoException;

    List<CostDetail> fetchUnitProductInventory(Integer unitId, String keyType);

    public void getMissingPrices() throws DataNotFoundException, EmailGenerationException;

    List<Integer> getRecipeProductIds(RecipeDetail recipe);

    public Set<Integer> getUnitsForVarianceAcknowledge(Integer userId, String acknowledgementType) throws Exception;

    public List<VarianceAcknowledgementDetail> getAcknowledgeVarianceData(String acknowledgementType, Integer unitId, String isAcknowledged, Integer userId) throws Exception;

    public boolean acknowledgeVariance(Integer id,Integer userId, String comment) throws Exception;

    public List<VarianceModal> getVarianceDetails(Integer unitId, Date businessDate , String stockTakeType, Integer scmDayCloseEventId) throws Exception;

    public void setWeeklyVarianceAcknowledgementData() throws Exception;

    public VarianceAcknowledgementCheck checkVarianceAcknowledgement(Integer unitId);

    List<VarianceAcknowledgementDetail> getAcknowledgeVarianceData(String acknowledgementType, Integer unitId, Date date) throws Exception;

    List<ApprovalDetail> getFoundAssetApprovals(Integer unitId) throws SumoException;

    List<Integer> getUnitsForStockTakeThroughApp();

    SCMDayCloseEventData getLatestKettleDayClose(Integer unitId);

    StockTakeSumoDayCloseEventDTO checkForStockTakeSumoDayClose(Integer unitId, Set<Integer> productIds, String status) throws SumoException;

    StockTakeSumoDayCloseEventDTO startStockTakeSumoDayClose(StockTakeSumoDayCloseEventDTO stockTakeSumoDayCloseEventDTO) throws SumoException;

    boolean updateSumoDayCloseDeviceInfo(String deviceInfo, Integer eventId) throws SumoException;

    boolean updateStockTakeSumoDayCloseEvent(Integer eventId, String newStatus) throws SumoException;

    boolean saveSubmitDayCloseProducts(List<StockTakeSumoDayCloseProductsDTO> productList, Integer eventId, boolean isSubmit, String deviceInfo) throws SumoException;

    void settleStockTakeSumoDayCloseEvent(Integer stockTakeSumoDayCloseEventId, SCMDayCloseEventData stockEvent);

    void cancelPendingStockTakeEventsOfApp();

    void generateNextWeeklyMonthlyEvent(StockInventoryData inventoryData, StockTakeType stockType, Date currentBusinessDate);

    void cancelStockTakeEvent(Integer unit, Date currentBusinessDate);

    Map<Integer, BigDecimal> getScmInventoryOfKeys(Integer unitId, List<Integer> keyIds, PriceUpdateEntryType keyType, boolean fetchOnlyLatest);

    <T extends ConsumptionVO> void verifyInventoryForKeys(T rec, boolean ignoreSemiFinished) throws SumoException;
}
