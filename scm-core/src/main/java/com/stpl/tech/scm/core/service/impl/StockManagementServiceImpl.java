/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.core.service.impl;

import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.stpl.tech.kettle.core.data.budget.vo.BudgetDetail;
import com.stpl.tech.kettle.core.data.budget.vo.ConsumablesAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.InventoryAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.WastageAggregate;
import com.stpl.tech.kettle.core.notification.ErrorNotification;
import com.stpl.tech.master.core.data.vo.ScmMissingPriceResponse;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.WebServiceCallException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.external.inventory.service.InventoryService;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.State;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.domain.model.UnitPartnerStatusVO;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.inventory.model.InventoryAction;
import com.stpl.tech.master.inventory.model.InventorySource;
import com.stpl.tech.master.inventory.model.QuantityResponseData;
import com.stpl.tech.master.recipe.model.IngredientProduct;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariant;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.master.tax.model.TaxData;
import com.stpl.tech.scm.core.ProductStockForUnitComparator;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.ThresholdType;
import com.stpl.tech.scm.core.annotation.LogExecutionTime;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.NegativeStockException;
import com.stpl.tech.scm.core.exception.SCMError;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.mapper.DomainDataMapper;
import com.stpl.tech.scm.core.service.AbstractStockManagementService;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.GoodsReceiveManagementService;
import com.stpl.tech.scm.core.service.ProductionBookingService;
import com.stpl.tech.scm.core.service.ReferenceOrderManagementService;
import com.stpl.tech.scm.core.service.SCMAssetManagementService;
import com.stpl.tech.scm.core.service.SCMNotificationService;
import com.stpl.tech.scm.core.service.SCMProductManagementService;
import com.stpl.tech.scm.core.service.SCMReportingService;
import com.stpl.tech.scm.core.service.StockManagementService;
import com.stpl.tech.scm.core.service.TransferOrderManagementService;
import com.stpl.tech.scm.core.service.WarehouseStockManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.webservice.InventoryServiceClientEndpoints;
import com.stpl.tech.scm.core.util.webservice.KettleServiceClientEndpoints;
import com.stpl.tech.scm.core.util.webservice.MasterServiceClientEndpoints;
import com.stpl.tech.scm.core.util.webservice.WebServiceHelper;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.DayCloseEventDao;
import com.stpl.tech.scm.data.dao.GoodsReceiveManagementDao;
import com.stpl.tech.scm.data.dao.PaymentRequestManagementDao;
import com.stpl.tech.scm.data.dao.PriceManagementDao;
import com.stpl.tech.scm.data.dao.SCMAssetManagementDao;
import com.stpl.tech.scm.data.dao.SCMProductManagementDao;
import com.stpl.tech.scm.data.dao.StockManagementDao;
import com.stpl.tech.scm.data.model.ApprovalDetailData;
import com.stpl.tech.scm.data.model.AssetDefinitionData;
import com.stpl.tech.scm.data.model.AssetDefinitionDataLog;
import com.stpl.tech.scm.data.model.AssetRecoveryData;
import com.stpl.tech.scm.data.model.AssetRecoveryDefinitionData;
import com.stpl.tech.scm.data.model.CostDetailData;
import com.stpl.tech.scm.data.model.DayCloseCostDetailDataCafeDump;
import com.stpl.tech.scm.data.model.DayCloseFrequencyMismatchProducts;
import com.stpl.tech.scm.data.model.DayCloseProductPackagingMappings;
import com.stpl.tech.scm.data.model.GoodsReceivedData;
import com.stpl.tech.scm.data.model.GoodsReceivedItemData;
import com.stpl.tech.scm.data.model.PaymentRequestData;
import com.stpl.tech.scm.data.model.RegularOrderUnitBrandData;
import com.stpl.tech.scm.data.model.RegularOrderingEvent;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.data.model.RequestOrderItemData;
import com.stpl.tech.scm.data.model.ReverseBookingConsumptionData;
import com.stpl.tech.scm.data.model.ReverseProductionBookingData;
import com.stpl.tech.scm.data.model.SCMDayCloseEventData;
import com.stpl.tech.scm.data.model.SCMDayCloseEventRangeData;
import com.stpl.tech.scm.data.model.SCMProductConsumptionData;
import com.stpl.tech.scm.data.model.SCMProductConsumptionDrillDown;
import com.stpl.tech.scm.data.model.SCMProductInventoryData;
import com.stpl.tech.scm.data.model.SCMWastageData;
import com.stpl.tech.scm.data.model.SCMWastageEventData;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.StockEntryEventData;
import com.stpl.tech.scm.data.model.StockEventAssetMappingDefinitionData;
import com.stpl.tech.scm.data.model.StockEventCalendarData;
import com.stpl.tech.scm.data.model.StockEventDefinitionData;
import com.stpl.tech.scm.data.model.StockTakeSumoDayCloseEvent;
import com.stpl.tech.scm.data.model.StockTakeSumoDayCloseProducts;
import com.stpl.tech.scm.data.model.TransferOrderItemData;
import com.stpl.tech.scm.data.model.UnitDayCloseDayMappingData;
import com.stpl.tech.scm.data.model.UnitDetailData;
import com.stpl.tech.scm.data.model.UnitOrderScheduleData;
import com.stpl.tech.scm.data.model.VarianceAcknowledgementData;
import com.stpl.tech.scm.data.model.VarianceEditAggregatedData;
import com.stpl.tech.scm.data.model.VarianceExpiryDrillDownData;
import com.stpl.tech.scm.data.model.WastageDataDrilldown;
import com.stpl.tech.scm.domain.model.ApprovalDetail;
import com.stpl.tech.scm.domain.model.ApprovalStatus;
import com.stpl.tech.scm.domain.model.ApprovalType;
import com.stpl.tech.scm.domain.model.AssetDefinition;
import com.stpl.tech.scm.domain.model.AssetStatusType;
import com.stpl.tech.scm.domain.model.BookingConsumption;
import com.stpl.tech.scm.domain.model.Consumable;
import com.stpl.tech.scm.domain.model.ConsumptionData;
import com.stpl.tech.scm.domain.model.CostDetail;
import com.stpl.tech.scm.domain.model.DayCloseEvent;
import com.stpl.tech.scm.domain.model.DayCloseFrequencyMismatch;
import com.stpl.tech.scm.domain.model.DayCloseProductPackagingMappingsDTO;
import com.stpl.tech.scm.domain.model.DayWiseExpiryProduct;
import com.stpl.tech.scm.domain.model.ExpiryDataRequest;
import com.stpl.tech.scm.domain.model.FixedAssetCompactDefinition;
import com.stpl.tech.scm.domain.model.FixedAssetDayCloseResponseObject;
import com.stpl.tech.scm.domain.model.GoodsReceived;
import com.stpl.tech.scm.domain.model.GoodsReceivedItem;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.InventoryItemDrilldown;
import com.stpl.tech.scm.domain.model.LostAssetEmailObject;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.PendingMilkBread;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.ProductBasicDetail;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductPackagingMapping;
import com.stpl.tech.scm.domain.model.ProductStatus;
import com.stpl.tech.scm.domain.model.ProductStockForUnit;
import com.stpl.tech.scm.domain.model.ProductType;
import com.stpl.tech.scm.domain.model.ProductionBooking;
import com.stpl.tech.scm.domain.model.RegularOrderEvent;
import com.stpl.tech.scm.domain.model.ReverseProductionBooking;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.StockCalendarEventCheckVO;
import com.stpl.tech.scm.domain.model.StockCalendarEventStatus;
import com.stpl.tech.scm.domain.model.StockEventStatus;
import com.stpl.tech.scm.domain.model.StockEventStatusType;
import com.stpl.tech.scm.domain.model.StockEventType;
import com.stpl.tech.scm.domain.model.StockInventoryData;
import com.stpl.tech.scm.domain.model.StockTakeFrequencyEnum;
import com.stpl.tech.scm.domain.model.StockTakeSubType;
import com.stpl.tech.scm.domain.model.StockTakeSumoDayCloseEventDTO;
import com.stpl.tech.scm.domain.model.StockTakeSumoDayCloseProductsDTO;
import com.stpl.tech.scm.domain.model.StockTakeSumoDayCloseStatus;
import com.stpl.tech.scm.domain.model.StockTakeType;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.scm.domain.model.VarianceAcknowledgementCheck;
import com.stpl.tech.scm.domain.model.VarianceAcknowledgementDetail;
import com.stpl.tech.scm.domain.model.VarianceEdit;
import com.stpl.tech.scm.domain.model.VarianceEditItem;
import com.stpl.tech.scm.domain.model.VarianceVO;
import com.stpl.tech.scm.domain.model.WastageAggregatedData;
import com.stpl.tech.scm.domain.model.WastageData;
import com.stpl.tech.scm.domain.model.WastageEvent;
import com.stpl.tech.scm.domain.vo.ConsumptionVO;
import com.stpl.tech.scm.notification.email.ScmMissingPricesNotification;
import com.stpl.tech.scm.notification.email.template.ScmMissingPricesNotificationTemplate;
import com.stpl.tech.scm.reports.VarianceSummaryReportNotification;
import com.stpl.tech.scm.reports.VarianceSummaryUtil;
import com.stpl.tech.scm.reports.modal.VarianceModal;
import com.stpl.tech.scm.reports.modal.VarianceSummaryModal;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.domain.adapter.DateDeserializer2;
import com.stpl.tech.util.endpoint.Endpoints;
import com.stpl.tech.util.notification.AttachmentData;
import com.stpl.tech.util.notification.model.ResponseData;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.stpl.tech.scm.data.converter.SCMDataConverter.convert;

@Service
public class StockManagementServiceImpl extends AbstractStockManagementService implements StockManagementService {

    private static final Logger LOG = org.slf4j.LoggerFactory.getLogger(StockManagementServiceImpl.class);
    private final WorkbookContextFactory ctxFactory = WorkbookContextFactory.useXlsx();

    @Autowired
    private StockManagementDao stockManagementDao;

    @Autowired
    private SCMProductManagementService productService;

    @Autowired
    private SCMProductManagementDao productManagementDao;

    @Autowired
    private PriceManagementDao priceManagementDao;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private SCMReportingService reportingService;

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private SCMNotificationService notificationService;

    @Autowired
    private GoodsReceiveManagementService goodsReceiveManagementService;

    @Autowired
    private GoodsReceiveManagementDao goodsReceiveManagementDao;

    @Autowired
    private PaymentRequestManagementDao paymentRequestManagementDao;

    @Autowired
    private EnvProperties props;

    @Autowired
    private DayCloseEventDao dayCloseEventDao;

    @Autowired
    private SCMAssetManagementService scmAssetManagementService;

    @Autowired
    private SCMNotificationService scmNotificationService;

    @Autowired
    private TaxDataCache taxCache;

    @Autowired
    private ProductionBookingService productionBookingService;

    @Autowired
    private WarehouseStockManagementService warehouseStockManagementService;

    @Autowired
    private ReferenceOrderManagementService referenceOrderManagementService;

    @Autowired
    private SCMProductManagementService scmProductManagementService;

    @Autowired
    private TransferOrderManagementService transferOrderManagementService;

    @Autowired
    private Environment env;

    @Autowired
    private SCMAssetManagementDao scmAssetManagementDao;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<WastageEvent> addWastageEvent(List<WastageEvent> wastageEvent) throws InventoryUpdateException, DataNotFoundException {
        return addWastage(wastageEvent);
    }

    @Deprecated
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public synchronized int removeDuplicateKey() {
        return stockManagementDao.removeDuplicateKey();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Integer> fetchDuplicateKey(Boolean isCafe) {
        if(Boolean.TRUE.equals(isCafe)){
            return stockManagementDao.fetchDuplicateKeyCafe();
        }else{
            return stockManagementDao.fetchDuplicateKeyWh();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public int updateDuplicateKeyLatestFlag(List<Integer> ids , Boolean isCafe ) {
    	return stockManagementDao.updateDuplicateKeyLatestFlag(ids,isCafe);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<WastageEvent> getWastageEventsForDates(String startDate, String endDate, Integer unitId) {
        List<SCMWastageEventData> wastageEventDataList = stockManagementDao.getAllWastageEventsForDates(startDate,endDate,unitId);
        List<WastageEvent> wastageEvents = new ArrayList<>();
        if (wastageEventDataList != null) {
            for (SCMWastageEventData wastage : wastageEventDataList) {
                String empName = masterDataCache.getEmployee(wastage.getGeneratedBy());
                wastageEvents.add(SCMDataConverter.convert(wastage, scmCache, empName));
            }
        }
        return wastageEvents;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<WastageAggregatedData> getWastageAggregatedData(List<WastageData> wastageDataList, Boolean isKitchenOrWH) {
        Map<String,WastageAggregatedData> wastageAggregatedDataMap = new HashMap<>();
        if (isKitchenOrWH) {
            for (WastageData data : wastageDataList) {
                if (wastageAggregatedDataMap.containsKey(data.getSku().getSkuName())) {
                    WastageAggregatedData aggregatedData = wastageAggregatedDataMap.get(data.getSku().getSkuName());
                    aggregatedData.setTotalQuantity(aggregatedData.getTotalQuantity().add(data.getQuantity()));
                    aggregatedData.setTotalCost(aggregatedData.getTotalCost().add(data.getTotalAmount()));
                    aggregatedData.setWastageEvents(aggregatedData.getWastageEvents() + 1);
                    wastageAggregatedDataMap.put(data.getSku().getSkuName(),aggregatedData);
                }
                else {
                    WastageAggregatedData aggregatedData = new WastageAggregatedData();
                    aggregatedData.setSkuName(data.getSku().getSkuName());
                    aggregatedData.setSubCategory(data.getSubCategory());
                    aggregatedData.setUom(data.getUom());
                    aggregatedData.setTotalQuantity(data.getQuantity());
                    aggregatedData.setTotalCost(data.getTotalAmount());
                    aggregatedData.setWastageEvents(1);
                    wastageAggregatedDataMap.put(data.getSku().getSkuName(),aggregatedData);
                }
            }
        }
        else {
            for (WastageData data : wastageDataList) {
                if (wastageAggregatedDataMap.containsKey(data.getProduct().getProductName())) {
                    WastageAggregatedData aggregatedData = wastageAggregatedDataMap.get(data.getProduct().getProductName());
                    aggregatedData.setTotalQuantity(aggregatedData.getTotalQuantity().add(data.getQuantity()));
                    aggregatedData.setTotalCost(aggregatedData.getTotalCost().add(data.getTotalAmount()));
                    aggregatedData.setWastageEvents(aggregatedData.getWastageEvents() + 1);
                    wastageAggregatedDataMap.put(data.getProduct().getProductName(),aggregatedData);
                }
                else {
                    WastageAggregatedData aggregatedData = new WastageAggregatedData();
                    aggregatedData.setProductName(data.getProduct().getProductName());
                    aggregatedData.setSubCategory(data.getSubCategory());
                    aggregatedData.setUom(data.getUom());
                    aggregatedData.setTotalQuantity(data.getQuantity());
                    aggregatedData.setTotalCost(data.getTotalAmount());
                    aggregatedData.setWastageEvents(1);
                    wastageAggregatedDataMap.put(data.getProduct().getProductName(),aggregatedData);
                }
            }
        }
        return new ArrayList<>(wastageAggregatedDataMap.values());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public DayWiseExpiryProduct getDayWiseExpiryProduct(Integer unitId, List<String> dates, Boolean isAggregated, Date firstDate) throws SumoException {
        List<Date> datesList = new ArrayList<>();
        dates.forEach(e -> datesList.add(AppUtils.getDate(e,"yyyy-MM-dd")));
        Collections.sort(datesList);
        DayWiseExpiryProduct result = new DayWiseExpiryProduct();
        try {
            //setting Expiry Products - inStock
            Map<Integer, Map<String ,Float>> inStock = getInStockProducts(unitId,dates,datesList.get(datesList.size() - 1), datesList.get(0));
            //setting in transit Products - inTransit
//            Map<Integer, Map<String ,Float>> inTransit = getInTransitProducts(unitId,datesList,datesList.get(datesList.size() - 1), datesList.get(0), dates);
            // New Logic For In Transit Stock Making Expiry based On the Prduct Shelf Life
            Map<Integer, Map<String ,Float>> inTransit = getInTransitProductsNew(unitId,datesList,datesList.get(datesList.size() - 1), datesList.get(0), dates);
            //setting in transit Products - Acknowledge RO
            Map<String, Map<Integer, Map<String, Float>>> acknowledgedRo = getAcknowledgedRoProducts(unitId,dates,datesList.get(datesList.size() - 1),firstDate);
            //setting total map
            Map<Integer, Map<String ,Float>> totalStock = getTotalStock(inStock,inTransit);

            result.setInStock(inStock);
            result.setInTransit(inTransit);
            result.setAcknowledgedRoInTransit(acknowledgedRo);
            result.setTotalStock(totalStock);
            LOG.info("For Unit : {} day wise expires are : {} and dates are : {}",masterDataCache.getUnit(unitId).getName(), new Gson().toJson(result),new Gson().toJson(datesList));
        }
        catch (SumoException e) {
            throw e;
        }
        catch (Exception e) {
            LOG.error("Exception Occurred While getting Day wise Expiry data ::: ",e);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void insertMonthlyDumpData() {
        LOG.info("Started Inserting the Cost detail dump data at : {}",AppUtils.getCurrentTimestamp());
        stockManagementDao.insertCostDetailDumpData();
        LOG.info("Started Inserting the Transfer Order Item In transit dump data at : {}",AppUtils.getCurrentTimestamp());
        stockManagementDao.insertToItemInTransitDumpData();
    }

    @Override
    @Transactional(noRollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String, Map<Integer, Map<String, Float>>> getAcknowledgedRoProducts(Integer unitId, List<String> dates, Date lastDate, Date firstOrderingDate) {
        Map<String, Map<Integer, Map<String, Float>>> result = new HashMap<>();
        try {
            LOG.info("Creating Acknowledged RO product map ..!");
            List<RequestOrderData> acknowledgedROProducts = stockManagementDao.getAcknowledgedROProducts(unitId, firstOrderingDate, lastDate);
            String lastDayType = AppUtils.getFormattedTime(lastDate, "yyyy-MM-dd");
            for (RequestOrderData requestOrderData : acknowledgedROProducts) {
                Map<Integer, Map<String, Float>> productMap = new HashMap<>();
                for (RequestOrderItemData itemData : requestOrderData.getRequestOrderItemDatas()) {
                    String dayTypeToSearch = lastDayType;
                    ProductDefinition productDefinition = scmCache.getProductDefinition(itemData.getProductId());
                    if (Objects.nonNull(productDefinition)) {
                        Unit fulfilmentUnit = masterDataCache.getUnit(requestOrderData.getFulfillmentUnitId());
                        if (fulfilmentUnit.getFamily().equals(UnitCategory.KITCHEN) || fulfilmentUnit.getFamily().equals(UnitCategory.WAREHOUSE)) {
                            int shelfLife = productDefinition.getShelfLifeInDays();
                            Date dateAfterShelfLife = AppUtils.getDateAfterDays(requestOrderData.getFulfillmentDate(), shelfLife >= 1 ? shelfLife - 1 : shelfLife);
                            String dayType = AppUtils.getFormattedTime(dateAfterShelfLife, "yyyy-MM-dd");
                            if (lastDate.compareTo(dateAfterShelfLife) < 0) {
                                dayType = AppUtils.getFormattedTime(lastDate,"yyyy-MM-dd");
                            }
                            dayTypeToSearch = shelfLife < 0 ? lastDayType : dayType;
                        }
                        if (fulfilmentUnit.getFamily().equals(UnitCategory.CAFE)) {
                            dayTypeToSearch = AppUtils.getFormattedTime(requestOrderData.getFulfillmentDate(), "yyyy-MM-dd");
                        }
                    }
                    if (!dates.contains(dayTypeToSearch)) {
                        LOG.info("Not found while searching in the date range - ACKNOWLEDGED RO : date is : {} product id : {} and qty is : {} and RO Id is : {}",dayTypeToSearch, itemData.getProductId(),
                                itemData.getRequestedAbsoluteQuantity(), requestOrderData.getId());
                        dayTypeToSearch = AppUtils.getFormattedTime(lastDate, "yyyy-MM-dd");
                    }
                    if (productMap.containsKey(itemData.getProductId())) {
                        Map<String, Float> innerMap = productMap.get(itemData.getProductId());
                        if (innerMap.containsKey(dayTypeToSearch)) {
                            innerMap.put(dayTypeToSearch, innerMap.get(dayTypeToSearch) + itemData.getRequestedAbsoluteQuantity().floatValue());
                            productMap.put(itemData.getProductId(), innerMap);
                        } else {
                            innerMap.put(dayTypeToSearch, itemData.getRequestedAbsoluteQuantity().floatValue());
                            productMap.put(itemData.getProductId(), innerMap);
                        }
                    } else {
                        Map<String, Float> innerMap = new HashMap<>();
                        innerMap.put(dayTypeToSearch, itemData.getRequestedAbsoluteQuantity().floatValue());
                        productMap.put(itemData.getProductId(), innerMap);
                    }
                }
                String fulfilmentDate = AppUtils.getFormattedTime(requestOrderData.getFulfillmentDate(), "yyyy-MM-dd");
                if (result.containsKey(fulfilmentDate)) {
                    Map<Integer, Map<String, Float>> innerProductMap = result.get(fulfilmentDate);
                    for (Map.Entry<Integer, Map<String, Float>> mapEntry : productMap.entrySet()) {
                        if (innerProductMap.containsKey(mapEntry.getKey())) {
                            Map<String, Float> productDayWiseStock = innerProductMap.get(mapEntry.getKey());
                            for (Map.Entry<String, Float> productDay : mapEntry.getValue().entrySet()) {
                                if (productDayWiseStock.containsKey(productDay.getKey())) {
                                    productDayWiseStock.put(productDay.getKey(), productDayWiseStock.get(productDay.getKey()) + productDay.getValue());
                                } else {
                                    productDayWiseStock.put(productDay.getKey(), productDay.getValue());
                                }
                            }
                            innerProductMap.put(mapEntry.getKey(), productDayWiseStock);
                        } else {
                            innerProductMap.put(mapEntry.getKey(), mapEntry.getValue());
                        }
                    }
                    result.put(fulfilmentDate, innerProductMap);
                } else {
                    result.put(fulfilmentDate, productMap);
                }
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While getting In Transit Products(Acknowleged RO Products ) ::: ", e);
        }
        return result;
    }

    @Override
    @Transactional(noRollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.NEVER)
    public ExpiryDataRequest getTransferOrderItemExpiries(ExpiryDataRequest expiryDataRequest) throws InventoryUpdateException, DataNotFoundException {
        try {
            return transferOrderManagementService.getTransferOrderItemExpiries(expiryDataRequest);
        }
        catch (Exception e) {
            LOG.error("Exception Occurred While getting TO items Expiries :: ",e);
            return null;
        }
    }

    private List<GoodsReceivedItem> convertIntoGRItem(List<RequestOrderItemData> finalRoItemData, Map<Integer, List<SkuDefinition>> productSkuMap) {
        List<GoodsReceivedItem> result = new ArrayList<>();
        for (RequestOrderItemData itemData : finalRoItemData) {
            GoodsReceivedItem goodsReceivedItem = new GoodsReceivedItem();
            if (productSkuMap.containsKey(itemData.getProductId()) && productSkuMap.get(itemData.getProductId()).size() > 0) {
                goodsReceivedItem.setProductId(itemData.getProductId());
                goodsReceivedItem.setSkuId(productSkuMap.get(itemData.getProductId()).get(0).getSkuId());
                goodsReceivedItem.setTransferredQuantity(itemData.getRequestedAbsoluteQuantity().floatValue());
                result.add(goodsReceivedItem);
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, Map<String, Float>> getTotalStock(Map<Integer, Map<String, Float>> inStock, Map<Integer, Map<String, Float>> inTransit) {
        Map<Integer, Map<String, Float>> result = new HashMap<>();
        try {
            LOG.info("Creating Total Stock product map ..!");
            Set<Integer> productIds = new HashSet<>();
            Set<String > dates = new HashSet<>();
            productIds.addAll(inStock.keySet());
            productIds.addAll(inTransit.keySet());
            for (Integer productId : productIds) {
                Map<String, Float> resultInnerMap = new HashMap<>();
                if (inStock.containsKey(productId)) {
                    Map<String, Float> innerMap = inStock.get(productId);
                    for (String date : inStock.get(productId).keySet()) {
                        if (resultInnerMap.containsKey(date)) {
                            resultInnerMap.put(date,resultInnerMap.get(date) + innerMap.get(date));
                        }
                        else {
                            resultInnerMap.put(date,innerMap.get(date));
                        }
                    }
                }
                if (inTransit.containsKey(productId)) {
                    Map<String, Float> innerMap = inTransit.get(productId);
                    for (String date : inTransit.get(productId).keySet()) {
                        if (resultInnerMap.containsKey(date)) {
                            resultInnerMap.put(date,resultInnerMap.get(date) + innerMap.get(date));
                        }
                        else {
                            resultInnerMap.put(date,innerMap.get(date));
                        }
                    }
                }
                result.put(productId,resultInnerMap);
            }
        }
        catch (Exception e) {
            LOG.error("Exception Occurred While getting In Stock Products(Expiry Products ) ::: ",e);
        }
        return result;
    }

    @Override
    public Map<Integer, Map<String, Float>> getInTransitProducts(Integer unitId, List<Date> dates, Date lastDate, Date firstDate, List<String> stringOfDates) {
        Map<Integer, Map<String, Float>> result = new HashMap<>();
        try {
            LOG.info("Creating In transit product map ..!");
            List<GoodsReceived> goodsReceived = goodsReceiveManagementService.getPendingGrsWithGrItems(unitId);
            for (GoodsReceived received : goodsReceived) {
                GoodsReceivedData goodsReceivedData = stockManagementDao.find(GoodsReceivedData.class, received.getId());
                goodsReceivedData = goodsReceiveManagementService.getOriginalGrData(goodsReceivedData);
                if (Objects.nonNull(goodsReceivedData.getRequestOrderData())) {
                    List<RequestOrderData> requestOrderDataList = stockManagementDao.getRosFromRoId(goodsReceivedData.getRequestOrderData().getId());
                    List<RequestOrderData> orderData = new ArrayList<>();
                    orderData.add(goodsReceivedData.getRequestOrderData());
                    List<RequestOrderData> finalROData = requestOrderDataList.isEmpty() ? orderData : requestOrderDataList;
                    Map<Integer, Map<String,Float>> expiries = getGrItemsExpiries(received,lastDate,firstDate, goodsReceivedData, stringOfDates);
                    LOG.info("Expiries are : {}",new Gson().toJson(expiries));
                    for (RequestOrderData roData : finalROData) {
                        if ((Objects.nonNull(roData.getIsSpecialOrder()) && roData.getIsSpecialOrder().equalsIgnoreCase(AppConstants.NO))
                        && Objects.nonNull(roData.getBulkOrder()) && roData.getBulkOrder().equalsIgnoreCase(AppConstants.NO) && Objects.nonNull(roData.getAssetOrder()) && roData.getAssetOrder().equalsIgnoreCase(AppConstants.NO)) {
                            LOG.info("GR id is : {} and RO Id is : {}",goodsReceivedData.getId(),roData.getId());
                            Map<Integer, Float> scmRoQuantity = getScmROMap(roData.getRequestOrderItemDatas());
                            LOG.info("Scm Ro Quantity is : {}",new Gson().toJson(scmRoQuantity));
                            for (Map.Entry<Integer, Float> entry : scmRoQuantity.entrySet()) {
                                Float quantity = entry.getValue();
                                Map<String, Float> productExpiries = expiries.getOrDefault(entry.getKey(),null);
                                if (Objects.nonNull(productExpiries)) {
                                    for (Date date : dates) {
                                        String dateString = AppUtils.getDateString(date, "yyyy-MM-dd");
                                        Float mapQuantity = productExpiries.getOrDefault(dateString, null);
                                        if (quantity > 0 ) {
                                            if (Objects.nonNull(mapQuantity)) {
                                                LOG.info("Current date is : {} and quantity is : {} and map quantity is : {}",dateString,quantity,mapQuantity);
                                                if (quantity >= mapQuantity) {
                                                    productExpiries.put(dateString, (float) 0);
                                                    if (result.containsKey(entry.getKey())) {
                                                        Map<String, Float> innerMap = result.get(entry.getKey());
                                                        if (innerMap.containsKey(dateString)) {
                                                            innerMap.put(dateString, innerMap.get(dateString) + mapQuantity);
                                                            result.put(entry.getKey(), innerMap);
                                                        } else {
                                                            innerMap.put(dateString, mapQuantity);
                                                            result.put(entry.getKey(), innerMap);
                                                        }
                                                    } else {
                                                        Map<String, Float> innerMap = new HashMap<>();
                                                        innerMap.put(dateString, mapQuantity);
                                                        result.put(entry.getKey(), innerMap);
                                                    }
                                                    quantity = quantity - mapQuantity;
                                                } else {
                                                    productExpiries.put(dateString, mapQuantity - quantity);
                                                    if (result.containsKey(entry.getKey())) {
                                                        Map<String, Float> innerMap = result.get(entry.getKey());
                                                        if (innerMap.containsKey(dateString)) {
                                                            innerMap.put(dateString, innerMap.get(dateString) + quantity);
                                                            result.put(entry.getKey(), innerMap);
                                                        } else {
                                                            innerMap.put(dateString, quantity);
                                                            result.put(entry.getKey(), innerMap);
                                                        }
                                                    } else {
                                                        Map<String, Float> innerMap = new HashMap<>();
                                                        innerMap.put(dateString, quantity);
                                                        result.put(entry.getKey(), innerMap);
                                                    }
                                                    quantity = (float) 0;
                                                }
                                            }
                                        } else {
                                            break;
                                        }
                                    }
                                    expiries.put(entry.getKey(),productExpiries);
                                }
                                scmRoQuantity.put(entry.getKey(),quantity);
                            }
                        }
                    }
                }
            }
        }
        catch (Exception e) {
            LOG.error("Exception Occurred While getting In Transit Products Stock(Expiry Products ) ::: ",e);
        }
        return result;
    }

    private Map<Integer, Map<String, Float>> getInTransitProductsNew(Integer unitId, List<Date> datesList, Date lastDate, Date firstDate, List<String> stringOfDates) {
        Map<Integer, Map<String, Float>> result = new HashMap<>();
        try {
            LOG.info("Creating In transit product map ..!");
            List<GoodsReceived> goodsReceived = goodsReceiveManagementService.getPendingGrsWithGrItems(unitId);
            for (GoodsReceived received : goodsReceived) {
                for (GoodsReceivedItem goodsReceivedItem : received.getGoodsReceivedItems()) {
                    for (InventoryItemDrilldown drilldown : goodsReceivedItem.getDrillDowns()) {
                        Date expiryDate = SCMUtil.getScmBusinessDate(drilldown.getExpiryDate());
                        String dayType = AppUtils.getFormattedTime(SCMUtil.getScmBusinessDate(drilldown.getExpiryDate()), "yyyy-MM-dd");
                        if (lastDate.compareTo(expiryDate) < 0) {
                            dayType = AppUtils.getFormattedTime(lastDate, "yyyy-MM-dd");
                        }
                        if (expiryDate.compareTo(firstDate) < 0) {
                            LOG.info("In IN_TRANSIT for Product Id : {} And Quantity : {} Expiry date is : {} and First Ordering Date is : {}",
                                    goodsReceivedItem.getProductId(), drilldown.getQuantity(), drilldown.getExpiryDate(), firstDate);
                            dayType = AppUtils.getFormattedTime(lastDate, "yyyy-MM-dd");

                            ProductDefinition productDefinition = scmCache.getProductDefinition(goodsReceivedItem.getProductId());
                            if (Objects.nonNull(productDefinition)) {
                                Unit fulfilmentUnit = masterDataCache.getUnit(received.getGenerationUnitId().getId());
                                if (fulfilmentUnit.getFamily().equals(UnitCategory.KITCHEN) || fulfilmentUnit.getFamily().equals(UnitCategory.WAREHOUSE)) {
                                    int shelfLife = productDefinition.getShelfLifeInDays();
                                        Date dateAfterShelfLife = AppUtils.getDateAfterDays(firstDate, shelfLife >= 1 ? shelfLife - 1 : shelfLife);
                                        String dayTypeAfterShelfLife = AppUtils.getFormattedTime(dateAfterShelfLife, "yyyy-MM-dd");
                                        if (lastDate.compareTo(dateAfterShelfLife) < 0) {
                                            dayTypeAfterShelfLife = AppUtils.getFormattedTime(lastDate, "yyyy-MM-dd");
                                        }
                                        dayType = shelfLife < 0 ? dayType : dayTypeAfterShelfLife;
                                    }
                                if (fulfilmentUnit.getFamily().equals(UnitCategory.CAFE)) {
                                    dayType = AppUtils.getFormattedTime(firstDate, "yyyy-MM-dd");
                                }
                            }
                        }
                        if (!stringOfDates.contains(dayType)) {
                            LOG.info("Not found while searching in the date range - IN TRANSIT :  date is : {} product id : {} and qty is : {} and GR Id is : {}", dayType, goodsReceivedItem.getProductId(),
                                    goodsReceivedItem.getTransferredQuantity(), received.getId());
                            dayType = AppUtils.getFormattedTime(firstDate, "yyyy-MM-dd");
                        }

                        if (result.containsKey(goodsReceivedItem.getProductId())) {
                            Map<String, Float> innerMap = result.get(goodsReceivedItem.getProductId());
                            if (innerMap.containsKey(dayType)) {
                                innerMap.put(dayType, innerMap.get(dayType) + drilldown.getQuantity().floatValue());
                                result.put(goodsReceivedItem.getProductId(), innerMap);
                            } else {
                                innerMap.put(dayType, drilldown.getQuantity().floatValue());
                                result.put(goodsReceivedItem.getProductId(), innerMap);
                            }
                        } else {
                            Map<String, Float> innerMap = new HashMap<>();
                            innerMap.put(dayType, drilldown.getQuantity().floatValue());
                            result.put(goodsReceivedItem.getProductId(), innerMap);
                        }
                    }
                }
            }
        }
        catch (Exception e) {
            LOG.error("Exception Occurred While getting In Transit Products Stock(Expiry Products ) ::: ",e);
        }
        return result;
    }

    private Map<Integer, Float> getScmROMap(List<RequestOrderItemData> requestOrderItemData) {
        Map<Integer, Float> result = new HashMap<>();
        try {
            for (RequestOrderItemData itemData : requestOrderItemData) {
                Float qty = itemData.getRequestedQuantity().floatValue();
                if (result.containsKey(itemData.getProductId())) {
                    qty = qty + result.get(itemData.getProductId());
                }
                result.put(itemData.getProductId(),qty);
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred while getting Scm RO map ::: ",e);
        }
        return result;
    }

    private Map<Integer, Map<String, Float>> getGrItemsExpiries(GoodsReceived received, Date lastDate, Date firstDate, GoodsReceivedData origanlGr, List<String> stringOfDates) {
        Map<Integer, Map<String, Float>> result = new HashMap<>();
        try {
            for (GoodsReceivedItem goodsReceivedItem : received.getGoodsReceivedItems()) {
                for (InventoryItemDrilldown drilldown : goodsReceivedItem.getDrillDowns()) {
                    Date expiryDate = SCMUtil.getScmBusinessDate(drilldown.getExpiryDate());
                    String dayType = AppUtils.getFormattedTime(SCMUtil.getScmBusinessDate(drilldown.getExpiryDate()), "yyyy-MM-dd");
                    if (lastDate.compareTo(expiryDate) < 0) {
                        dayType = AppUtils.getFormattedTime(lastDate, "yyyy-MM-dd");
                    }
                    if (expiryDate.compareTo(firstDate) < 0) {
                        LOG.info("In IN_TRANSIT for Product Id : {} And Quantity : {} Expiry date is : {} and First Ordering Date is : {}",
                                goodsReceivedItem.getProductId(), drilldown.getQuantity(), drilldown.getExpiryDate(), firstDate);
                        dayType = AppUtils.getFormattedTime(firstDate, "yyyy-MM-dd");

                    ProductDefinition productDefinition = scmCache.getProductDefinition(goodsReceivedItem.getProductId());
                    if (Objects.nonNull(productDefinition)) {
                        Unit fulfilmentUnit = masterDataCache.getUnit(received.getGenerationUnitId().getId());
                        if (fulfilmentUnit.getFamily().equals(UnitCategory.KITCHEN) || fulfilmentUnit.getFamily().equals(UnitCategory.WAREHOUSE)) {
                            int shelfLife = productDefinition.getShelfLifeInDays();
                            if (Objects.nonNull(origanlGr.getRequestOrderData())) {
                                Date dateAfterShelfLife = AppUtils.getDateAfterDays(origanlGr.getRequestOrderData().getFulfillmentDate(), shelfLife);
                                String dayTypeAfterShelfLife = AppUtils.getFormattedTime(dateAfterShelfLife, "yyyy-MM-dd");
                                if (lastDate.compareTo(dateAfterShelfLife) < 0) {
                                    dayTypeAfterShelfLife = AppUtils.getFormattedTime(lastDate, "yyyy-MM-dd");
                                }
                                dayType = shelfLife < 0 ? dayType : dayTypeAfterShelfLife;
                            }
                        }
                        if (fulfilmentUnit.getFamily().equals(UnitCategory.CAFE) && Objects.nonNull(origanlGr.getRequestOrderData())) {
                            dayType = AppUtils.getFormattedTime(origanlGr.getRequestOrderData().getFulfillmentDate(), "yyyy-MM-dd");
                        }
                    }
                }
                    if (!stringOfDates.contains(dayType)) {
                        LOG.info("Not found while searching in the date range - IN TRANSIT :  date is : {} product id : {} and qty is : {} and GR Id is : {}",dayType, goodsReceivedItem.getProductId(),
                                goodsReceivedItem.getTransferredQuantity(), received.getId());
                        continue;
                    }

                    if (result.containsKey(goodsReceivedItem.getProductId())) {
                        Map<String, Float> innerMap = result.get(goodsReceivedItem.getProductId());
                        if (innerMap.containsKey(dayType)) {
                            innerMap.put(dayType, innerMap.get(dayType) + drilldown.getQuantity().floatValue());
                            result.put(goodsReceivedItem.getProductId(), innerMap);
                        } else {
                            innerMap.put(dayType, drilldown.getQuantity().floatValue());
                            result.put(goodsReceivedItem.getProductId(), innerMap);
                        }
                    } else {
                        Map<String, Float> innerMap = new HashMap<>();
                        innerMap.put(dayType, drilldown.getQuantity().floatValue());
                        result.put(goodsReceivedItem.getProductId(), innerMap);
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("Error Occurred while getting the GR items expiries(In transit Stock) ::: ",e);
        }
        return result;
    }

    @Override
    public Map<Integer, Map<String, Float>> getInStockProducts(Integer unitId, List<String> dates, Date lastDate, Date firstDate) throws SumoException {
        Map<Integer, Map<String, Float>> result = new HashMap<>();
        List<String> exceptionProducts = new ArrayList<>();
        try {
            LOG.info("Creating expiry product map ..!");
            LOG.info("## Trying to get SCM Products Stock from Cost Detail Data ##");
            List<CostDetail> costDetails = fetchUnitProductInventory(unitId, PriceUpdateEntryType.PRODUCT.name());
            for (CostDetail detail : costDetails) {
                String dayType = AppUtils.getFormattedTime(SCMUtil.getScmBusinessDate(detail.getExpiryDate()), "yyyy-MM-dd");
                if (lastDate.compareTo(SCMUtil.getScmBusinessDate(detail.getExpiryDate())) < 0) {
                    dayType = AppUtils.getFormattedTime(lastDate, "yyyy-MM-dd");
                }
                if (SCMUtil.getScmBusinessDate(detail.getExpiryDate()).compareTo(firstDate) < 0 && detail.getQuantity().compareTo(BigDecimal.ZERO) != 0) {
                    LOG.info("In IN_STOCK for Product Id : {} And Quantity : {} Expiry date is : {} and First Ordering Date is : {}",
                            detail.getKeyId(), scmCache.getProductDefinition(detail.getKeyId()).getProductName(), detail.getExpiryDate(), firstDate);
                    String exceptionMessage = scmCache.getProductDefinition(detail.getKeyId()).getProductName() + "_" + detail.getKeyId() + " [ " + detail.getExpiryDate() + " - " + detail.getQuantity() + " ]";
                    exceptionProducts.add(exceptionMessage);
                }
                if (!dates.contains(dayType)) {
                    LOG.info("Day type is not in between the Dates IN-stock Inventory ..!");
                    continue;
                }
                if (result.containsKey(detail.getKeyId())) {
                    Map<String, Float> innerMap = result.get(detail.getKeyId());
                    if (innerMap.containsKey(dayType)) {
                        innerMap.put(dayType, innerMap.get(dayType) + detail.getQuantity().floatValue());
                    } else {
                        innerMap.put(dayType, detail.getQuantity().floatValue());
                    }
                    result.put(detail.getKeyId(), innerMap);
                } else {
                    Map<String, Float> innerMap = new HashMap<>();
                    innerMap.put(dayType, detail.getQuantity().floatValue());
                    result.put(detail.getKeyId(), innerMap);
                }
            }
            if (!exceptionProducts.isEmpty()) {
                try {
                    String finalMessage = "Error in In Stock : Found Some Products with Previous Expiry Dates :" + masterDataCache.getUnit(unitId).getName() + "\n " + Arrays.toString(exceptionProducts.toArray());
                    if (finalMessage.length() > 4096) {
                        finalMessage = finalMessage.substring(0, 4096);
                    }
                    SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "SUMO", null,
                            SlackNotification.F9_NOTIFICATION.getChannel(props.getEnvType()), finalMessage);
                } catch (Exception e) {
                    LOG.error("Exception Occurred While Sending In Stock Error Notification during F9 ::: ", e);
                }
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While getting In Stock Products(Expiry Products ) ::: ", e);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<WastageEvent> verifyPriceData(List<WastageEvent> wastageEvent) throws InventoryUpdateException {
        return verifyPrices(wastageEvent);
    }

    @Override
    public StockManagementDao getDao() {
        return stockManagementDao;
    }

    @Override
    public SCMCache getScmCache() {
        return scmCache;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<WastageEvent> getWastageEvents(int unitId, Date businessDate) {
        List<SCMWastageEventData> wastageEventDataList = stockManagementDao.getAllWastageEventsForUnit(unitId,
                businessDate);
        List<WastageEvent> wastageEvents = new ArrayList<>();
        if (wastageEventDataList != null) {
            for (SCMWastageEventData wastage : wastageEventDataList) {
                String empName = masterDataCache.getEmployee(wastage.getGeneratedBy());
                wastageEvents.add(SCMDataConverter.convert(wastage, scmCache, empName));
            }
        }
        return wastageEvents;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean cancelWastageEvent(Integer wastageId) {
        Boolean flag = false;
        SCMWastageEventData wastageEventData = stockManagementDao.find(SCMWastageEventData.class, wastageId);
        StockEventType stockEventType = StockEventType.STOCK_TAKE;
        if (!masterDataCache.getUnit(wastageEventData.getUnitId()).getFamily().value().equalsIgnoreCase(UnitCategory.CAFE.value())) {
            stockEventType = StockEventType.WH_CLOSING;
        }
        SCMDayCloseEventData dayCloseEventUnit = dayCloseEventDao.getDayCloseDataOfUnit(wastageEventData.getUnitId(), stockEventType);
        //Date wastageBusinessDate = SCMUtil.getDate(wastageEventData.getBusinessDate());
        try {
            if (wastageEventData != null) {
                //Date currentBusinessDate = SCMUtil.getCurrentBusinessDate();
				/* if (wastageBusinessDate.equals(currentBusinessDate)
						&& !checkInventoryUpdated(wastageEventData.getUnitId(), currentBusinessDate)) */
                if (wastageEventData.getGenerationTime().after(dayCloseEventUnit.getGenerationTime())) {
                    wastageEventData.setStatus(StockEventStatus.CANCELLED.toString());
                    stockManagementDao.update(wastageEventData, true);
                    String empName = masterDataCache.getEmployee(wastageEventData.getGeneratedBy());
                    WastageEvent wastage = SCMDataConverter.convert(wastageEventData, scmCache, empName);
                    boolean hasSavedDrilldown = updateWastageItemDrillDowns(wastageEventData, wastage);
                    cancelBookings(wastage);
                    getPriceDao().addReceiving(wastage, true);
                    publishSCMWastage(InventoryAction.ADD, wastageEventData.getUnitId(), wastageEventData);
                    flag = true;
                }
            }
        } catch (Exception e) {
            LOG.error("Exception occurred while updating wastage event ", e);
        }
        return flag;
    }

    private void cancelBookings(WastageEvent wastage) throws DataNotFoundException, InventoryUpdateException {
        for (WastageData wastageData : wastage.getItems()) {
            if (wastageData.getProductionId() != null) {
                productionBookingService.cancelBookings(wastageData.getProductionId(), wastage.getGeneratedBy());
            }
        }
    }

    private boolean updateWastageItemDrillDowns(SCMWastageEventData wastageEventData, WastageEvent wastage) {
        LOG.info("updateWastageItemDrillDowns : wastage id : " + wastageEventData.getWastageId());
        HashMap<Integer, WastageData> itemMap = new HashMap<>();
        for (WastageData item : wastage.getItems()) {
            itemMap.put(item.getId(), item);
        }
        for (SCMWastageData wastageData : wastageEventData.getItems()) {
            List<InventoryItemDrilldown> ddList = new ArrayList<>();
            for (WastageDataDrilldown dd : wastageData.getItems()) {
                ddList.add(SCMDataConverter.convert(dd));
            }
            if (ddList.size() == 0) {
                return false;
            }
//            itemMap.get(wastageData.getWastageItemId()).getDrillDowns().addAll(ddList);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean generateOpeningEvent(UnitDetailData unitDetailData) throws NegativeStockException, SumoException {
        StockInventoryData openingValues = getExpectedValues(unitDetailData.getUnitId(),
                SCMServiceConstants.SYSTEM_USER, StockTakeType.ALL, StockEventType.OPENING,null);
        int returnResult = updateInventoryForUnit(openingValues, StockTakeType.MONTHLY,
                SCMUtil.getCurrentBusinessDate(), false);
        return returnResult != 0;
    }

    @Override
    public List<ProductDefinition> getAllProductsByFrequency(StockTakeType stockingFrequency) {
        List<ProductDefinition> returnList = productService.viewAllProductsForCafeInventory();
        if (stockingFrequency != null) {
            switch (stockingFrequency) {
                case DAILY:
                case WEEKLY:
                case MONTHLY:
                    returnList = returnList.stream().filter(
                                    productDefinition -> (!productDefinition.getStockKeepingFrequency().equals(StockTakeType.ALL) && !productDefinition.getStockKeepingFrequency().equals(StockTakeType.FIXED_ASSETS)))
                            .collect(Collectors.toList());
                    break;
                case FIXED_ASSETS:
                    returnList = returnList.stream().filter(productDefinition -> productDefinition
                            .getStockKeepingFrequency().equals(StockTakeType.FIXED_ASSETS)).collect(Collectors.toList());
                    break;
                case ALL:
                default:
                    break;
            }
        }

        returnList = returnList.stream().sorted((productDefinition1, productDefinition2) -> productDefinition1
                .getProductName().compareToIgnoreCase(productDefinition2.getProductName()))
                .collect(Collectors.toList());

        return returnList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean checkOpeningRequired(int unitId) {
        return stockManagementDao.getOpeningEventForUnit(unitId) == null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean cancelDayClose(int unitId, int closureId) {
        return stockManagementDao.cancelDayCloseForUnit(unitId, closureId);
    }


    private Boolean createMissingPriceEntries(ConsumptionData consumption) throws DataNotFoundException {

        List<Integer> keyIds = consumption.getConsumables().stream().map(consumable -> consumable.getProductId()).collect(Collectors.toList());
        GsonBuilder gsonBuilder = new GsonBuilder();
        Gson gson = gsonBuilder.create();
        String productIds = gson.toJson(keyIds);
        LOG.info("Creating Missing Price  Entries For Kettle Day Close for Product Ids ::::::: {}" ,productIds);
        getPriceDao().getOrCreateCurrentPrices(PriceUpdateEntryType.PRODUCT, consumption.getUnitId(), keyIds, false, true , true);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public ResponseData<ConsumptionData> initiateDayClose(ConsumptionData scmProductConsumption) throws Exception {
        SCMDayCloseEventData event = null;
        ResponseData<ConsumptionData> response = null;
        GsonBuilder gSonBuilder = new GsonBuilder().registerTypeAdapter(Date.class, new DateDeserializer2());
        Gson gson = gSonBuilder.create();
        String str = gson.toJson(scmProductConsumption);
        LOG.info("Consumption Data For Unit : {} ::::::::::::::: {}", scmProductConsumption.getUnitId(),str);
        try {
            Integer kettleClosureId = scmProductConsumption.getClosureId();
            int unitId = scmProductConsumption.getUnitId();
            Date businessDate = scmProductConsumption.getBusinessDate();
            boolean updateStatus = cancelAllPreviousEvents(unitId, businessDate);
            if (unitId != 0 && businessDate != null && updateStatus) {
                event = stockManagementDao.createConsumptionEvent(StockEventType.CLOSING,
                        scmProductConsumption.getUnitId(), StockTakeType.DAILY, StockEventStatus.INITIATED,
                        businessDate, kettleClosureId);

                stockManagementDao.createDayCloseEventRecord(event, scmProductConsumption.getStartOrderId(),
                        scmProductConsumption.getEndOrderId(), StockEventType.CONSUMPTION, StockTakeType.DAILY);
                if (!scmProductConsumption.getConsumables().isEmpty()) {
                    List<SCMProductConsumptionData> data = createConsumptionRecords(scmProductConsumption, event,
                            StockTakeType.DAILY);
                    if (data != null && data.size() > 0) {
                        ConsumptionData consumption = getConsumptionData(data, unitId, businessDate, null, null, new HashSet<>(), null);
                        createMissingPriceEntries(consumption);
                        ConsumptionData finalConsumption = getPriceDao().reduceConsumable(consumption, false);
                        setPrices(finalConsumption);
                        response = new ResponseData<>(true, "SUCCESS");
                        finalConsumption.setClosureId(kettleClosureId); // reset consumption id
                        response.setPayload(finalConsumption);
                    }
                } else {
                    // in case no COGS found to process for the day, send default object to run everything else smoothly
                    response = new ResponseData<>(true, "SUCCESS");
                    ConsumptionData finalConsumption = new ConsumptionData();
                    finalConsumption.setBusinessDate(businessDate);
                    finalConsumption.setUnitId(unitId);
                    finalConsumption.setStartOrderId(scmProductConsumption.getStartOrderId());
                    finalConsumption.setEndOrderId(scmProductConsumption.getEndOrderId());
                    finalConsumption.setClosureId(kettleClosureId);
                    response.setPayload(finalConsumption);
                }
            } else if (!updateStatus) {
                String message = "Failed to update previous day close events for kettle day close on unit :: " + unitId;
                LOG.error(message);
                notificationService.sendDayClosureAlarmNotification(unitId, message);
            }
        } catch (Exception e) {
            LOG.error("Got exception while processing the scm product consumption", e);
            String subject = "Failed to initiate day close for kettle day close on unit "
                    + scmProductConsumption.getUnitId();
            ErrorNotification error = new ErrorNotification(subject, e.getMessage(), e, props.getEnvType());
            if (scmProductConsumption != null) {
                updateEmailsAndSlack(error, scmProductConsumption.getUnitId());
            }
            error.sendEmail();
            throw e;
        }
        return response;
    }

    private void updateEmailsAndSlack(ErrorNotification error, int unitId) {
        List<String> emailList = new ArrayList<String>();
        List<String> slackList = new ArrayList<String>();
        Unit u = masterDataCache.getUnit(unitId);
        emailList.add(u.getUnitEmail());
        slackList.add(u.getChannel());
        addSlackAndEmail(u.getManagerId(), emailList, slackList);
        if (u.getCafeManager() != null) {
            addSlackAndEmail(u.getCafeManager().getId(), emailList, slackList);
        }
        error.setToEmailList(emailList);
    }

    private void addSlackAndEmail(Integer empId, List<String> emailList, List<String> slackList) {
        EmployeeBasicDetail e2 = masterDataCache.getEmployeeBasicDetail(empId);
        if (e2 != null && !AppUtils.isBlank(e2.getEmailId())) {
            emailList.add(e2.getEmailId());
            if (!AppUtils.isBlank(e2.getSlackChannel())) {
                slackList.add(e2.getSlackChannel());
            }
        }
    }

    private boolean cancelAllPreviousEvents(int unitId, Date businessDate) throws InventoryUpdateException {
        //stockManagementDao.cancelAllPreviousEvents(unitId, businessDate);
        SCMDayCloseEventData initiated = stockManagementDao.getInitiatedDayCloseEvents(unitId, businessDate);
        if (initiated != null) {
            List<SCMProductConsumptionData> consumptions = stockManagementDao.getConsumption(initiated.getEventId());
            if (consumptions != null && consumptions.size() > 0) {
                ConsumptionData consumption = getConsumptionData(consumptions, unitId, businessDate, null, null, new HashSet<>(), null);
                ConsumptionData finalConsumption = getPriceDao().addReceiving(consumption, true);
                if (finalConsumption != null) {
                    initiated.setStatus(StockEventStatus.CANCELLED.name());
                    stockManagementDao.update(initiated, true);
                    return true;
                }
            } else {
                // not able to find any consumption data for the event
                initiated.setStatus(StockEventStatus.CANCELLED.name());
                stockManagementDao.update(initiated, true);
                return true;
            }
            return false;
        } else {
            return true;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DayCloseEvent checkClosingInitiated(int unit) {
        try {
            LOG.info("Checking For Cafe Day Close Initiated Event : {}", unit);

            Date businessDate = SCMUtil.getPreviousBusinessDate();
            List<UnitHours> unitHours = getMasterDataCache().getOperationalHoursForUnit(unit);
            int retryCount =0;
            boolean retry = true;
            while (retry && retryCount<7) {
                try {
                    UnitHours hours = unitHours.get(AppUtils.getDayOfWeek(businessDate)-1);
                    if(Objects.nonNull(hours) && hours.isIsOperational()){
                        retry = false;
                    }else{
                        businessDate = AppUtils.getPreviousBusinessDate(businessDate);
                    }
                } catch (Exception e) {
                    businessDate = AppUtils.getPreviousBusinessDate(businessDate);
                }
                retryCount++;
            }

            LOG.info("Checking CLosing For Start Business Date ::::::: {}",businessDate);

            SCMDayCloseEventData scmDayCloseEventData  = stockManagementDao.checkLastClosingInitiated(unit,businessDate);
            if(Objects.nonNull(scmDayCloseEventData)){
                return SCMDataConverter.convert(scmDayCloseEventData,masterDataCache);
            }
            return null;
        } catch (Exception ex) {
            LOG.error("Error In Day Close for unit {} :::::::", unit, ex);
            return null;
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public ConsumptionData getTransfersForBusinessDate(int unitId, Date businessDate, Date lastBusinessDate, List<Integer> currentProductIds,
                                                       ConsumptionData availableConsumptionData, Set<Integer> excludeProductIds, Map<Integer, BigDecimal> varianceTillNow) {
        String[] statusList = new String[]{SCMOrderStatus.CREATED.toString(), SCMOrderStatus.TRANSFERRED.toString(),
                SCMOrderStatus.SETTLED.toString()};
        LOG.info("Started Getting the Transfers During Day Close For Unit Id  : {}" ,unitId);
        List<TransferOrderItemData> transferOrderItemDataList = stockManagementDao.getTransferList(unitId, businessDate,
                lastBusinessDate, Arrays.asList(statusList), true, currentProductIds);
        LOG.info("Completed Getting the Transfers During Day Close For Unit Id  : {}" ,unitId);
        if (transferOrderItemDataList != null && transferOrderItemDataList.size() > 0) {
            if (Objects.nonNull(availableConsumptionData)) {
                Map<Integer, Consumable> consumableMap = new HashMap<>();
                for (TransferOrderItemData orderItem : transferOrderItemDataList) {
                    int productId = getProductId(orderItem.getSkuId());
                    if (excludeProductIds.contains(productId)) {
                        addToConsumableMap(consumableMap, productId, orderItem.getTransferredQuantity(),
                                orderItem.getUnitOfMeasure(), orderItem.getCalculatedAmount(), varianceTillNow.getOrDefault(productId, BigDecimal.ZERO));
                    }
                }
                for (Map.Entry<Integer,Consumable> entry : consumableMap.entrySet()) {
                    DayCloseFrequencyMismatch dayCloseFrequencyMismatch = DayCloseFrequencyMismatch.builder().businessDate(availableConsumptionData.getBusinessDate())
                            .productId(entry.getKey()).unitId(availableConsumptionData.getUnitId()).startOrderId(transferOrderItemDataList.get(0).getTransferOrderData().getId())
                            .endOrderId(transferOrderItemDataList.get(transferOrderItemDataList.size() - 1).getTransferOrderData().getId()).build();
                    availableConsumptionData.getDayCloseFrequencyMismatchList().add(dayCloseFrequencyMismatch);
                }
                availableConsumptionData.getConsumables().addAll(consumableMap.values());
                return availableConsumptionData;
            } else {
                ConsumptionData consumptionData = new ConsumptionData();
                consumptionData.setBusinessDate(businessDate);
                consumptionData.setUnitId(unitId);
                consumptionData.setStartOrderId(transferOrderItemDataList.get(0).getTransferOrderData().getId());
                consumptionData.setEndOrderId(
                        transferOrderItemDataList.get(transferOrderItemDataList.size() - 1).getTransferOrderData().getId());
                Map<Integer, Consumable> consumableMap = new HashMap<>();
                for (TransferOrderItemData orderItem : transferOrderItemDataList) {
                    int productId = getProductId(orderItem.getSkuId());
                    if (!excludeProductIds.contains(productId)) {
                        addToConsumableMap(consumableMap, productId, orderItem.getTransferredQuantity(),
                                orderItem.getUnitOfMeasure(), orderItem.getCalculatedAmount(), varianceTillNow.getOrDefault(productId, BigDecimal.ZERO));
                    }
                }
                consumptionData.getConsumables().addAll(consumableMap.values());
                return consumptionData;
            }
        } else {
            if (Objects.nonNull(availableConsumptionData)) {
                return availableConsumptionData;
            }
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public ConsumptionData getGoodsReceivedForBusinessDate(int unitId, Date businessDate, Date lastBusinessDate, List<Integer> currentProductIds,
                                                           ConsumptionData availableConsumptionData, Set<Integer> excludeProductIds, Map<Integer, BigDecimal> varianceTillNow) {
        LOG.info("Started Getting the GR During Day Close For Unit Id  : {}" ,unitId);
        List<GoodsReceivedItemData> goodsReceivedItemDataList = stockManagementDao.getReceivingList(unitId,
                businessDate, lastBusinessDate, currentProductIds);
        LOG.info("Completed Getting the GR During Day Close For Unit Id  : {}" ,unitId);
        if (goodsReceivedItemDataList != null && goodsReceivedItemDataList.size() > 0) {
            if (Objects.nonNull(availableConsumptionData)) {
                Map<Integer, Consumable> consumableMap = new HashMap<>();
                for (GoodsReceivedItemData orderItem : goodsReceivedItemDataList) {
                    int productId = getProductId(orderItem.getSkuId());
                    if (excludeProductIds.contains(productId)) {
                        addToConsumableMap(consumableMap, productId, orderItem.getReceivedQuantity(),
                                orderItem.getUnitOfMeasure(), orderItem.getCalculatedAmount(), varianceTillNow.getOrDefault(productId, BigDecimal.ZERO));
                    }
                }
                for (Map.Entry<Integer,Consumable> entry : consumableMap.entrySet()) {
                    DayCloseFrequencyMismatch dayCloseFrequencyMismatch = DayCloseFrequencyMismatch.builder().businessDate(availableConsumptionData.getBusinessDate())
                            .productId(entry.getKey()).unitId(availableConsumptionData.getUnitId()).startOrderId(goodsReceivedItemDataList.get(0).getGoodsReceivedData().getId())
                            .endOrderId(goodsReceivedItemDataList.get(goodsReceivedItemDataList.size() - 1).getGoodsReceivedData().getId()).build();
                    availableConsumptionData.getDayCloseFrequencyMismatchList().add(dayCloseFrequencyMismatch);
                }
                availableConsumptionData.getConsumables().addAll(consumableMap.values());
                return availableConsumptionData;
            } else {
                ConsumptionData consumptionData = new ConsumptionData();
                consumptionData.setBusinessDate(businessDate);
                consumptionData.setUnitId(unitId);
                consumptionData.setStartOrderId(goodsReceivedItemDataList.get(0).getGoodsReceivedData().getId());
                consumptionData.setEndOrderId(
                        goodsReceivedItemDataList.get(goodsReceivedItemDataList.size() - 1).getGoodsReceivedData().getId());
                Map<Integer, Consumable> consumableMap = new HashMap<>();
                for (GoodsReceivedItemData orderItem : goodsReceivedItemDataList) {
                    int productId = getProductId(orderItem.getSkuId());
                    if (!excludeProductIds.contains(productId)) {
                        addToConsumableMap(consumableMap, productId, orderItem.getReceivedQuantity(),
                                orderItem.getUnitOfMeasure(), orderItem.getCalculatedAmount(), varianceTillNow.getOrDefault(productId, BigDecimal.ZERO));
                    }
                }
                consumptionData.getConsumables().addAll(consumableMap.values());
                return consumptionData;
            }
        } else {
            if (Objects.nonNull(availableConsumptionData)) {
                return availableConsumptionData;
            }
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public ConsumptionData getWastageEventsForBusinessDate(int unitId, Date businessDate, Date lastBusinessDate, List<Integer> currentProductIds,
                                                           ConsumptionData availableConsumptionData, Set<Integer> excludeProductIds, Map<Integer, BigDecimal> varianceTillNow) {
        LOG.info("Started Getting the Wastage During Day Close For Unit Id  : {}" ,unitId);
        List<SCMWastageData> wastageItems = stockManagementDao.getWastageEventList(unitId, businessDate,
                lastBusinessDate, currentProductIds);
        LOG.info("Completed Getting the Wastage During Day Close For Unit Id  : {}" ,unitId);
        if (wastageItems != null && wastageItems.size() > 0) {
            Map<Integer, BigDecimal> expiryWastageOfProducts = getExpiryWastageOfProducts(wastageItems);
            if (Objects.nonNull(availableConsumptionData)) {
                Map<Integer, Consumable> consumableMap = new HashMap<>();
                for (SCMWastageData wastage : wastageItems) {
                    int productId = wastage.getProduct().getProductId();
                    if (excludeProductIds.contains(productId)) {
                        addToConsumableMap(consumableMap, productId, wastage.getQuantity(),
                                wastage.getProduct().getUnitOfMeasure(), wastage.getCost(), varianceTillNow.getOrDefault(productId, BigDecimal.ZERO));
                    }
                }
                for (Map.Entry<Integer,Consumable> entry : consumableMap.entrySet()) {
                    DayCloseFrequencyMismatch dayCloseFrequencyMismatch = DayCloseFrequencyMismatch.builder().businessDate(availableConsumptionData.getBusinessDate())
                            .productId(entry.getKey()).unitId(availableConsumptionData.getUnitId()).startOrderId(wastageItems.get(0).getWastage().getWastageId())
                            .endOrderId(wastageItems.get(wastageItems.size() - 1).getWastage().getWastageId()).build();
                    availableConsumptionData.getDayCloseFrequencyMismatchList().add(dayCloseFrequencyMismatch);
                }
                availableConsumptionData.getConsumables().addAll(consumableMap.values());
                availableConsumptionData.getExpiredWastage().putAll(expiryWastageOfProducts);
                return availableConsumptionData;
            } else {
                ConsumptionData consumptionData = new ConsumptionData();
                Map<Integer, Consumable> consumableMap = new HashMap<>();
                consumptionData.setBusinessDate(businessDate);
                consumptionData.setUnitId(unitId);
                consumptionData.setStartOrderId(wastageItems.get(0).getWastage().getWastageId());
                consumptionData.setEndOrderId(wastageItems.get(wastageItems.size() - 1).getWastage().getWastageId());
                for (SCMWastageData wastage : wastageItems) {
                    int productId = wastage.getProduct().getProductId();
                    if (!excludeProductIds.contains(productId)) {
                        addToConsumableMap(consumableMap, productId, wastage.getQuantity(),
                                wastage.getProduct().getUnitOfMeasure(), wastage.getCost(), varianceTillNow.getOrDefault(productId, BigDecimal.ZERO));
                    }
                }
                consumptionData.getConsumables().addAll(consumableMap.values());
                consumptionData.getExpiredWastage().putAll(expiryWastageOfProducts);
                return consumptionData;
            }
        } else {
            if (Objects.nonNull(availableConsumptionData)) {
                return availableConsumptionData;
            }
            return null;
        }
    }

    private Map<Integer, BigDecimal> getExpiryWastageOfProducts(List<SCMWastageData> wastageItems) {
        Map<Integer, BigDecimal> result = new HashMap<>();
        for (SCMWastageData wastageData :  wastageItems) {
            if (Objects.nonNull(wastageData.getComment()) && wastageData.getComment().equalsIgnoreCase(SCMServiceConstants.EXPIRED)) {
                BigDecimal quantity = wastageData.getQuantity();
                if (result.containsKey(wastageData.getProduct().getProductId())) {
                    quantity = quantity.add(result.get(wastageData.getProduct().getProductId()));
                }
                result.put(wastageData.getProduct().getProductId(), quantity);
            }
        }
        return result;
    }

    /**
     * @param finalConsumption
     */
    private void setPrices(ConsumptionData finalConsumption) {
        if (finalConsumption != null) {
            for (Consumable c : finalConsumption.getConsumables()) {
                SCMProductConsumptionData p = stockManagementDao.find(SCMProductConsumptionData.class, c.getId());

                BigDecimal totalCost = BigDecimal.ZERO;
                BigDecimal totalQuantity = BigDecimal.ZERO;

                for (InventoryItemDrilldown idd : c.getDrillDowns()) {
                    totalCost = SCMUtil.add(totalCost, SCMUtil.multiplyWithScale10(idd.getPrice(), idd.getQuantity()));
                    totalQuantity = SCMUtil.add(totalQuantity, idd.getQuantity());
                }

                p.setConsumptionPrice(SCMUtil.divideWithScale10(totalCost, totalQuantity));
                p.setConsumptionCost(totalCost);
                p.setTaxableAmount(AppUtils.multiplyWithScale10(p.getTaxableConsumption(), p.getConsumptionPrice()));
                p.setTaxAmount(AppUtils.percentOfWithScale10(p.getTaxableAmount(), p.getTaxPercentage()));
            }
        }
    }

    private void addToConsumableMap(Map<Integer, Consumable> consumableMap, int productId, BigDecimal quantity,
                                    String uom, BigDecimal amount, BigDecimal varianceTillNow) {
        Consumable consumable = consumableMap.get(productId);
        if (consumable == null) {
            consumable = new Consumable();
            consumable.setQuantity(SCMUtil.convertToBigDecimal(quantity));
            consumable.setUom(uom);
            consumable.setProductId(productId);
            consumable.setCost(amount);
            consumable.setVarianceTillNow(varianceTillNow);
        } else {
            consumable.setQuantity(consumable.getQuantity().add(SCMUtil.convertToBigDecimal(quantity)));
            consumable.setCost(AppUtils.add(consumable.getCost(), amount));
        }
        consumableMap.put(productId, consumable);
    }

    private Map<Integer, ProductBasicDetail> getAllProductIdsByFrequency(StockTakeType frequency) {
        Map<Integer, ProductBasicDetail> products = getAllProductsByFrequency(frequency).stream()
                .collect(Collectors.toMap(productDefinition -> productDefinition.getProductId(),
                        productDefinition -> SCMDataConverter.convert(productDefinition)));
        return products;
    }

    @LogExecutionTime
    private List<Integer> settleConflictingGRs(Integer unitId) {
        List<Integer> grIdList = null;
        try {
            LOG.info("Settling Conflict GR for Unit {}", unitId);
            List<GoodsReceivedData> rejectedGRList = goodsReceiveManagementService.getRejectedPendingGrs(unitId);
            grIdList = new ArrayList<>();
            for (GoodsReceivedData goodsReceivedData : rejectedGRList) {
                GoodsReceived gr = goodsReceiveManagementService.getGoodsReceivedDetail(goodsReceivedData.getId());
                gr.setReceivedBy(SCMUtil.getSystemUser());
                goodsReceiveManagementService.settleGoodsReceivedDetail(gr);
                grIdList.add(goodsReceivedData.getId());
            }
        } catch (Exception e) {
            LOG.error("Exception occurred while settling rejected GRs for unitId {}", unitId, e);
            StringBuilder message = new StringBuilder("Exception occurred while settling rejected GRs for \n");
            message.append("Unit ID :: " + unitId + "\n");
            message.append("Encountered error ::::: " + e.getMessage());
            SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "SUMO",
                    SlackNotification.SYSTEM_ERRORS, message.toString());

        }
        return grIdList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void generateSummaryReport(Date businessDate) throws IOException, EmailGenerationException {
        WorkbookContext workbook = ctxFactory.createWorkbook();
        SheetContext sheet = workbook.createSheet("All Cafe Summary");

        VarianceSummaryUtil util = new VarianceSummaryUtil();
        for (UnitBasicDetail unit : masterDataCache.getAllUnits()) {
            if (SwitchStatus.ACTIVE.name().equals(unit.getStatus().name())
                    && (SCMUtil.isCafe(unit.getCategory()) || SCMUtil.isOffice(unit.getCategory()))) {
                SCMDayCloseEventData dayCloseEvent = stockManagementDao.getLastDayCloseEvent(unit.getId(),
                        StockEventType.STOCK_TAKE, false);
                if (dayCloseEvent != null && businessDate.equals(dayCloseEvent.getBusinessDate())) {
                    List<VarianceSummaryModal> list = stockManagementDao.getDailySummary(dayCloseEvent.getEventId());
                    util.writeSummaryView(unit.getName(), sheet, list);
                    sheet.nextRow().nextRow();
                }
            }
        }

        // TODO @Vivek attach PNL summary sheet here

        VarianceSummaryReportNotification reportNotification = new VarianceSummaryReportNotification(props.getEnvType(),
                businessDate);
        List<AttachmentData> attachments = new ArrayList<>();
        String filePath = props.getBasePath() + File.separator + "variance_reports" + File.separator;
        String fileName = StockEventType.STOCK_TAKE.name() + "_Variance_Report_"
                + SCMUtil.getCurrentTimeISTStringWithNoColons() + ".xls";
        File file = new File(filePath);
        if (!file.exists()) {
            file.mkdirs();
        }
        String path = SCMUtil.write(workbook.toNativeBytes(), filePath, "summary", fileName, LOG);
        try {
            AttachmentData reports = new AttachmentData(IOUtils.toByteArray(new FileInputStream(path)), fileName,
                    AppConstants.EXCEL_MIME_TYPE);
            attachments.add(reports);
            reportNotification.sendRawMail(attachments);
        } catch (Exception e) {
            StringBuilder message = new StringBuilder("Failed to send variance summary report mail \n");
            message.append("Business Date :: " + businessDate + "\n");
            SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "Kettle",
                    SlackNotification.VARIANCE_REPORT_FAILURES, message.toString());
            throw e;
        }

    }

    private boolean settleStockEvent(SCMDayCloseEventData stockEvent) throws InventoryUpdateException, SumoException, DataNotFoundException {
        if (stockEvent != null) {
            try {
                StockTakeType stockTakeType = StockTakeType.valueOf(stockEvent.getEventFrequencyType());
                //GETTING ALL PRODUCTS WHICH ARE AVAILABLE IN COST DETAIL DATA
                List<Integer> inventorySnapShotKeyIds = priceManagementDao.getAllAvailableKeyIds(stockEvent.getUnitId(), PriceUpdateEntryType.PRODUCT);
                Map<Integer, BigDecimal> variancePriceMap = new HashMap<>();
                if (stockTakeType.equals(StockTakeType.FIXED_ASSETS)) {
                    generateVariance(stockEvent, StockTakeType.FIXED_ASSETS, inventorySnapShotKeyIds, variancePriceMap);
                } else {
                    generateVariance(stockEvent, StockTakeType.DAILY, inventorySnapShotKeyIds, variancePriceMap);
                    if (stockTakeType.equals(StockTakeType.WEEKLY)) {
                        generateVariance(stockEvent, StockTakeType.WEEKLY, inventorySnapShotKeyIds, variancePriceMap);
                    }
                    if (stockTakeType.equals(StockTakeType.MONTHLY)) {
                        generateVariance(stockEvent, StockTakeType.MONTHLY, inventorySnapShotKeyIds, variancePriceMap);
                    }
                }
                return true;
            } catch (InventoryUpdateException | SumoException | DataNotFoundException e) {
                LOG.error("Exception Occurred While settleStockEvent ::: ", e);
                throw e;
            } catch (Exception e) {
                LOG.error("Exception Occurred While settleStockEvent ::: ", e);
                throw new SumoException("Exception Occurred while settleStockEvent", e.getMessage());
            }
        }
        return false;
    }

    private void generateVariance(SCMDayCloseEventData stockEvent, StockTakeType stockType, List<Integer> inventorySnapShotKeyIds, Map<Integer, BigDecimal> variancePriceMap)
            throws InventoryUpdateException, SumoException, DataNotFoundException {
        LOG.info("Generating Variance for unit {} and stockType {} for Unit Id : {}", stockEvent.getUnitId(), stockType, stockEvent.getUnitId());
        SCMDayCloseEventData lastDayCloseEvent = getLastDayCloseEvent(stockEvent.getUnitId(), stockType, StockEventType.STOCK_TAKE);
        Map<Integer, BigDecimal> lastInventory = stockManagementDao.getStockForUnit(lastDayCloseEvent.getUnitId(),
                lastDayCloseEvent.getEventId());
        Map<Integer, Pair<SCMDayCloseEventData, BigDecimal>> notInLastDayCloseProducts = new HashMap<>();
        Map<Integer, BigDecimal> varianceTillNowMap = stockManagementDao.getVarianceTillNow(lastDayCloseEvent, null);
        Map<Integer, BigDecimal> inventoryData = getInventoryData(stockEvent, stockType, inventorySnapShotKeyIds);
        List<Integer> inActiveProductsList = new ArrayList<>();
        Map<Integer, SCMProductConsumptionData> consumptionDataList = settleConsumption(stockEvent, stockType, notInLastDayCloseProducts, lastDayCloseEvent,
                varianceTillNowMap, lastInventory, inActiveProductsList, inventorySnapShotKeyIds);
        List<ProductStockForUnit> stockList = settleInventoryForUnit(stockEvent, stockType, consumptionDataList,
                inventoryData, notInLastDayCloseProducts, lastDayCloseEvent, varianceTillNowMap, lastInventory, inActiveProductsList);
        if (stockType.value().equalsIgnoreCase(stockEvent.getEventFrequencyType())) {
            setVarianceStatusOfEvent(stockList, stockEvent, stockType);
        }
        if (stockType.equals(StockTakeType.DAILY)) {
            Map<Integer, BigDecimal> latestPricesMap = new HashMap<>();
            setVarianceCosts(stockList, stockEvent.getUnitId(), latestPricesMap);
            settlePricing(stockList, stockEvent, stockType, latestPricesMap, variancePriceMap);
        } else {
            // updating the variance price and variance cost based on its Daily Variance Price
            LOG.info("Not Overriding for Unit : {} Event Id : {} and Frequency is : {}", stockEvent.getUnitId(), stockEvent.getEventId(), stockEvent.getEventFrequencyType());
            updatePrice(stockList, variancePriceMap, true);
        }
        if (Objects.nonNull(masterDataCache) && Objects.nonNull(masterDataCache.getUnit(stockEvent.getUnitId())) && stockType.value().equals(stockEvent.getEventFrequencyType()))  {
            String ackReq = null;
            ackReq = masterDataCache.getUnit(stockEvent.getUnitId()).getVarianceAcknowledgementRequired();
            if (Objects.nonNull(ackReq) && ackReq.equals(SCMUtil.YES)) {
                calculateAcknowledgementData(stockList, stockEvent, stockType);
            }
        }
        //updateTransfers(stockEvent, stockType);
    }

    private void setVarianceStatusOfEvent(List<ProductStockForUnit> stockList, SCMDayCloseEventData stockEvent, StockTakeType stockType) {
        try {
            Optional<ProductStockForUnit> productStockForUnit = stockList.stream().filter(e -> e.getVariance().compareTo(BigDecimal.ZERO) != 0).findAny();
            if (productStockForUnit.isEmpty()) {
                stockEvent.setVarianceStatus("NO_VARIANCE");
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred while setVarianceStatusOfEvent ::  ", e);
        }
    }

    @LogExecutionTime
    private void updateTransfers(SCMDayCloseEventData stockEvent, StockTakeType stockTakeType) {
        SCMDayCloseEventRangeData eventRangeData = stockManagementDao.getTransfersRangeData(stockEvent, stockTakeType);
        if (eventRangeData != null) {
            stockManagementDao.updateTransfers(stockEvent, eventRangeData.getStartId(), eventRangeData.getEndId(), stockTakeType);
        }
    }

    @LogExecutionTime
    private Map<Integer, BigDecimal> getInventoryData(SCMDayCloseEventData stockEvent, StockTakeType stockType, List<Integer> inventorySnapShotKeyIds) {
        Map<Integer, BigDecimal> stockList = stockManagementDao.getStockForUnit(stockEvent.getUnitId(),
                stockEvent.getEventId());

        return getTrimmedList(stockList, inventorySnapShotKeyIds);
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_COMMITTED)
    public List<Integer> getUnavailableProductIds(List<Integer> productIds, int unitId, Map<Integer, BigDecimal> productUnitPrices) {
        List<CostDetailData> inventory = getPriceDao().getCurrentPrices(PriceUpdateEntryType.PRODUCT,
                unitId, productIds, false);
        List<Integer> unavailableInventory = new ArrayList<>();
        if (inventory != null) {
            Map<Integer, BigDecimal> inventoryLookup = inventory.stream()
                    .collect(Collectors.groupingBy(CostDetailData::getKeyId,
                            Collectors.reducing(BigDecimal.ZERO, CostDetailData::getQuantity, SCMUtil::add)
                    ));
            for (CostDetailData costDetailData : inventory) {
                productUnitPrices.put(costDetailData.getKeyId(), costDetailData.getPrice());
            }
            for (Integer item : productIds) {
                BigDecimal quantity = inventoryLookup.get(item);
                if (quantity == null) {
                    unavailableInventory.add(item);
                }
            }
        }
        return unavailableInventory;
    }

    private Map<Integer, BigDecimal> getTrimmedList(Map<Integer, BigDecimal> stockList, List<Integer> productIds) {
        Map<Integer, BigDecimal> trimmedList = new HashMap<>();
        for (Integer productId : productIds) {
            trimmedList.put(productId, SCMUtil.convertToBigDecimal(stockList.get(productId)));
        }
        return trimmedList;
    }

    @LogExecutionTime
    private void setVarianceCosts(List<ProductStockForUnit> stockList, Integer unitId, Map<Integer, BigDecimal> latestPriceMap) throws InventoryUpdateException, SumoException {
        List<Integer> productIds = stockList.stream().map(ProductStockForUnit::getProductId)
                .collect(Collectors.toList());
        List<CostDetailData> currentPrices = getPriceDao().getCurrentPrices(PriceUpdateEntryType.PRODUCT, unitId,
                productIds, true);
        Map<Integer, CostDetailData> currentPriceMap = null;

        try {
            currentPriceMap = currentPrices.stream()
                    .collect(Collectors.toMap(CostDetailData::getKeyId, Function.identity()));
        } catch (Exception e) {
            try {
                currentPriceMap = goodsReceiveManagementService.getPriceDao().fixPricing(currentPrices).stream()
                        .collect(Collectors.toMap(CostDetailData::getKeyId, Function.identity()));
            } catch (Exception e1) {
                LOG.error("Error while fixing pricing :::: ", e);
                String message = "Error while fixing prices for duplicate entries for pricing";
                List<Integer> costIds = currentPrices.stream().mapToInt(CostDetailData::getCostDetailDataId).boxed()
                        .collect(Collectors.toList());
                message = message + "\n" + costIds;
                notificationService.sendDayClosureAlarmNotification(unitId, message);
            }
        }

        if (currentPriceMap != null) {
            for (ProductStockForUnit stockForUnit : stockList) {
                ProductDefinition productDefinition = scmCache.getProductDefinition(stockForUnit.getProductId());
                CostDetailData currentPrice = currentPriceMap.get(stockForUnit.getProductId());
                if (Objects.isNull(currentPrice)) {
                    throw new SumoException("Missing Price for PRODUCT " + stockForUnit.getProductId() ,
                            "No Price Found for PRODUCT " + stockForUnit.getProductId() + " [" + productDefinition.getProductName() + "]");
                }
                BigDecimal varianceCost = SCMUtil.multiplyWithScale10(currentPrice.getPrice(), stockForUnit.getVariance());
                stockForUnit.setUnitPrice(currentPrice.getPrice());
                stockForUnit.setVarianceCost(varianceCost);
                latestPriceMap.put(productDefinition.getProductId(), currentPrice.getPrice());
            }
        }
    }

    @LogExecutionTime
    private void settlePricing(List<ProductStockForUnit> stockList, SCMDayCloseEventData event, StockTakeType stockType, Map<Integer, BigDecimal> latestPricesMap,
                               Map<Integer, BigDecimal> variancePriceMap)
            throws InventoryUpdateException, DataNotFoundException, SumoException {
        LOG.info("Overriding the Inventory of Unit : {} Event : {}", event.getUnitId(), event.getEventId());
        VarianceVO negativeVariance = new VarianceVO(event.getEventId(), event.getUnitId(),
                PriceUpdateEntryType.PRODUCT);
        VarianceVO positiveVariance = new VarianceVO(event.getEventId(), event.getUnitId(),
                PriceUpdateEntryType.PRODUCT);
        VarianceVO all = new VarianceVO(event.getEventId(), event.getUnitId(), PriceUpdateEntryType.PRODUCT);
        List<VarianceExpiryDrillDownData> varianceExpiryDrillDownDataList = event.getVarianceExpiryDrillDownDataList();
        Map<Integer, VarianceExpiryDrillDownData> varianceExpiryDrillDownDataMap = new HashMap<>();
        if (Objects.nonNull(varianceExpiryDrillDownDataList) && !varianceExpiryDrillDownDataList.isEmpty()) {
            varianceExpiryDrillDownDataMap = varianceExpiryDrillDownDataList.stream().collect(Collectors.toMap(VarianceExpiryDrillDownData::getKeyId, Function.identity(),
                    (firstEntry, secondEntry) -> (firstEntry.getVarianceExpiryDrillDownDataId() > secondEntry.getVarianceExpiryDrillDownDataId()) ? firstEntry : secondEntry));
        }
        for (ProductStockForUnit stock : stockList) {
            int typeOfVariance = stock.getVariance().compareTo(BigDecimal.ZERO);
            if (typeOfVariance > 0) {
                positiveVariance.getStockList().add(stock);
            }
            if (typeOfVariance < 0) {
                if (SCMUtil.convertToBigDecimal(stock.getStockValue()).compareTo(BigDecimal.ZERO) > 0 && varianceExpiryDrillDownDataMap.containsKey(stock.getKeyId())) {
                    ProductDefinition productDefinition = scmCache.getProductDefinition(stock.getKeyId());
                    InventoryItemDrilldown drillDown = new InventoryItemDrilldown();
                    drillDown.setKeyId(stock.getKeyId());
                    drillDown.setKeyType(stock.getKeyType().name());
                    BigDecimal price = latestPricesMap.get(stock.getKeyId());
                    if (Objects.isNull(price)) {
                        throw new SumoException("Missing Price for PRODUCT " + stock.getKeyId() ,
                                "No Price Found for PRODUCT " + stock.getKeyId() + " [" + productDefinition.getProductName() + "]");
                    }
                    drillDown.setPrice(price);
                    BigDecimal quantity = BigDecimal.ZERO;
                    if (stock.getExpectedValue().compareTo(BigDecimal.ZERO) >= 0) {
                        quantity = SCMUtil.convertToBigDecimal(stock.getStockValue()).subtract(stock.getExpectedValue());
                    } else {
                        quantity = SCMUtil.convertToBigDecimal(stock.getStockValue());
                    }
                    drillDown.setQuantity(quantity);
                    drillDown.setExpiryDate(SCMUtil.formatExpiryDate(varianceExpiryDrillDownDataMap.get(stock.getKeyId()).getExpiryDate()));
                    stock.setDrillDowns(Collections.singletonList(drillDown));
                    stock.setPrice(price);
                    stock.setItemKeyType(StockEventType.VARIANCE);
                    stock.setItemKeyId(varianceExpiryDrillDownDataMap.get(stock.getKeyId()).getVarianceExpiryDrillDownDataId());
                }
                negativeVariance.getStockList().add(stock);
            }
            all.getStockList().add(stock);
        }
        getPriceDao().reduceConsumable(positiveVariance, false);
        getPriceDao().addReceiving(negativeVariance, false);
        updatePrice(all.getStockList(), variancePriceMap, false);
        getPriceDao().overrideInventory(all);
    }

    private void updatePrice(List<ProductStockForUnit> stockForUnitList, Map<Integer, BigDecimal> variancePriceMap, boolean setPricesFromVarianceMap) {
        List<Integer> inventoryIds = stockForUnitList.stream().mapToInt(ProductStockForUnit::getInventoryId).boxed().collect(Collectors.toList());
        List<SCMProductInventoryData> inventoryDataList = stockManagementDao.getScmProductInventoryDataListWithIds(inventoryIds);
        Map<Integer, SCMProductInventoryData> inventoryDataListMap = inventoryDataList.stream().collect(Collectors.toMap(SCMProductInventoryData::getStockingId, Function.identity()));
        List<SCMProductInventoryData> needToUpdateList = new ArrayList<>();
        if (!inventoryDataList.isEmpty()) {
            for (ProductStockForUnit stockForUnit : stockForUnitList) {
                SCMProductInventoryData inventoryData = inventoryDataListMap.getOrDefault(stockForUnit.getInventoryId(), stockManagementDao.find(SCMProductInventoryData.class,
                        stockForUnit.getInventoryId()));
                if (!setPricesFromVarianceMap) {
                    inventoryData.setVariancePrice(stockForUnit.getPrice());
                    variancePriceMap.put(stockForUnit.getKeyId(), stockForUnit.getPrice());
                    inventoryData.setVarianceCost(stockForUnit.getVarianceCost());
                    inventoryData.setOriginalVarianceCost(stockForUnit.getVarianceCost());
                    if (inventoryData.getTaxPercentage() != null) {
                        inventoryData.setVarianceTax(AppUtils.percentOfWithScale10(stockForUnit.getVarianceCost(),
                                inventoryData.getTaxPercentage()));
                        inventoryData.setOriginalVarianceTax(AppUtils.percentOfWithScale10(stockForUnit.getVarianceCost(),
                                inventoryData.getTaxPercentage()));
                    }
                } else {
                    inventoryData.setVariancePrice(SCMUtil.convertToBigDecimal(variancePriceMap.get(stockForUnit.getKeyId())));
                    BigDecimal varianceCost = SCMUtil.multiplyWithScale10(SCMUtil.convertToBigDecimal(variancePriceMap.get(stockForUnit.getKeyId())), stockForUnit.getVariance());
                    stockForUnit.setVarianceCost(varianceCost);
                    stockForUnit.setUnitPrice(SCMUtil.convertToBigDecimal(variancePriceMap.get(stockForUnit.getKeyId())));
                    inventoryData.setVarianceCost(varianceCost);
                    inventoryData.setOriginalVarianceCost(varianceCost);
                    if (inventoryData.getTaxPercentage() != null) {
                        inventoryData.setVarianceTax(AppUtils.percentOfWithScale10(varianceCost,
                                inventoryData.getTaxPercentage()));
                        inventoryData.setOriginalVarianceTax(AppUtils.percentOfWithScale10(varianceCost,
                                inventoryData.getTaxPercentage()));
                    }
                }
                needToUpdateList.add(inventoryData);
            }
        }
        if (!needToUpdateList.isEmpty()) {
            stockManagementDao.update(needToUpdateList, true);
        }
    }

    @LogExecutionTime
    private SCMDayCloseEventData settleInventoryBySystem(SCMDayCloseEventData dayCloseEvent)
            throws NegativeStockException, SumoException {
        LOG.info("Auto Settling Inventory By System for unit {} and dayCloseEvent {}", dayCloseEvent.getUnitId(),
                dayCloseEvent.getEventId());
        int unitId = dayCloseEvent.getUnitId();
        StockTakeType stockTakeType = StockTakeType.DAILY;
        int userId = SCMServiceConstants.SYSTEM_USER;
        StockInventoryData detail = getExpectedValues(unitId, userId, stockTakeType, StockEventType.CLOSING,null);
        if (updateInventoryForUnit(detail, stockTakeType, dayCloseEvent.getBusinessDate(), false) == 1) {
            List<String> stockTakeTypes = Arrays.asList(StockTakeType.DAILY.name(), StockTakeType.MONTHLY.name());
            return stockManagementDao.getStockEventForUnit(dayCloseEvent.getUnitId(), dayCloseEvent.getDayClosureId(),
                    stockTakeTypes);
        } else {
            LOG.info("Auto Settling Inventory By System for unit {} and dayCloseEvent {} returned NULL", dayCloseEvent.getUnitId(),
                    dayCloseEvent.getEventId());
            return null;
        }
    }

    @Override
    public boolean checkInventoryUpdated(StockInventoryData inventoryData, Date businessDate, StockTakeType stockType) throws NegativeStockException {
        List<ProductStockForUnit> negatives = validateItems(inventoryData.getInventoryResponse(), inventoryData.getUnit());
        if (negatives == null || negatives.isEmpty()) {
            return stockManagementDao.checkInventoryLoaded(inventoryData.getUnit(), businessDate, stockType);
        }
        NegativeStockException e = new NegativeStockException(
                "Product(s) not available for cafe!",
                "Please contact your Cafe Manager/Area Manager for resolution");
        e.setNegatives(negatives);
        throw e;
    }

    @Override
    public boolean validateCalendarEvent(StockInventoryData inventoryData, Date businessDate, StockTakeType stockType) throws InventoryUpdateException {
        List<StockEventCalendarData> data = null;
        if (StockTakeType.WEEKLY.equals(stockType)) {
            data = stockManagementDao.checkPendingCalendarEventByType(inventoryData.getUnit(), StockTakeType.MONTHLY, businessDate);
            if (data != null && !data.isEmpty()) {
                throw new InventoryUpdateException("Monthly stock take is pending. Please fill monthly inventory.");
            }
        }
        data = stockManagementDao.checkPendingCalendarEventByType(inventoryData.getUnit(), stockType, businessDate);
        if (data == null || data.isEmpty()) {
            throw new InventoryUpdateException("No pending " + stockType + " stock event available.");
        }
        return true;
    }

    private List<ProductStockForUnit> validateItems(List<ProductStockForUnit> products, Integer unit) {
        List<Integer> keyIds = products.stream()
                .filter(o -> o.getStockValue() != null)
                .map(ProductStockForUnit::getProductId).collect(Collectors.toList());
        List<CostDetailData> inventory = getPriceDao().getCurrentPrices(PriceUpdateEntryType.PRODUCT,
                unit, keyIds, false);
        List<ProductStockForUnit> unavailableInventory = new ArrayList<>();
        if (inventory != null) {
            Map<Integer, BigDecimal> inventoryLookup = inventory.stream()
                    .collect(Collectors.groupingBy(CostDetailData::getKeyId,
                            Collectors.reducing(BigDecimal.ZERO, CostDetailData::getQuantity, SCMUtil::add)
                    ));
            for (ProductStockForUnit item : products) {
                BigDecimal quantity = inventoryLookup.get(item.getKeyId());
                if (quantity == null) { // no price entry found for the keyId
                    unavailableInventory.add(item);
                }
            }
        }
        return unavailableInventory;
    }


    @Override
    public Boolean checkInventoryUpdated(Integer unit, Date businessDate) {
        return stockManagementDao.checkStockUpdated(unit,businessDate,null);
    }

    private SCMDayCloseEventData settleDailyAndMonthlyStock(SCMDayCloseEventData dayCloseEvent)
            throws NegativeStockException, InventoryUpdateException, SumoException, DataNotFoundException {
        List<String> stockTakeTypes = Arrays.asList(StockTakeType.DAILY.name(), StockTakeType.WEEKLY.name(),
                StockTakeType.MONTHLY.name());
        SCMDayCloseEventData stockEvent = stockManagementDao.getStockEventForUnit(dayCloseEvent.getUnitId(),
                dayCloseEvent.getDayClosureId(), stockTakeTypes);
        if (stockEvent == null) {
            stockEvent = settleInventoryBySystem(dayCloseEvent);
        }
        if (settleStockEvent(stockEvent)) {
            stockEvent.setStatus(StockEventStatus.CLOSED.toString());
            stockManagementDao.update(stockEvent, false);
            dayCloseEvent.setStatus(StockEventStatus.CLOSED.toString());
            stockManagementDao.update(dayCloseEvent, true);
        }
        return stockEvent;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean settleFixedAssetStockForUnit(Integer unit, Date businessDate)
            throws NegativeStockException, InventoryUpdateException, SumoException, DataNotFoundException {
        // settle FIXED_ASSETS Stock for the unit
        SCMDayCloseEventData stockEvent = settleFixedAssetStock(unit);
        // update pricing and delete obsolete values from Cost Detail for the unit
        getPriceDao().deleteObsoletePrices(unit, PriceUpdateEntryType.PRODUCT, stockEvent.getEventId());
        //check for ordering EVents
//        List<RegularOrderEvent> regularOrderEvents = warehouseStockManagementService.getRegularOrderingEvents(unit);
//        if (regularOrderEvents.size() == 0) {
//            reportingService.sendVarianceReport(businessDate, unit, true);
//        }
        // send variance report after completion of inventory settlement
        reportingService.sendVarianceReport(businessDate, unit, true, null);
        // publish to Inventory Service
        publishToLiveInventory(unit, stockEvent);
        // return true if everything goes well
        return true;
    }

    private void publishToLiveInventory(Integer unit, SCMDayCloseEventData stockEvent) {
        try {
            inventoryService.publishInventorySQSFifo(props.getEnvType().name(),
                    new QuantityResponseData(unit, null, InventoryAction.FLUSH, InventorySource.SUMO,
                            stockEvent.getEventId(), stockEvent.getGenerationTime()));
        } catch (Exception e) {
            LOG.info("Failed to publish SQS Flush Notification on day close ::: ", e);
        }
    }

    private SCMDayCloseEventData settleFixedAssetStock(Integer unitId)
            throws InventoryUpdateException, SumoException, DataNotFoundException {
        List<String> fixedAssetType = Arrays.asList(StockTakeType.FIXED_ASSETS.name());
        // get the FA stock take event for this unit for this business date
        SCMDayCloseEventData fixedAssetEvent = stockManagementDao.getStockEventForUnit(unitId, null, fixedAssetType);
        if (settleStockEvent(fixedAssetEvent)) {
            fixedAssetEvent.setStatus(StockEventStatus.CLOSED.toString());
            stockManagementDao.update(fixedAssetEvent, true);
        }
        return fixedAssetEvent;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean refreshKettleDayClose(Integer unitId) throws DataUpdationException, WebServiceCallException{
        try {
            LOG.info("Processing Product Consumption for Unit: {}", unitId);
            Boolean checkDayClose = callWebService(Boolean.class, props.getDayCloseURLinKettle(),
                    unitId);
            if (checkDayClose == null || !checkDayClose) {
                LOG.info("Error In Day Close for unit {}", unitId);
                return false;
            }
            return true;
        } catch (WebServiceCallException e) {
            throw new WebServiceCallException(e.getMessage());
        } catch (Exception ex) {
            LOG.error("Error In Day Close for unit {}", unitId, ex);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void cancelStockTakeEvent(Integer unit, Date currentBusinessDate) {
        try {
            SCMDayCloseEventData dayCloseEvent = stockManagementDao.getInitiatedDayCloseEvents(unit, currentBusinessDate);
            if (dayCloseEvent != null) {
                List<String> stockTakeTypes = Arrays.asList(StockTakeType.DAILY.name(), StockTakeType.WEEKLY.name(),
                        StockTakeType.MONTHLY.name());
                SCMDayCloseEventData stockEvent = stockManagementDao.getStockEventForUnit(dayCloseEvent.getUnitId(),
                        dayCloseEvent.getDayClosureId(), stockTakeTypes);
                if (Objects.nonNull(stockEvent)) {
                    stockEvent.setStatus(StockEventStatus.CANCELLED.name());
                    stockEvent.setUpdatedAt(AppUtils.getCurrentTimestamp());
                    stockEvent.setUpdatedBy(stockEvent.getCreatedBy());
                    stockManagementDao.update(stockEvent, true);
                }
                deleteNegativeVarianceExpiryData(dayCloseEvent, null);
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While Cancelling StockTakeEvent for unit {} :: ", unit, e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void deleteNegativeVarianceExpiryData(SCMDayCloseEventData dayCloseEvent, Integer eventId) {
        if (Objects.nonNull(eventId)) {
            dayCloseEvent = stockManagementDao.find(SCMDayCloseEventData.class, eventId);
        }
        LOG.info("::: Trying to delete negative variance List for Event Id ::: {}", dayCloseEvent.getEventId());
        stockManagementDao.deleteNegativeVarianceExpiryData(dayCloseEvent);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean settleDayCloseForUnit(Integer unit, Date businessDate, Integer stockTakeSumoDayCloseEventId)
            throws NegativeStockException, InventoryUpdateException, SumoException, DataNotFoundException {
        // fetch day close event generated from kettle
        try {
            long time = System.currentTimeMillis();
            SCMDayCloseEventData dayCloseEvent = stockManagementDao.getInitiatedDayCloseEvents(unit, businessDate);
            if (dayCloseEvent != null) {
                LOG.info("Day Close Event Found for unit {}, businessDate {}", unit, businessDate);
                // settle all conflicting GRs for the unit first
                //settleConflictingGRs(unit);
                // settle inventory for the unit at day close
                LOG.info(":: Started Settling the Day Close ::");
                SCMDayCloseEventData stockEvent = settleDailyAndMonthlyStock(dayCloseEvent);
                LOG.info("COMPLETED Settling the Stock Event ::: {}ms for Unit :: {}", System.currentTimeMillis() - time, unit);
                if (Objects.nonNull(stockTakeSumoDayCloseEventId)) {
                    settleStockTakeSumoDayCloseEvent(stockTakeSumoDayCloseEventId, stockEvent);
                }
                time = System.currentTimeMillis();
                // update pricing and delete obsolete values from Cost Detail for the unit
                getPriceDao().deleteObsoletePrices(unit, PriceUpdateEntryType.PRODUCT, stockEvent.getEventId());
                LOG.info("COMPLETED deleteObsoletePrices ::: {}ms for Unit {}", System.currentTimeMillis() - time, unit);
                time = System.currentTimeMillis();
                //generating Ordering Events
//            List<RegularOrderEvent> regularOrderEvents = generateOrderingEvents(stockEvent);
                // send variance report after completion of inventory settlement
//            if (regularOrderEvents.size() == 0) {
//                reportingService.sendVarianceReport(businessDate, unit, false);
//            }

                // Dumping Cost Detail Data And Updating Average Price
                dumpCostDetailDataAndUpdateAveragePrice(stockEvent);

                // getting Next Day's Expiry data (Getting Inventory and Checking Which is going to expire for next Business Date)
                Map<Integer, SCMProductInventoryData> dailyInventoryData = stockManagementDao.getScmProductInventoryDataListByFrequency(stockEvent, StockTakeType.DAILY.value())
                        .stream().collect(Collectors.toMap(SCMProductInventoryData::getProductId, Function.identity(), (existingValue, newValue) -> newValue));
                setNextBusinessDateExpectedExpiry(dayCloseEvent, stockEvent, dailyInventoryData);

                reportingService.sendVarianceReport(businessDate, unit, false, dailyInventoryData);
                LOG.info("COMPLETED sendVarianceReport ::: {}ms for Unit {}", System.currentTimeMillis() - time, unit);
                time = System.currentTimeMillis();
                // publish to live inventory service
                publishToLiveInventory(unit, stockEvent);
                LOG.info("COMPLETED publishToLiveInventory ::: {}ms for Unit {}", System.currentTimeMillis() - time, unit);
                // return true if everything goes well
                LOG.info("Settle Day Close Completed for unit {}, businessDate {}", unit, businessDate);
                //TODO DELETE COST DETAIL DATA ENTRIES DURING MONTHLY WHEN THERE IS NOT TRANSACTION AND 0 INVENTORY DURING THE MONTHLY.
                return true;
            }
            LOG.info("Day Close Event NOT Found for unit {}, businessDate {}", unit, businessDate);
            // return false if anything goes wrong
            return false;
        } catch (NegativeStockException | InventoryUpdateException | SumoException | DataNotFoundException e) {
            LOG.error("Exception Occurred While settleDayCloseForUnit ::: ", e);
            throw e;
        } catch (Exception e) {
            LOG.error("Exception Occurred While settleDayCloseForUnit ::: ", e);
            throw new SumoException("Exception Occurred While settleDayCloseForUnit", e.getMessage());
        }
    }

    private void dumpCostDetailDataAndUpdateAveragePrice(SCMDayCloseEventData stockEvent) {
        try {
            // GETTING CURRENT INVENTORY SNAP SHOT
            Map<Integer, List<CostDetail>> inventorySnapShotMap = fetchUnitProductInventory(stockEvent.getUnitId(), PriceUpdateEntryType.PRODUCT.name())
                    .stream().collect(Collectors.groupingBy(CostDetail::getKeyId));

            List<DayCloseCostDetailDataCafeDump> dayCloseCostDetailDataCafeDumpList = new ArrayList<>();
            Map<String, List<SCMProductInventoryData>> allInventoryDataOfCurrentDayCloseList  = stockManagementDao.getScmProductInventoryDataListByFrequency(stockEvent, null)
                    .stream().collect(Collectors.groupingBy(SCMProductInventoryData::getStockType));
            Map<Integer, BigDecimal> productAveragePriceMap = new HashMap<>();
            List<SCMProductInventoryData> needToUpdateList = new ArrayList<>();
            // First Updating Daily Data and setting the Average Price and Adding Data of Dump after that Updating Each Stock Type Data
            if (allInventoryDataOfCurrentDayCloseList.containsKey(StockTakeType.DAILY.value())) {
                for (SCMProductInventoryData scmProductInventoryData : allInventoryDataOfCurrentDayCloseList.get(StockTakeType.DAILY.value())) {
                    if (Objects.nonNull(inventorySnapShotMap.get(scmProductInventoryData.getProductId())) && !inventorySnapShotMap.get(scmProductInventoryData.getProductId()).isEmpty()) {
                        BigDecimal costOfInventory = BigDecimal.ZERO;
                        BigDecimal zeroQuantityCostOfInventory = BigDecimal.ZERO;
                        List<BigDecimal> distinctPrices = new ArrayList<>();
                        BigDecimal totalQuantity = BigDecimal.ZERO;
                        for (CostDetail costDetail : inventorySnapShotMap.get(scmProductInventoryData.getProductId())) {
                            DayCloseCostDetailDataCafeDump costDetailDataDump = new DayCloseCostDetailDataCafeDump();
                            costDetailDataDump.setDayCloseEventId(stockEvent.getEventId());
                            costDetailDataDump.setCostDetailDataId(costDetail.getCostDetailId());
                            costDetailDataDump.setUnitId(costDetail.getUnitId());
                            costDetailDataDump.setPrice(costDetail.getPrice());
                            costDetailDataDump.setQuantity(costDetail.getQuantity());
                            costDetailDataDump.setProductId(costDetail.getKeyId());
                            costDetailDataDump.setUom(costDetail.getUom());
                            costDetailDataDump.setLatest(SCMUtil.setStatus(costDetail.isLatest()));
                            costDetailDataDump.setLastUpdatedTime(costDetail.getLastUpdatedTimes());
                            costDetailDataDump.setExpiryDate(costDetail.getExpiryDate());
                            costDetailDataDump.setCreationReason(costDetail.getCreationReason());
                            costDetailDataDump.setCreationItemId(costDetail.getCreationItemId());
                            dayCloseCostDetailDataCafeDumpList.add(costDetailDataDump);
                            costOfInventory = costOfInventory.add(costDetail.getPrice().multiply(costDetail.getQuantity().compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : costDetail.getQuantity()));
                            zeroQuantityCostOfInventory = zeroQuantityCostOfInventory.add(SCMUtil.convertToBigDecimal(costDetail.getPrice()));
                            distinctPrices.add(SCMUtil.convertToBigDecimal(costDetail.getPrice()));
                            totalQuantity = totalQuantity.add(SCMUtil.convertToBigDecimal(costDetail.getQuantity()));
                        }
                        BigDecimal avgPrice;
                        if (totalQuantity.compareTo(BigDecimal.ZERO) > 0) {
                            avgPrice = AppUtils.divideWithScale10(costOfInventory, totalQuantity);
                            scmProductInventoryData.setAveragePrice(avgPrice);
                        } else {
                            avgPrice = AppUtils.divideWithScale10(zeroQuantityCostOfInventory, BigDecimal.valueOf(distinctPrices.size()));
                            scmProductInventoryData.setAveragePrice(avgPrice);
                        }
                        productAveragePriceMap.put(scmProductInventoryData.getProductId(), avgPrice);
                        needToUpdateList.add(scmProductInventoryData);
                    }
                }
            }
            // updating other stock take types
            for (Map.Entry<String,List<SCMProductInventoryData>> mapEntry : allInventoryDataOfCurrentDayCloseList.entrySet()) {
                if (!mapEntry.getKey().equalsIgnoreCase(StockTakeType.DAILY.value()) && Objects.nonNull(mapEntry.getValue())) {
                    for (SCMProductInventoryData scmProductInventoryData : mapEntry.getValue()) {
                        BigDecimal averagePrice = productAveragePriceMap.get(scmProductInventoryData.getProductId());
                        scmProductInventoryData.setAveragePrice(averagePrice);
                        needToUpdateList.add(scmProductInventoryData);
                    }
                }
            }
            if (!needToUpdateList.isEmpty()) {
                scmAssetManagementDao.update(needToUpdateList, true);
            }
            if (!dayCloseCostDetailDataCafeDumpList.isEmpty()) {
                stockManagementDao.addAll(dayCloseCostDetailDataCafeDumpList);
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While Dumping Cost Detail Data Cafe and Updating Average Price ::: ", e);
        }
    }

    private void setNextBusinessDateExpectedExpiry(SCMDayCloseEventData dayCloseEvent, SCMDayCloseEventData stockEvent, Map<Integer, SCMProductInventoryData> dailyInventoryData) {
        Map<Integer, List<CostDetail>> inventorySnapShotMap = fetchUnitProductInventory(dayCloseEvent.getUnitId(), PriceUpdateEntryType.PRODUCT.name())
                .stream().collect(Collectors.groupingBy(CostDetail::getKeyId));
        Map<Integer, Pair<BigDecimal, BigDecimal>> expectedWastageForNextBusinessDate = getExpectedWastageForNextBusinessDate(inventorySnapShotMap, stockEvent);
        // Getting the Daily Entries Of Current Day Close and setting the Expiry data

        List<SCMProductInventoryData> needToUpdateList = new ArrayList<>();
        for (Map.Entry<Integer, Pair<BigDecimal, BigDecimal>> entry : expectedWastageForNextBusinessDate.entrySet()) {
            SCMProductInventoryData scmProductInventoryData = dailyInventoryData.get(entry.getKey());
            if (Objects.nonNull(scmProductInventoryData)) {
                Pair<BigDecimal, BigDecimal> wastageData = entry.getValue();
                scmProductInventoryData.setNextDayExpectedExpiryWastage(wastageData.getKey());
                scmProductInventoryData.setNextDayExpectedWastageByNow(wastageData.getValue());
                dailyInventoryData.put(entry.getKey(), scmProductInventoryData);
                needToUpdateList.add(scmProductInventoryData);
            }
        }
        if (!needToUpdateList.isEmpty()) {
            stockManagementDao.update(needToUpdateList, true);
        }
    }

    private Map<Integer, Pair<BigDecimal, BigDecimal>> getExpectedWastageForNextBusinessDate(Map<Integer, List<CostDetail>> inventorySnapShotMap, SCMDayCloseEventData stockEvent) {
        // Product Id, Next Day Wastage, Complete Wastage Expected
        Map<Integer, Pair<BigDecimal, BigDecimal>> result = new HashMap<>();
        Date nextBusinessDate = AppUtils.getDateAfterDays(stockEvent.getBusinessDate(), 1);
        LOG.info("Current Business Date is : {} and next business date is : {}", stockEvent.getBusinessDate(), nextBusinessDate);
        try {
            for (Map.Entry<Integer, List<CostDetail>> entry : inventorySnapShotMap.entrySet()) {
                BigDecimal nextBusinessDateExpiry = BigDecimal.ZERO;
                BigDecimal completeExpiryTillNextDate = BigDecimal.ZERO;
                for (CostDetail costDetail : entry.getValue()) {
                    if (costDetail.getQuantity().compareTo(BigDecimal.ZERO) > 0) {
                        Date expiryBusinessDate = SCMUtil.getScmBusinessDate(costDetail.getExpiryDate());
                        if (expiryBusinessDate.compareTo(nextBusinessDate) <= 0) {
                            if(expiryBusinessDate.compareTo(nextBusinessDate) == 0) {
                                nextBusinessDateExpiry = nextBusinessDateExpiry.add(costDetail.getQuantity());
                            }
                            completeExpiryTillNextDate = completeExpiryTillNextDate.add(costDetail.getQuantity());
                        }
                    }
                }
                result.put(entry.getKey(), new Pair<>(nextBusinessDateExpiry, completeExpiryTillNextDate));
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While getExpectedWastageForNextBusinessDate ::: ", e);
        }
        return result;
    }

    private List<RegularOrderEvent> generateOrderingEvents(SCMDayCloseEventData dayClose) {
        List<RegularOrderEvent> result = new ArrayList<>();
        try {
            List<RegularOrderUnitBrandData> orderUnitBrandData = warehouseStockManagementService.getRoUnitBrandData(dayClose.getUnitId());
            if (orderUnitBrandData.size() == 0) {
                LOG.info("No Ordering Schedule Found");
            }
            else {
                Date businessDate = dayClose.getBusinessDate();
                String dayType = AppUtils.getFormattedTime(businessDate,"EEEE");
                LOG.info("day type is : {} and date is {}",dayType,businessDate);
                for (RegularOrderUnitBrandData brandData : orderUnitBrandData) {
                    LOG.info("Found brand data of brand Id : {}", brandData.getBrandId());
                    UnitOrderScheduleData unitOrderScheduleData = getOrderScheduleOfDay(brandData, dayType);
                    if (Objects.nonNull(unitOrderScheduleData)) {
                        RegularOrderingEvent regularOrderingEvent = generateRegularOrderingEvent(dayClose, businessDate, brandData, unitOrderScheduleData,false);
                        if (Objects.nonNull(regularOrderingEvent)) {
                            RegularOrderEvent orderEvent = new RegularOrderEvent(regularOrderingEvent.getEventId(), regularOrderingEvent.getUnitId(), regularOrderingEvent.getBrand()
                                    , regularOrderingEvent.getFulfilmentDate(), regularOrderingEvent.getOrderingDays(), regularOrderingEvent.getStatus());
                            result.add(orderEvent);
                        }
                    }
                }
//                if (orderUnitBrandData.size() == 1) {
//                RegularOrderUnitBrandData data = orderUnitBrandData.get(0);
//                LOG.info("Found only 1 brand data of brand Id : {}",data.getBrandId());
//                UnitOrderScheduleData unitOrderScheduleData = getOrderScheduleOfDay(data,dayType);
//                if (Objects.nonNull(unitOrderScheduleData)) {
//                    RegularOrderingEvent regularOrderingEvent = generateRegularOrderingEvent(dayClose, businessDate, data,unitOrderScheduleData,false);
//                    if (Objects.nonNull(regularOrderingEvent)) {
//                        RegularOrderEvent orderEvent = new RegularOrderEvent(regularOrderingEvent.getEventId(), regularOrderingEvent.getUnitId(), regularOrderingEvent.getBrand()
//                                , regularOrderingEvent.getFulfilmentDate(), regularOrderingEvent.getOrderingDays(), regularOrderingEvent.getStatus());
//                        result.add(orderEvent);
//                    }
//                    return result;
//                }
//                else {
//                    LOG.info("No Unit Ordering Schedule Found...!");
//                }
//                }
//                else {
//                    Boolean check = checkForBothBrands(orderUnitBrandData,dayType);
//                    if (check) {
//                        RegularOrderUnitBrandData brandData = orderUnitBrandData.get(0);
//                        LOG.info("BOTH brands found here ..!");
//                        UnitOrderScheduleData unitOrderScheduleData = getOrderScheduleOfDay(brandData, dayType);
//                        if (Objects.nonNull(unitOrderScheduleData)) {
//                            LOG.info("Got unit schedule for both brands");
//                            RegularOrderingEvent regularOrderingEvent = generateRegularOrderingEvent(dayClose, businessDate, brandData, unitOrderScheduleData,true);
//                            if (Objects.nonNull(regularOrderingEvent)) {
//                                RegularOrderEvent orderEvent = new RegularOrderEvent(regularOrderingEvent.getEventId(), regularOrderingEvent.getUnitId(), "BOTH"
//                                        , regularOrderingEvent.getFulfilmentDate(), regularOrderingEvent.getOrderingDays(), regularOrderingEvent.getStatus());
//                                result.add(orderEvent);
//                            }
//                        }
//                    }
//                    else {
//                        for (RegularOrderUnitBrandData brandData : orderUnitBrandData) {
//                            LOG.info("Found brand data of brand Id : {}", brandData.getBrandId());
//                            UnitOrderScheduleData unitOrderScheduleData = getOrderScheduleOfDay(brandData, dayType);
//                            if (Objects.nonNull(unitOrderScheduleData)) {
//                                RegularOrderingEvent regularOrderingEvent = generateRegularOrderingEvent(dayClose, businessDate, brandData, unitOrderScheduleData,false);
//                                if (Objects.nonNull(regularOrderingEvent)) {
//                                    RegularOrderEvent orderEvent = new RegularOrderEvent(regularOrderingEvent.getEventId(), regularOrderingEvent.getUnitId(), regularOrderingEvent.getBrand()
//                                            , regularOrderingEvent.getFulfilmentDate(), regularOrderingEvent.getOrderingDays(), regularOrderingEvent.getStatus());
//                                    result.add(orderEvent);
//                                }
//                            }
//                        }
//                    }
//                    return result;
//                }
            }
        }
        catch (Exception e) {
            LOG.error("Error Occurred in generating day close RO events :: ",e);
        }
        return result;
    }

    private UnitOrderScheduleData getOrderScheduleOfDay(RegularOrderUnitBrandData data, String dayType) {
        for (UnitOrderScheduleData scheduleData : data.getUnitOrderScheduleData()) {
            if (scheduleData.getOrderingDay().equalsIgnoreCase(dayType) && Objects.nonNull(scheduleData.getIsFunctional()) && scheduleData.getIsFunctional().equalsIgnoreCase(AppConstants.YES)
                    && Objects.nonNull(scheduleData.getIsOrderingDay()) && scheduleData.getIsOrderingDay().equalsIgnoreCase(AppConstants.YES)
                    && Objects.nonNull(scheduleData.getOrderingDays())) {
                return scheduleData;
            }
        }
        return null;
    }

    private RegularOrderingEvent generateRegularOrderingEvent(SCMDayCloseEventData dayClose, Date businessDate, RegularOrderUnitBrandData data,
                                                              UnitOrderScheduleData unitOrderScheduleData,Boolean bothBrands) throws SumoException {
        String brand = bothBrands ? "BOTH" : getBrandName(data.getBrandId());
        Date fulfilmentDate = AppUtils.getDateAfterDays(businessDate,3);
        List<RegularOrderingEvent> orderingEvents = stockManagementDao.checkOrderingEvents(dayClose, brand, unitOrderScheduleData, fulfilmentDate);
        LOG.info("Returned events for unit id : {} and date is : {} and size : {}",dayClose.getUnitId(),businessDate,orderingEvents.size());
        if (orderingEvents.size() == 0) {
            LOG.info("Generating events for unit id : {} and fulfilment date is : {}",dayClose.getUnitId(),fulfilmentDate);
            RegularOrderingEvent regularOrderingEvent = new RegularOrderingEvent();
            regularOrderingEvent.setDayCloseEventData(dayClose);
            regularOrderingEvent.setUnitId(dayClose.getUnitId());
            regularOrderingEvent.setBrand(bothBrands ? "BOTH" : getBrandName(data.getBrandId()));
            regularOrderingEvent.setFulfilmentDate(AppUtils.getDateAfterDays(businessDate, 3));
            regularOrderingEvent.setOrderingDays(unitOrderScheduleData.getOrderingDays());
            regularOrderingEvent.setStatus(AppConstants.STATUS_CREATED);
            regularOrderingEvent.setCreatedAt(AppUtils.getCurrentTimestamp());
            regularOrderingEvent = getDao().add(regularOrderingEvent, true);
            dayClose.setOrderingSuccess(AppConstants.NO);
            getDao().update(dayClose, true);
            return regularOrderingEvent;
        }
        return null;
    }

    private String getBrandName(Integer brandId) {
        if (brandId == 1) {
            return "Chaayos";
        }
        else if (brandId == 4) {
            return "BOTH";
        }
        else {
            return "Ghee and Turmeric";
        }
    }

    private Boolean checkForBothBrands(List<RegularOrderUnitBrandData> orderUnitBrandData, String dayType) {
        List<UnitOrderScheduleData> list = new ArrayList<>();
        for (RegularOrderUnitBrandData brandData : orderUnitBrandData) {
            UnitOrderScheduleData unitOrderScheduleData = getOrderScheduleOfDay(brandData,dayType);
            if (Objects.nonNull(unitOrderScheduleData)) {
                list.add(unitOrderScheduleData);
            }
        }
        if (list.size() > 1) {
            LOG.info("Checking for both brands");
            return list.get(0).getOrderingDays().equals(list.get(1).getOrderingDays());
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public StockInventoryData getExpectedValues(int unitId, int userId, StockTakeType stockTakeType,
                                                StockEventType stockEventType , Date businessDate) throws SumoException {
        long startTime = System.currentTimeMillis();
        LOG.info("Started Getting Expected Values For Unit Id : {}", unitId);
        Date currentBusinessDate = Objects.nonNull(businessDate) ? businessDate : SCMUtil.getCurrentBusinessDate();
        Date tempDate = SCMUtil.getCurrentBusinessDate();
        StockInventoryData detail = new StockInventoryData();
        detail.setUnit(unitId);
        detail.setEventType(stockEventType);
        detail.setFrequency(stockTakeType);
        detail.setGeneratedBy(userId);
        try {
            List<ProductStockForUnit> productStockForUnitList = new ArrayList<>();
            if (stockEventType.equals(StockEventType.CLOSING)) {
                SCMDayCloseEventData stockEvent = getLastDayCloseEvent(unitId, stockTakeType,
                        StockEventType.STOCK_TAKE);
                if (stockEvent != null) {
                    Map<Integer, ProductStockForUnit> dailyExpectedValuesMap = new HashMap<>();
                    if (!stockTakeType.equals(StockTakeType.DAILY)) {
                        dailyExpectedValuesMap = getDailyStockTakeExpectedValues(unitId, userId, stockEventType, businessDate)
                                .stream().collect(Collectors.toMap(ProductStockForUnit::getProductId, Function.identity()));
                    }
                    LOG.info("Last Day Close Event Found For Unit ID : {} is : {}", unitId, stockEvent.getEventId());
                    Map<Integer, List<CostDetail>> inventorySnapShotMap = fetchUnitProductInventory(stockEvent.getUnitId(), PriceUpdateEntryType.PRODUCT.name())
                            .stream().collect(Collectors.groupingBy(CostDetail::getKeyId));
                    Map<Integer, ProductBasicDetail> productList = scmCache.getProductDefinitions().values().stream()
                            .filter(productDefinition -> inventorySnapShotMap.containsKey(productDefinition.getProductId()))
                            .collect(Collectors.toMap(ProductDefinition::getProductId,
                                    SCMDataConverter::convert));
                    Map<Integer, BigDecimal> productUnitPrices = new HashMap<>();
                    setProductUnitPrices(productUnitPrices, inventorySnapShotMap);
                    LOG.info("Complete Product List For Frequency After Filtering : {} for Unit : {} is : {}", stockTakeType.name(), unitId, productList.size());
                    Map<Integer, BigDecimal> inventoryList = stockManagementDao.getStockForUnit(unitId, stockEvent.getEventId());
                    LOG.info("::: Getting Closing Stock With Last Day close Id :::");
                    long time = System.currentTimeMillis();
                    Map<Integer, Pair<SCMDayCloseEventData, BigDecimal>> notInLastDayCloseProducts = getNotInLastDayCloseProducts(inventoryList, productList);
                    if (!notInLastDayCloseProducts.isEmpty()) {
                        LOG.info("List of Products not in Last Day close are : {}",Arrays.asList(notInLastDayCloseProducts.keySet().toArray()));
                    }
                    Map<Integer, BigDecimal> varianceTillNowMap = stockManagementDao.getVarianceTillNow(stockEvent, null);
                    Map<Integer, SCMProductConsumptionData> consumptionDataMap = getRecords(
                            getAllRecords(unitId, currentBusinessDate, stockTakeType,notInLastDayCloseProducts, stockEvent, varianceTillNowMap), stockEvent, null,
                            false, stockTakeType, null, null);
                    LOG.info("######### Completed Getting Consumption Data ---------- {}ms",System.currentTimeMillis() - time);
                    time = System.currentTimeMillis();
                    if (inventoryList != null) {
                        for (Integer productId : productList.keySet()) {
                            ProductStockForUnit stockForUnit = new ProductStockForUnit();
                            stockForUnit.setProductId(productId);
                            ProductDefinition productDefinition = scmCache.getProductDefinition(productId);
                            stockForUnit.setCategoryId(productDefinition.getCategoryDefinition().getId());
                            SCMProductConsumptionData consumptionData = consumptionDataMap.get(productId);
                            BigDecimal expectedValue = inventoryList.get(productId) != null ? inventoryList.get(productId) : BigDecimal.ZERO;
                            stockForUnit.setOpening(expectedValue);
                            if (consumptionData != null) {
                                stockForUnit.setVarianceTillNow(SCMUtil.convertToBigDecimal(consumptionData.getVarianceTillNow()));
                                expectedValue = calculateConsumptionForProduct(consumptionData, expectedValue, stockForUnit);
                                stockForUnit.setTransferred(SCMUtil.convertToBigDecimal(consumptionData.getTransferOut()));
                                stockForUnit.setReceived(SCMUtil.convertToBigDecimal(consumptionData.getReceived()));
                                stockForUnit.setWasted(SCMUtil.convertToBigDecimal(consumptionData.getWastage()));
                                stockForUnit.setConsumption(SCMUtil.convertToBigDecimal(consumptionData.getConsumption()));
                            } else {
                                stockForUnit.setVarianceTillNow(SCMUtil.convertToBigDecimal(varianceTillNowMap.get(productId)));
                            }
                            stockForUnit.setOriginalExpectedValue(expectedValue);
                            stockForUnit.setVarianceTillNow(SCMUtil.convertToBigDecimal(stockForUnit.getVarianceTillNow()));
                            if (expectedValue.compareTo(BigDecimal.ZERO) < 0) {
                                stockForUnit.setExtraVariance(expectedValue);
                            }
                            stockForUnit.setExpectedValue(expectedValue);
                            if (Objects.isNull(stockForUnit.getOriginalExpectedValue())) {
                                stockForUnit.setOriginalExpectedValue(expectedValue);
                            }
                            stockForUnit.setStockValue(expectedValue);
                            stockForUnit.setProductId(productId);
                            stockForUnit.setVariance(BigDecimal.ZERO);
                            if (productUnitPrices.containsKey(productId)) {
                                stockForUnit.setUnitPrice(SCMUtil.convertToBigDecimal(productUnitPrices.get(productId)));
                            } else {
                                stockForUnit.setUnitPrice(BigDecimal.ZERO);
                            }
                            if (!stockTakeType.equals(StockTakeType.DAILY)) {
                                if (dailyExpectedValuesMap.containsKey(stockForUnit.getProductId())) {
                                    ProductStockForUnit dailyExpectedValueOfProduct = dailyExpectedValuesMap.get(stockForUnit.getProductId());
                                    stockForUnit.setDailyExpectedValue(dailyExpectedValueOfProduct.getStockValue());
                                    stockForUnit.setDailyVariance(dailyExpectedValueOfProduct.getVariance());
                                }
                            } else {
                                stockForUnit.setDailyExpectedValue(stockForUnit.getStockValue());
                                stockForUnit.setDailyVariance(stockForUnit.getVariance());
                            }
                            productStockForUnitList.add(stockForUnit);
                        }
                        consumptionDataMap.clear();
                        inventoryList.clear();
                        productList.clear();
                    }
                }
            } else if (stockEventType.equals(StockEventType.OPENING)) {
                List<ProductDefinition> allProducts = getAllProductsByFrequency(StockTakeType.MONTHLY);
                for (ProductDefinition productDefinition : allProducts) {
                    ProductStockForUnit stockForUnit = new ProductStockForUnit();
                    int productId = productDefinition.getProductId();
                    stockForUnit.setCategoryId(productDefinition.getCategoryDefinition().getId());
                    stockForUnit.setProductId(productId);
                    stockForUnit.setStockValue(BigDecimal.ZERO);
                    stockForUnit.setOpening(BigDecimal.ZERO);
                    stockForUnit.setVariance(BigDecimal.ZERO);
                    productStockForUnitList.add(stockForUnit);
                }
            }
            if (productStockForUnitList != null) {
                ProductStockForUnitComparator comparator = new ProductStockForUnitComparator(
                        scmCache.getProductDefinitions());
                Collections.sort(productStockForUnitList, comparator);
            }
            detail.getInventoryResponse().addAll(productStockForUnitList);
            LOG.info("######### Completed Get Expected Values in  ---------- {}ms",System.currentTimeMillis() - startTime);
            return detail;
        } catch (Exception e) {
            LOG.error("got exception while processing list for inventory", e);
            throw e;
        }
    }

    private List<ProductStockForUnit> getDailyStockTakeExpectedValues(int unitId, int userId, StockEventType stockEventType, Date businessDate) {
        // GETTING THE LAST DAILY DAY CLOSE EVENT TO CALCULATE THE NEGATIVE VARIANCE DATA
        try {
            StockInventoryData stockInventoryData = getExpectedValues(unitId, userId, StockTakeType.DAILY, stockEventType, businessDate);
            if (Objects.nonNull(stockInventoryData) && Objects.nonNull(stockInventoryData.getInventoryResponse()) && !stockInventoryData.getInventoryResponse().isEmpty()) {
                return stockInventoryData.getInventoryResponse();
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While getDailyStockTakeExpectedValues :: ", e);
        }
        return new ArrayList<>();
    }

    private void setProductUnitPrices(Map<Integer, BigDecimal> productUnitPrices, Map<Integer, List<CostDetail>> inventorySnapShotMap) {
        for (Map.Entry<Integer, List<CostDetail>> mapEntry : inventorySnapShotMap.entrySet()) {
            for (CostDetail costDetail : mapEntry.getValue()) {
                productUnitPrices.put(mapEntry.getKey(), costDetail.getPrice());
                break;
            }
        }
    }

    private void calculateVarianceTillNow(SCMDayCloseEventData stockEvent, Map<Integer, ProductBasicDetail> productList, Map<Integer, BigDecimal> varianceTillNowMap) {
//        Map<Integer, List<Integer>> calculateVarianceProducts = new HashMap<>();
//        List<Integer> productIds = new ArrayList<>();
//        LOG.info("Started Calculating Variance Which are not in teh Consumption map ");
//        for (Integer productId : productList.keySet()) {
//            SCMProductConsumptionData consumptionData = consumptionDataMap.get(productId);
//            if (Objects.isNull(consumptionData)) {
//                SCMDayCloseEventData lastDayCloseEventData = stockEvent;
//                if (notInLastDayCloseProducts.containsKey(productId)) {
//                    if (Objects.nonNull(notInLastDayCloseProducts.get(productId).getKey())) {
//                        lastDayCloseEventData = notInLastDayCloseProducts.get(productId).getKey();
//                    }
//                }
//                if (calculateVarianceProducts.containsKey(lastDayCloseEventData.getEventId())) {
//                    productIds = calculateVarianceProducts.get(lastDayCloseEventData.getEventId());
//                } else {
//                    productIds = new ArrayList<>();
//                }
//                productIds.add(productId);
//                calculateVarianceProducts.put(lastDayCloseEventData.getEventId(), productIds);
//            }
//        }
        Map<Integer, BigDecimal> varianceTillNow = stockManagementDao.getVarianceTillNow(stockEvent, new ArrayList<>(productList.keySet()));
        varianceTillNowMap.putAll(varianceTillNow);
        LOG.info("Completed Calculating Variance Which are not in teh Consumption map ");
    }

    private Map<Integer, Pair<SCMDayCloseEventData, BigDecimal>> getNotInLastDayCloseProducts(Map<Integer, BigDecimal> inventoryList, Map<Integer, ProductBasicDetail> productList) {
        Map<Integer, Pair<SCMDayCloseEventData, BigDecimal>> result = new HashMap<>();
        for (Map.Entry<Integer, ProductBasicDetail> product : productList.entrySet()) {
            if (!inventoryList.containsKey(product.getKey())) {
                result.put(product.getKey(), new Pair<>(null, BigDecimal.ZERO));
            }
        }
        return result;
    }

    private SCMDayCloseEventData getLastDayCloseEvent(int unitId, StockTakeType stockType, StockEventType eventType) {
        SCMDayCloseEventData event = stockType.equals(StockTakeType.DAILY)
                ? stockManagementDao.getLastDayCloseEvent(unitId, eventType, false)
                : stockManagementDao.getLastDayCloseEventOfType(unitId, stockType, eventType);
        if (event == null) {
            if (stockType.equals(StockTakeType.WEEKLY)) {
                event = stockManagementDao.getLastDayCloseEventOfType(unitId, StockTakeType.MONTHLY, eventType);
            }
            if (event == null) {
                event = stockManagementDao.getOpeningEventForUnit(unitId);
            }
        }
        return event;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public int checkAndUpdateInventoryForUnit(StockInventoryData detail, StockTakeType stockType, Date businessDate,
                                               boolean runValidation) throws NegativeStockException, InventoryUpdateException, SumoException {
        int check = -1;
        if(Objects.nonNull(detail.getEventType()) && detail.getEventType().equals(StockEventType.OPENING)){
            check = 1;
        }else{
            check = validateKettleDayClose(detail.getUnit(), businessDate);
        }
        if (check != 1) {
            return check;
        }
        if (checkInventoryUpdated(detail, businessDate, stockType)) {
            SCMError error = new SCMError(stockType.name() + " Inventory already updated", "", 701);
            throw new InventoryUpdateException(error);
        }
        if (calendarValidationRequired(stockType, detail)) {
            validateCalendarEvent(detail, businessDate, stockType);
        }
        detail.setFrequency(stockType);
        int result =  updateInventoryForUnit(detail, stockType, businessDate, true);
        //TODO : Send Variance Report to cafE
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void generateNextWeeklyMonthlyEvent(StockInventoryData inventoryData, StockTakeType stockType, Date currentBusinessDate) {
        try {
            if (calendarValidationRequired(stockType, inventoryData)) {
                List<StockEventCalendarData> pendingCalenderEvents = stockManagementDao.getPendingCalendarEvent(inventoryData.getUnit(), currentBusinessDate);
                if (!pendingCalenderEvents.isEmpty()) {
                    for (StockEventCalendarData pendingCalenderEvent : pendingCalenderEvents) {
                        pendingCalenderEvent.setStatus(StockCalendarEventStatus.CLOSED.name());
                        pendingCalenderEvent.setEventTime(SCMUtil.getCurrentTimestamp());
                        stockManagementDao.update(pendingCalenderEvent, true);
                        generateNextStockCalendarEvent(inventoryData, StockTakeType.valueOf(pendingCalenderEvent.getEventFrequencyType()));
                    }
                }
                flushUnitPosSessions(inventoryData.getUnit());
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While GENERATENEXTWEEKLYMONTHLYEVENT ::: for Unit : {} ", inventoryData.getUnit(), e);
        }
    }

    private Integer validateKettleDayClose(Integer unit, Date businessDate) throws InventoryUpdateException, SumoException {
        SCMDayCloseEventData initiated = stockManagementDao.getInitiatedDayCloseEvents(unit, businessDate);
        if (Objects.nonNull(initiated) && Objects.nonNull(initiated.getDayClosureId())) {
            try {
                LOG.info("Checking for Kettle Day Close with Day close Id : {} and closure Id : {}", initiated.getEventId(), initiated.getDayClosureId());
                String endPoint = props.getKettleServiceBasePath() + KettleServiceClientEndpoints.VALIDATE_KETTLE_DAY_CLOSE;
                Map<String, Integer> uriVariables = new HashMap<>();
                uriVariables.put("dayClosureId", initiated.getDayClosureId());
                Boolean kettleDayCloseStatus = WebServiceHelper.exchangeWithAuth(endPoint, props.getAuthToken(), HttpMethod.GET,
                        Boolean.class, null, uriVariables);
                if (kettleDayCloseStatus.equals(Boolean.FALSE)) {
                    cancelAllPreviousEvents(unit, businessDate);
                    return 404;
                }
                return 1;
            } catch (URISyntaxException e) {
                LOG.error("Error verifying the Kettle Day close Of unit : {} for business date : {}", unit, businessDate, e);
                throw new SumoException("Error Verifying Kettle Day Close","Error Occurred While Verifying the Kettle Day Close Of Unit");
            }
        } else {
            LOG.info("No Day close Event Found for Unit : {} for business Date : {}",unit, businessDate);
            return 405;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public StockCalendarEventCheckVO cheAvailableStockCalendarEvent(StockInventoryData detail, StockTakeType stockType, Date businessDate,
                                                                    boolean runValidation) {
        StockCalendarEventCheckVO response;
        try {
            response = new StockCalendarEventCheckVO();
            response.setValid(validateCalendarEvent(detail, businessDate, stockType));
        } catch (InventoryUpdateException e) {
            response = new StockCalendarEventCheckVO();
            response.setValid(false);
            response.setErrorMsg(e.getMessage());
            List<StockEventCalendarData> plannedEvents = stockManagementDao.getPlannedCalendarEventByType(detail.getUnit(), stockType, businessDate);
            if (plannedEvents != null && !plannedEvents.isEmpty()) {
                response.setErrorMsg(e.getMessage() + " Your next " + stockType.name()
                        + " stock event is planned on " + SCMUtil.getFormattedDate(plannedEvents.get(0).getScheduledDate()));
            }
        }
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<StockCalendarEventCheckVO> cheAvailableStockCalendarEvents(StockInventoryData detail, Date businessDate,
                                                                    boolean runValidation) {
        List<StockCalendarEventCheckVO> responses = new ArrayList<>();
        List<StockTakeType> stockTakeTypes = new ArrayList<>();
        stockTakeTypes.add(StockTakeType.WEEKLY);
        stockTakeTypes.add(StockTakeType.MONTHLY);
        for (StockTakeType stockType : stockTakeTypes) {
            StockCalendarEventCheckVO response = new StockCalendarEventCheckVO();
            List<StockEventCalendarData> data = stockManagementDao.checkPendingCalendarEventByType(detail.getUnit(), stockType, businessDate);
            if (data == null || data.isEmpty()) {
                response.setValid(false);
            } else {
                response.setValid(true);
            }
            responses.add(response);
        }
        return responses;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<StockEventCalendarData> getPendingCalendarEventForUnit(Integer unitId) {
        return stockManagementDao.getPendingCalendarEventForUnit(unitId, SCMUtil.getCurrentBusinessDate());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<StockEventCalendarData> getPlannedCalendarEventByType(Integer unitId, StockTakeType stockType) {
        return stockManagementDao.getPlannedCalendarEventByType(unitId, stockType, AppUtils.getOldDate(AppUtils.getCurrentBusinessDate(), 365));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateStockEventCalendar(List<StockEventCalendarData> request) throws SumoException {
        for (StockEventCalendarData stockEventCalendarData : request) {
            StockEventCalendarData data = stockManagementDao.find(StockEventCalendarData.class, stockEventCalendarData.getEventId());
            if (data == null) {
                throw new SumoException("Event id " + stockEventCalendarData.getEventId() + " not valid.");
            }
            data.setScheduledDate(SCMUtil.getStartOfDay(stockEventCalendarData.getScheduledDate()));
            data.setUpdatedBy(stockEventCalendarData.getUpdatedBy());
            data.setUpdatedAt(SCMUtil.getCurrentTimestamp());
            data = stockManagementDao.update(data, false);
            if (data == null) {
                throw new SumoException("Error updating stock calendar event.");
            }
        }
        stockManagementDao.flush();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public int updateInventoryForUnit(StockInventoryData detail, StockTakeType stockType, Date businessDate,
                                      boolean runValidation) throws NegativeStockException {
        int flag = AppConstants.UPDATE_INVENTORY_INIT;
        List<ProductStockForUnit> negativeStockList = new ArrayList<>();
        if (runValidation) {
            validateInventory(detail.getInventoryResponse());
        }
        if (validateStockingType(stockType, businessDate) && negativeStockList.isEmpty()) {
            StockEventType eventType = checkOpeningRequired(detail.getUnit()) ? StockEventType.OPENING
                    : StockEventType.STOCK_TAKE;
            try {
                SCMDayCloseEventData stockTakeEvent = stockManagementDao.createStockTakeEvent(eventType,
                        detail.getUnit(), businessDate, stockType, detail.getGeneratedBy());
                for (ProductStockForUnit productStockForUnit : detail.getInventoryResponse()) {
                    StockEntryEventData productStock = new StockEntryEventData();
                    productStock.setUnitId(detail.getUnit());
                    productStock.setProductId(productStockForUnit.getProductId());
                    productStock.setCurrentStock(SCMUtil.convertToBigDecimal(productStockForUnit.getStockValue()).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO
                            : SCMUtil.convertToBigDecimal(productStockForUnit.getStockValue()));
                    productStock.setUpdateEventId(stockTakeEvent.getEventId());
                    productStock.setUnitOfMeasure(scmCache.getProductDefinitions()
                            .get(productStockForUnit.getProductId()).getUnitOfMeasure());
                    productStock.setGeneratedBy(
                            detail.getGeneratedBy() != 0 ? detail.getGeneratedBy() : SCMServiceConstants.SYSTEM_USER);
                    stockManagementDao.add(productStock, false);
                }
                if (Objects.nonNull(detail.getNegativeVarianceList()) && !detail.getNegativeVarianceList().isEmpty()) {
                    warehouseStockManagementService.addVarianceExpiryData(detail.getNegativeVarianceList(), stockTakeEvent.getEventId(), detail.getGeneratedBy(), PriceUpdateEntryType.PRODUCT);
                }
                flag = AppConstants.UPDATE_INVENTORY_SUCCESS;
            } catch (Exception e) {
                LOG.error("Settling inventory had an issue while updating for unit {}", detail.getUnit(), e);
                flag = AppConstants.UPDATE_INVENTORY_FAIL;
            }
        } else if (!negativeStockList.isEmpty()) {
            NegativeStockException e = new NegativeStockException("NEGATIVE STOCK FOUND!", "Please settle negative stock first");
            e.setNegatives(negativeStockList);
            throw e;
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void settleDayCloseForUnit(UnitDetail unit, Date businessDate, Integer stockTakeSumoDayCloseEventId) throws InventoryUpdateException, NegativeStockException, DataNotFoundException, SumoException {
        // settles day close unit by unit for ACTIVE units
        if (SwitchStatus.ACTIVE.name().equals(unit.getUnitStatus().name())) {
            try {
                //SETTLE Normal Day close events first
                settleDayCloseForUnit(unit.getUnitId(), businessDate, stockTakeSumoDayCloseEventId);
                //settleFixedAssetStockForUnit(unit.getUnitId(), businessDate);
            } catch (Exception ex) {
                logVarianceError(unit.getUnitId(), unit.getUnitName(), ex);
                throw ex;
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void logVarianceError(Integer unitId, String unitName, Exception e) {
        LOG.error("Exception occurred while settling variance in cron job for unit ::::::: {} {}",
                unitId, unitName, e);
        StringBuilder message = new StringBuilder();
        message.append("Exception occurred while settling variance in cron job \n");
        message.append("Unit ID :::::: " + unitId + "\n");
        message.append("Unit Name :::::: " + unitName + "\n");
        message.append(e.getMessage());
        SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "SUMO",
                SlackNotification.VARIANCE_REPORT_FAILURES, message.toString());
    }

    private boolean validateStockingType(StockTakeType stockType, Date businessDate) {
        /*
         * return stockType.equals(StockTakeType.MONTHLY) ? true :
         * stockType.equals(SCMUtil.getRequiredFrequency(businessDate));
         */
        return true;
    }

    private List<String> validateInventory(List<ProductStockForUnit> inventoryData) {
        List<String> negativeStock = new ArrayList<>();
        inventoryData.forEach(productStockForUnit -> {
            double stockValue = SCMUtil.convertToBigDecimal(productStockForUnit.getStockValue()).doubleValue();
            double expected = SCMUtil.convertToBigDecimal(productStockForUnit.getExpectedValue()).doubleValue();
            // when stock value is less than zero
            if (stockValue < 0.00d) {
                // when expected and stock both are same and negative
                if (expected != stockValue) {
                    negativeStock
                            .add(scmCache.getProductDefinition(productStockForUnit.getProductId()).getProductName());
                } else if (expected == stockValue) {
                    // reset stock to zero in case expected is less than zero
                    productStockForUnit.setStockValue(BigDecimal.ZERO);
                }
            }
        });

        return negativeStock;
    }

    @LogExecutionTime
    private List<ProductStockForUnit> settleInventoryForUnit(SCMDayCloseEventData dayCloseEvent,
                                                             StockTakeType stockType, Map<Integer, SCMProductConsumptionData> consumptionData,
                                                             Map<Integer, BigDecimal> inventoryDataMap,
                                                             Map<Integer, Pair<SCMDayCloseEventData, BigDecimal>> notInLastDayCloseProducts,
                                                             SCMDayCloseEventData lastEvent, Map<Integer, BigDecimal> varianceTillNowMap,
                                                             Map<Integer, BigDecimal> lastInventory, List<Integer> inActiveProductsList) throws SumoException {

        List<ProductStockForUnit> settledInventory = new ArrayList<>();
        List<String> missingTaxesProducts = new ArrayList<>();

        Map<Integer, ProductDefinition> productList = scmCache.getProductDefinitions();

        if (!notInLastDayCloseProducts.isEmpty()) {
            LOG.info("List of Products not in Last Day close are During Submission For Unit : {} are : {}",lastEvent.getUnitId(), Arrays.asList(notInLastDayCloseProducts.keySet().toArray()));
        }

        Map<Integer, SCMProductInventoryData> dailyInventoryDataOfLastDayClose = new HashMap<>();

        if (stockType.equals(StockTakeType.DAILY)) {
            // Getting the Daily Entries Of Current Day Close and setting the Expiry data
            dailyInventoryDataOfLastDayClose = stockManagementDao.getScmProductInventoryDataListByFrequency(lastEvent, StockTakeType.DAILY.value())
                    .stream().collect(Collectors.toMap(SCMProductInventoryData::getProductId, Function.identity(), (existingValue, newValue) -> newValue));
        }

        for (Integer productId : inventoryDataMap.keySet()) {
//            if (inActiveProductsList.contains(productId)) {
//                LOG.info("Product : {} Is IN_ACTIVE for Event Id : {}", productId, dayCloseEvent.getEventId());
//                continue;
//            }
            if(Objects.isNull(productList.get(productId))){
                LOG.info("scm Product Id : {} ",productId);
            }
            ProductBasicDetail productBasicDetail = SCMDataConverter.convert(productList.get(productId));
            BigDecimal stockEntry = inventoryDataMap.get(productId);
            SCMProductConsumptionData productConsumptionData = consumptionData.get(productId);
            BigDecimal openingValue = Objects.nonNull(lastInventory.get(productId)) ? SCMUtil.convertToBigDecimal(lastInventory.get(productId)) :  BigDecimal.ZERO;
            ProductStockForUnit productStockForUnit = new ProductStockForUnit();
            BigDecimal expectedValue = productConsumptionData != null
                    ? calculateConsumptionForProduct(productConsumptionData, openingValue, productStockForUnit)
                    : openingValue;
            if (Objects.nonNull(productConsumptionData)) {
                productStockForUnit.setVarianceTillNow(SCMUtil.convertToBigDecimal(productConsumptionData.getVarianceTillNow()));
            } else {
                productStockForUnit.setVarianceTillNow(SCMUtil.convertToBigDecimal(varianceTillNowMap.get(productId)));
            }
            productStockForUnit.setOriginalExpectedValue(expectedValue);
            if (expectedValue.compareTo(BigDecimal.ZERO) < 0) {
                productStockForUnit.setExtraVariance(expectedValue);
            }

            SCMProductInventoryData inventoryData = new SCMProductInventoryData();
            inventoryData.setBusinessDate(dayCloseEvent.getBusinessDate());
            // add opening value of stock
            inventoryData.setOpeningStock(openingValue.longValue() < 0L ? BigDecimal.ZERO : openingValue);
            inventoryData.setStatus(StockEventStatus.CLOSED.toString());
            inventoryData.setVariance(expectedValue.subtract(stockEntry));
            inventoryData.setExtraVariance(SCMUtil.convertToBigDecimal(productStockForUnit.getExtraVariance()));
            inventoryData.setOriginalVariance(inventoryData.getVariance());
            inventoryData.setGenerationTime(SCMUtil.getCurrentTimestamp());
            inventoryData.setGeneratedBy(SCMServiceConstants.SYSTEM_USER);
            inventoryData.setCurrentDayCloseEvent(dayCloseEvent);
            inventoryData.setLastDayCloseEvent(lastEvent.getEventId());
            inventoryData.setProductId(productId);
            inventoryData.setActualClosing(stockEntry);
            inventoryData.setOriginalClosing(stockEntry);
            inventoryData.setExpectedClosing(expectedValue);
            inventoryData.setUnitId(dayCloseEvent.getUnitId());
            inventoryData.setUnitOfMeasure(productBasicDetail.getUnitOfMeasure());
            inventoryData.setEventType(dayCloseEvent.getDayCloseEventType());
            inventoryData.setStockType(stockType.name());
            inventoryData.setOriginalExpectedClosing(SCMUtil.convertToBigDecimal(productStockForUnit.getOriginalExpectedValue()));
            inventoryData.setVarianceTillNow(SCMUtil.convertToBigDecimal(productStockForUnit.getVarianceTillNow()));
            int stateId = masterDataCache.getUnit(dayCloseEvent.getUnitId()).getLocation().getState().getId();
            String hsnCode = productList.get(productId).getTaxCode();
            TaxData taxData = taxCache.getTaxData(stateId, hsnCode);
            if (Objects.isNull(taxData)) {
                LOG.info("Tax Data is Missing for Product Id : {} : {}", productId,productBasicDetail.getProductName());
                missingTaxesProducts.add(productBasicDetail.getProductName() + " [ " + productId + "  ]");
                continue;
            }
            if (Objects.isNull(taxData.getState())) {
                LOG.info("Tax Data(State) is Missing for Product Id : {} : {}", productId,productBasicDetail.getProductName());
                missingTaxesProducts.add(productBasicDetail.getProductName() + " [ " + productId + "  ]");
                continue;
            }
            BigDecimal igstTaxPercentage = taxData.getState().getIgst();
            if (Objects.isNull(igstTaxPercentage)) {
                LOG.info("Missing IGST Tax for Product Id : {} : {}", productId,productBasicDetail.getProductName());
                missingTaxesProducts.add(productBasicDetail.getProductName() + " [ " + productId + "  ] (IGST)");
                continue;
            }
            inventoryData.setTaxType("IGST");
            inventoryData.setTaxPercentage(igstTaxPercentage);
            inventoryData.setCategoryId(productBasicDetail.getCategory().getId());
            inventoryData.setSubCategoryId(productBasicDetail.getSubCategory().getId());
            if (stockType.equals(StockTakeType.DAILY)) {
                // setting the Expiry Data
                setDailyExpiryWastageData(dailyInventoryDataOfLastDayClose, productId, productConsumptionData, inventoryData);
            }
            inventoryData = stockManagementDao.add(inventoryData, false);
            ProductStockForUnit stockForUnit = SCMDataConverter.convertInventory(inventoryData, productConsumptionData,
                    openingValue, productBasicDetail);
            settledInventory.add(stockForUnit);
            stockManagementDao.update(inventoryData, false);
        }
        if (!missingTaxesProducts.isEmpty()) {
            throw new SumoException("Missing Tax Data For Products  " , "Missing Tax Data For Products : " + Arrays.asList(missingTaxesProducts.toArray()));
        }
        stockManagementDao.flush();
        return settledInventory;
    }

    private void setDailyExpiryWastageData(Map<Integer, SCMProductInventoryData> dailyInventoryDataOfLastDayClose, Integer productId, SCMProductConsumptionData productConsumptionData, SCMProductInventoryData inventoryData) {
        BigDecimal lastDayCloseExpectedExpiryWastage = SCMUtil.convertToBigDecimal(Objects.nonNull(dailyInventoryDataOfLastDayClose.get(productId))
                ? dailyInventoryDataOfLastDayClose.get(productId).getNextDayExpectedExpiryWastage() : null);
        BigDecimal lastDayCloseCompleteExpiryWastage = SCMUtil.convertToBigDecimal(Objects.nonNull(dailyInventoryDataOfLastDayClose.get(productId))
                ? dailyInventoryDataOfLastDayClose.get(productId).getExpectedWastageByNow() : null);

        BigDecimal actualExpiryWastage = SCMUtil.convertToBigDecimal(Objects.nonNull(productConsumptionData)
                ? productConsumptionData.getExpiredWastage() : null);

        BigDecimal consumptionOfProduct = SCMUtil.convertToBigDecimal(Objects.nonNull(productConsumptionData)
                ? productConsumptionData.getConsumption() : null);

        BigDecimal expectedExpiryWastageAfterConsumption = BigDecimal.ZERO;

        if (consumptionOfProduct.compareTo(lastDayCloseExpectedExpiryWastage) < 0) {
            expectedExpiryWastageAfterConsumption = lastDayCloseExpectedExpiryWastage.subtract(consumptionOfProduct);
        }

        // deviation -> Expected wastage after wastage - actual expiry

        BigDecimal deviationOfWastage = expectedExpiryWastageAfterConsumption.subtract(actualExpiryWastage);

        inventoryData.setExpectedExpiryWastage(lastDayCloseExpectedExpiryWastage);
        inventoryData.setExpectedWastageByNow(lastDayCloseCompleteExpiryWastage);
        inventoryData.setExpectedExpiryWastageAfterConsumption(expectedExpiryWastageAfterConsumption);
        inventoryData.setActualExpiryWastage(actualExpiryWastage);
        inventoryData.setDeviationOfExpiryWastage(deviationOfWastage);
    }

    private void calculateAcknowledgementData(List<ProductStockForUnit> productStockForUnitList, SCMDayCloseEventData dayCloseEvent,StockTakeType stockType) throws SumoException {
        try {
            VarianceAcknowledgementData vad = new VarianceAcknowledgementData();
            vad.setUnitId(dayCloseEvent.getUnitId());
            vad.setBusinessDate(dayCloseEvent.getBusinessDate());
            vad.setFrequency(stockType.value());
            vad.setGenerationTime(SCMUtil.getCurrentTimestamp());
            vad.setAcknowledged(SCMUtil.NO);
            vad.setAcknowledgementType(String.valueOf(StockTakeType.DAILY));
            vad.setAcknowledgementRequired(SCMUtil.NO);
            vad.setCurrentDayCloseEvent(dayCloseEvent);

            BigDecimal totalInventoryCost = BigDecimal.ZERO;
            BigDecimal totalVarianceCost = BigDecimal.ZERO;

            for (ProductStockForUnit productStockForUnit : productStockForUnitList) {
                BigDecimal productCost = BigDecimal.ZERO;
                BigDecimal varianceCost = BigDecimal.ZERO;
//                CostDetailData costDetailData = priceManagementDao.getCurrentPrice(productStockForUnit.getKeyType(), dayCloseEvent.getUnitId(), productStockForUnit.getKeyId(), true).get(0);
                   productCost = SCMUtil.multiplyWithScale(productStockForUnit.getUnitPrice(), productStockForUnit.getOpening(), 6);
                   varianceCost = SCMUtil.multiplyWithScale(productStockForUnit.getUnitPrice(), productStockForUnit.getVariance(), 6);
                   totalInventoryCost = SCMUtil.add(totalInventoryCost, productCost);
                   totalVarianceCost = SCMUtil.add(totalVarianceCost, varianceCost);
            }
            BigDecimal variancePercentage = SCMUtil.multiplyWithScale(SCMUtil.divideWithScale(totalVarianceCost, totalInventoryCost, 6), BigDecimal.valueOf(100), 6);

            Unit unit = masterDataCache.getUnit(dayCloseEvent.getUnitId());
//            vad.setAcknowledgementRequired(SCMUtil.YES);
//            Unit unit = masterDataCache.getUnit(dayCloseEvent.getUnitId());
//
//            BigDecimal percentLimit = props.getVarAckCafePercent();

//            if(Objects.nonNull(variancePercentage) && (variancePercentage.compareTo(percentLimit) > 0 || variancePercentage.compareTo(percentLimit.negate())<0)){
//                vad.setAcknowledgementRequired(SCMUtil.YES);
//            }

            vad.setVarianceCost(totalVarianceCost);
            vad.setInventoryCost(totalInventoryCost);
            vad.setVariancePercentage(variancePercentage);
            stockManagementDao.add(vad, false);
        }catch (Exception e){
            LOG.error("Exception Occurred While adding variance acknowledgement data ::: ",e);
        }
    }

    private void varianceAcknowledgementDetailsAfterEdit(SCMDayCloseEventData dayCloseEvent) throws SumoException {
        try {
            VarianceAcknowledgementData vadData = stockManagementDao.getVarianceAcknowledgementData(dayCloseEvent.getUnitId(),dayCloseEvent.getBusinessDate());
            VarianceAcknowledgementData vad = stockManagementDao.find(VarianceAcknowledgementData.class,vadData.getId());
            vad.setCurrentDayCloseEvent(dayCloseEvent);
            LOG.info("Getting inventory data fro Event : {} and Frequency : {}", dayCloseEvent.getEventId(), dayCloseEvent.getEventFrequencyType());
            List<SCMProductInventoryData> scmProductInventoryDataList = stockManagementDao.getScmProductInventoryDataListWithEventIdAndFrequencyType(dayCloseEvent.getEventId(), dayCloseEvent.getEventFrequencyType());
            if (Objects.nonNull(scmProductInventoryDataList) && !scmProductInventoryDataList.isEmpty()) {
                BigDecimal totalVarianceCost = BigDecimal.ZERO;
                for (SCMProductInventoryData scmProductInventoryData : scmProductInventoryDataList) {
                    totalVarianceCost = SCMUtil.add(totalVarianceCost, scmProductInventoryData.getVarianceCost());
                }
                BigDecimal variancePercentage = SCMUtil.multiplyWithScale(SCMUtil.divideWithScale(totalVarianceCost, vad.getInventoryCost(), 6), BigDecimal.valueOf(100), 6);
                vad.setAcknowledgementRequired(SCMUtil.YES);
//                if (Objects.nonNull(variancePercentage) && (variancePercentage.compareTo(BigDecimal.valueOf(1)) > 0 || variancePercentage.compareTo(BigDecimal.valueOf(-1)) < 0)) {
//                    vad.setAcknowledgementRequired(SCMUtil.YES);
//                }else {
//                    vad.setAcknowledgementRequired(SCMUtil.NO);
//                }
                vad.setVarianceCost(totalVarianceCost);
                vad.setVariancePercentage(variancePercentage);
                stockManagementDao.update(vad, false);
            }
        }catch (Exception e){
            LOG.error("Exception Occurred While adding variance acknowledgement data during variance edit ::: ",e);
        }
    }


    @LogExecutionTime
    private Map<StockEventType, ConsumptionData> getAllRecords(Integer unitId, Date businessDate,
                                                               StockTakeType frequency, Map<Integer, Pair<SCMDayCloseEventData, BigDecimal>> notInLastDayCloseProducts,
                                                               SCMDayCloseEventData lastDayCloseEventData, Map<Integer, BigDecimal> varianceTillNow) {
        Map<StockEventType, ConsumptionData> returnMap = new HashMap<>();
        LOG.info(":: Getting Variance Till Now ::");
        long time = System.currentTimeMillis();
        LOG.info("######### Got Variance Till Now ---------- {}ms",System.currentTimeMillis() - time);
        time = System.currentTimeMillis();
        returnMap.put(StockEventType.TRANSFER_OUT, getAll(StockEventType.TRANSFER_OUT, unitId, businessDate, frequency, lastDayCloseEventData, null, null, notInLastDayCloseProducts.keySet(), varianceTillNow));
        LOG.info("######### Completed Transfer Out ---------- {}ms",System.currentTimeMillis() - time);
        time = System.currentTimeMillis();
        returnMap.put(StockEventType.RECEIVED, getAll(StockEventType.RECEIVED, unitId, businessDate, frequency, lastDayCloseEventData, null, null, notInLastDayCloseProducts.keySet(), varianceTillNow));
        LOG.info("######### Completed RECEIVED ---------- {}ms",System.currentTimeMillis() - time);
        time = System.currentTimeMillis();
        returnMap.put(StockEventType.WASTAGE, getAll(StockEventType.WASTAGE, unitId, businessDate, frequency, lastDayCloseEventData, null, null, notInLastDayCloseProducts.keySet(), varianceTillNow));
        LOG.info("######### Completed WASTAGE ---------- {}ms",System.currentTimeMillis() - time);
        time = System.currentTimeMillis();
        returnMap.put(StockEventType.CONSUMPTION, getAll(StockEventType.CONSUMPTION, unitId, businessDate, frequency, lastDayCloseEventData, null, null, notInLastDayCloseProducts.keySet(), varianceTillNow));
        LOG.info("######### Completed CONSUMPTION ---------- {}ms",System.currentTimeMillis() - time);
        time = System.currentTimeMillis();
        LOG.info(" grouping Products Based on the Day Close Event :: ");
        if (!notInLastDayCloseProducts.isEmpty()) {
            LOG.info(" ::: Getting Values For Products Which are not in Last Day Close :::");
            for (Map.Entry<StockEventType, ConsumptionData> consumptionType: returnMap.entrySet()) {
                LOG.info("Running to get the values for : {}", consumptionType.getKey());
                    returnMap.put(consumptionType.getKey(),
                            getAll(consumptionType.getKey(), unitId, businessDate, frequency, lastDayCloseEventData ,
                                    new ArrayList<>(notInLastDayCloseProducts.keySet()), consumptionType.getValue(), notInLastDayCloseProducts.keySet(), varianceTillNow));
                LOG.info("Completed Getting Values for : {}", consumptionType.getKey());
            }
            LOG.info("######### Completed Getting Values For Products Which are not in Last Day Close ---------- {}ms",System.currentTimeMillis() - time);
        }
        return returnMap;
    }

    private Map<Integer, List<Integer>> getGroupedNotInLastDayCloseProductList(Map<Integer, Pair<SCMDayCloseEventData, BigDecimal>> notInLastDayCloseProducts, SCMDayCloseEventData lastDayCloseEventData) {
        Map<Integer, List<Integer>> result = new HashMap<>();
        for (Map.Entry<Integer, Pair<SCMDayCloseEventData, BigDecimal>> product : notInLastDayCloseProducts.entrySet()) {
            SCMDayCloseEventData dayCloseEventData = lastDayCloseEventData;
            if (Objects.nonNull(product.getValue().getKey())) {
                dayCloseEventData = product.getValue().getKey();
            }
            List<Integer> productsIds = null;
            if (result.containsKey(dayCloseEventData.getEventId())) {
                productsIds = result.get(dayCloseEventData.getEventId());
            } else {
                productsIds = new ArrayList<>();
            }
            productsIds.add(product.getKey());
            result.put(dayCloseEventData.getEventId(), productsIds);
        }
        return result;
    }

    @LogExecutionTime
    private Map<Integer, SCMProductConsumptionData> settleConsumption(SCMDayCloseEventData stockEvent,
                                                                      StockTakeType frequency, Map<Integer, Pair<SCMDayCloseEventData, BigDecimal>> notInLastDayCloseProducts,
                                                                      SCMDayCloseEventData lastDayCloseEvent, Map<Integer, BigDecimal> varianceTillNowMap, Map<Integer, BigDecimal> openingInventoryData,
                                                                      List<Integer> inActiveProductsList, List<Integer> inventorySnapShotKeyIds) throws SumoException {
//        Map<Integer, ProductBasicDetail> productList = getAllProductIdsByFrequency(frequency, false);
//        List<Integer> unavailableProductIds = getUnavailableProductIds(new ArrayList<>(productList.keySet()), stockEvent.getUnitId(), new HashMap<>());
//        List<ProductBasicDetail> finalProductList = productList.values().stream().filter(productBasicDetail -> !unavailableProductIds.contains(productBasicDetail.getProductId())).collect(Collectors.toList());

//        List<ProductBasicDetail> finalProductList = new ArrayList<>();
        Map<Integer, ProductBasicDetail> productList = scmCache.getProductDefinitions().values().stream()
                .filter(productDefinition -> inventorySnapShotKeyIds.contains(productDefinition.getProductId()))
                .collect(Collectors.toMap(ProductDefinition::getProductId,
                        SCMDataConverter::convert));

//        productList = finalProductList.stream()
//                .collect(Collectors.toMap(ProductBasicDetail::getProductId, Function.identity()));
        Map<Integer, BigDecimal> inventoryList = new HashMap<>();
        if (Objects.nonNull(lastDayCloseEvent)) {
            inventoryList = stockManagementDao.getStockForUnit(stockEvent.getUnitId(), lastDayCloseEvent.getEventId());
        }
        Map<Integer, Pair<SCMDayCloseEventData, BigDecimal>> productsNotInList = getNotInLastDayCloseProducts(inventoryList, productList);
        notInLastDayCloseProducts.putAll(productsNotInList);
        Map<StockEventType, ConsumptionData> recordMap = getAllRecords(stockEvent.getUnitId(),
                stockEvent.getBusinessDate(), frequency, notInLastDayCloseProducts, lastDayCloseEvent, varianceTillNowMap);

        if (recordMap.get(StockEventType.TRANSFER_OUT) != null) {
            stockManagementDao.createDayCloseEventRecord(stockEvent,
                    recordMap.get(StockEventType.TRANSFER_OUT).getStartOrderId(),
                    recordMap.get(StockEventType.TRANSFER_OUT).getEndOrderId(), StockEventType.TRANSFER_OUT, frequency);
            createDayCloseFrequencyMismatchProduct(recordMap.get(StockEventType.TRANSFER_OUT), frequency, stockEvent, StockEventType.TRANSFER_OUT);
        }
        if (recordMap.get(StockEventType.RECEIVED) != null) {
            stockManagementDao.createDayCloseEventRecord(stockEvent,
                    recordMap.get(StockEventType.RECEIVED).getStartOrderId(),
                    recordMap.get(StockEventType.RECEIVED).getEndOrderId(), StockEventType.RECEIVED, frequency);
            createDayCloseFrequencyMismatchProduct(recordMap.get(StockEventType.RECEIVED), frequency, stockEvent, StockEventType.RECEIVED);
        }
        if (recordMap.get(StockEventType.WASTAGE) != null) {
            stockManagementDao.createDayCloseEventRecord(stockEvent,
                    recordMap.get(StockEventType.WASTAGE).getStartOrderId(),
                    recordMap.get(StockEventType.WASTAGE).getEndOrderId(), StockEventType.WASTAGE, frequency);
            createDayCloseFrequencyMismatchProduct(recordMap.get(StockEventType.WASTAGE), frequency, stockEvent, StockEventType.WASTAGE);
        }
        if (recordMap.get(StockEventType.CONSUMPTION) != null) {
            stockManagementDao.createDayCloseEventRecord(stockEvent,
                    recordMap.get(StockEventType.CONSUMPTION).getStartOrderId(),
                    recordMap.get(StockEventType.CONSUMPTION).getEndOrderId(), StockEventType.CONSUMPTION, frequency);
            createDayCloseFrequencyMismatchProduct(recordMap.get(StockEventType.CONSUMPTION), frequency, stockEvent, StockEventType.CONSUMPTION);
        }

        return getRecords(recordMap, stockEvent,
                new HashMap<>(), true, frequency, openingInventoryData, inActiveProductsList);
    }

    private void createDayCloseFrequencyMismatchProduct(ConsumptionData consumptionData, StockTakeType frequency, SCMDayCloseEventData stockEvent, StockEventType eventType) {
        try {
            List<DayCloseFrequencyMismatchProducts> dayCloseFrequencyMismatchProductsList = new ArrayList<>();
            if (Objects.nonNull(consumptionData) && Objects.nonNull(consumptionData.getDayCloseFrequencyMismatchList())
            & !consumptionData.getDayCloseFrequencyMismatchList().isEmpty()) {
                for (DayCloseFrequencyMismatch mismatch : consumptionData.getDayCloseFrequencyMismatchList()) {
                    DayCloseFrequencyMismatchProducts mismatchProducts = new DayCloseFrequencyMismatchProducts();
                    mismatchProducts.setProductId(mismatch.getProductId());
                    mismatchProducts.setUnitId(stockEvent.getUnitId());
                    mismatchProducts.setEventId(stockEvent.getEventId());
                    mismatchProducts.setStartId(mismatch.getStartOrderId());
                    mismatchProducts.setEndId(mismatch.getEndOrderId());
                    mismatchProducts.setStockType(frequency.value());
                    mismatchProducts.setType(eventType.value());
                    dayCloseFrequencyMismatchProductsList.add(mismatchProducts);
                }
            }
            if (!dayCloseFrequencyMismatchProductsList.isEmpty()) {
                stockManagementDao.addAll(dayCloseFrequencyMismatchProductsList);
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While adding the Day Close Frequency Mis Matched Products ::: ",e);
        }
    }

    @LogExecutionTime
    private Map<Integer, SCMProductConsumptionData> getRecords(Map<StockEventType, ConsumptionData> allRecords,
                                                               SCMDayCloseEventData eventData, Map<Integer, SCMProductConsumptionData> productConsumptionDataList,
                                                               boolean update, StockTakeType stockType, Map<Integer, BigDecimal> openingInventoryData, List<Integer> inActiveProductsList) throws SumoException {

        productConsumptionDataList = productConsumptionDataList != null ? productConsumptionDataList : new HashMap<>();

        ConsumptionData transferOut = allRecords.get(StockEventType.TRANSFER_OUT);
        ConsumptionData received = allRecords.get(StockEventType.RECEIVED);
        ConsumptionData wastage = allRecords.get(StockEventType.WASTAGE);
        ConsumptionData consumption = allRecords.get(StockEventType.CONSUMPTION);

        List<String> missingTaxesProducts = new ArrayList<>();
        if (consumption != null && consumption.getConsumables() != null) {
            for (Consumable consumptionData : consumption.getConsumables()) {
                addToProductConsumption(productConsumptionDataList, consumptionData, eventData,
                        StockEventType.CONSUMPTION, missingTaxesProducts);
            }
        }

        if (transferOut != null && transferOut.getConsumables() != null) {
            for (Consumable consumptionData : transferOut.getConsumables()) {
                addToProductConsumption(productConsumptionDataList, consumptionData, eventData,
                        StockEventType.TRANSFER_OUT, missingTaxesProducts);
            }
        }

        if (received != null && received.getConsumables() != null) {
            for (Consumable consumptionData : received.getConsumables()) {
                addToProductConsumption(productConsumptionDataList, consumptionData, eventData,
                        StockEventType.RECEIVED, missingTaxesProducts);
            }
        }

        if (wastage != null && wastage.getConsumables() != null) {
            for (Consumable consumptionData : wastage.getConsumables()) {
                addToProductConsumption(productConsumptionDataList, consumptionData, eventData, StockEventType.WASTAGE, missingTaxesProducts);
            }
            updateProductExpiryData(productConsumptionDataList, wastage);
        }

        if (!missingTaxesProducts.isEmpty()) {
            throw new SumoException("Missing Tax Data For Products  " , "Missing Tax Data For Products : " + Arrays.asList(missingTaxesProducts.toArray()));
        }

        if (update) {
            return updateConsumptionData(stockType, productConsumptionDataList, openingInventoryData, inActiveProductsList);
        } else {
            return productConsumptionDataList;
        }
    }

    private void updateProductExpiryData(Map<Integer, SCMProductConsumptionData> productConsumptionDataMap, ConsumptionData wastage) {
        if (Objects.nonNull(wastage.getExpiredWastage()) && !wastage.getExpiredWastage().isEmpty()) {
            for (Map.Entry<Integer, BigDecimal> entry : wastage.getExpiredWastage().entrySet()) {
                SCMProductConsumptionData productConsumptionData = productConsumptionDataMap.get(entry.getKey());
                if (Objects.nonNull(productConsumptionData)) {
                    productConsumptionData.setExpiredWastage(entry.getValue());
                }
                productConsumptionDataMap.put(entry.getKey(), productConsumptionData);
            }
        }
    }

    private int getProductId(int skuId) {
        SkuDefinition definition = scmCache.getSkuDefinitions().get(skuId);
        if (definition == null) {
            SkuDefinitionData skuDefinitionData = productManagementDao.find(SkuDefinitionData.class, skuId);
            IdCodeName createdBy = SCMUtil.generateIdCodeName(skuDefinitionData.getCreatedBy(), "",
                    masterDataCache.getEmployees().get(skuDefinitionData.getCreatedBy()));
            definition = SCMDataConverter.convert(skuDefinitionData, createdBy);
            scmCache.getSkuDefinitions().put(skuId, definition);
        }
        return definition.getLinkedProduct().getId();
    }

    private BigDecimal calculateConsumptionForProduct(SCMProductConsumptionData consumptionData,
                                                      BigDecimal openingStock, ProductStockForUnit stockForUnit) {
        BigDecimal received = SCMUtil.convertToBigDecimal(consumptionData.getReceived());
        BigDecimal consumed = SCMUtil.convertToBigDecimal(consumptionData.getConsumption());
        BigDecimal transferred = SCMUtil.convertToBigDecimal(consumptionData.getTransferOut());
        BigDecimal wastage = SCMUtil.convertToBigDecimal(consumptionData.getWastage());
        BigDecimal stockAtHand = SCMUtil.convertToBigDecimal(openingStock).add(received);
        BigDecimal consumedValue = consumed.add(transferred).add(wastage);
        return stockAtHand.subtract(consumedValue);
    }

    @LogExecutionTime
    private ConsumptionData getAll(StockEventType eventType, int unitId, Date businessDate, StockTakeType frequency, SCMDayCloseEventData lastDayCloseEventData,
                                   List<Integer> currentProductIds, ConsumptionData availableConsumptionData, Set<Integer> excludeProductIds, Map<Integer, BigDecimal> varianceTillNow) {
        Date currentTime = SCMUtil.getCurrentTimestamp();
        if (lastDayCloseEventData != null) {
            if (lastDayCloseEventData.getGenerationTime() == null || businessDate == null) {
                return new ConsumptionData();
            }

            Date startTime = lastDayCloseEventData.getGenerationTime();
            Date previousDate = lastDayCloseEventData.getBusinessDate();
            ConsumptionData consumptionData = null;

            switch (eventType) {
                case TRANSFER_OUT:
                    consumptionData = getTransfersForBusinessDate(unitId, currentTime, startTime, currentProductIds, availableConsumptionData, excludeProductIds, varianceTillNow);
                    break;
                case RECEIVED:
                    consumptionData = getGoodsReceivedForBusinessDate(unitId, currentTime, startTime, currentProductIds, availableConsumptionData, excludeProductIds, varianceTillNow);
                    break;
                case WASTAGE:
                    consumptionData = getWastageEventsForBusinessDate(unitId, currentTime, startTime, currentProductIds, availableConsumptionData, excludeProductIds, varianceTillNow);
                    break;
                case CONSUMPTION:
                    consumptionData = getConsumptionForBusinessDate(unitId, businessDate, previousDate, currentProductIds, availableConsumptionData, excludeProductIds, varianceTillNow);
                    break;
                default:
                    break;
            }
            return consumptionData;
        }
        return null;
    }

    private ConsumptionData getConsumptionForBusinessDate(int unitId, Date businessDate, Date previousDate, List<Integer> currentProductIds,
                                                          ConsumptionData availableConsumptionData, Set<Integer> excludeProductIds, Map<Integer, BigDecimal> varianceTillNow) {
        LOG.info("Getting The Product Consumption Data :: for unit id : {}", unitId);
        List<SCMProductConsumptionData> productConsumptionDataList = stockManagementDao
                .getConsumptionForBusinessDate(unitId, businessDate, previousDate, currentProductIds);
        LOG.info("Completed Getting The Product Consumption Data :: for unit id : {}", unitId);
        return getConsumptionData(productConsumptionDataList, unitId, businessDate, currentProductIds, availableConsumptionData, excludeProductIds, varianceTillNow);

    }

    private ConsumptionData getConsumptionData(List<SCMProductConsumptionData> productConsumptionDataList, int unitId,
                                               Date businessDate, List<Integer> currentProductIds, ConsumptionData availableConsumptionData, Set<Integer> excludeProductIds, Map<Integer, BigDecimal> varianceTillNow) {
        if (productConsumptionDataList != null && productConsumptionDataList.size() > 0) {
            if (Objects.nonNull(availableConsumptionData)) {
                Map<Integer, Consumable> consumableMap = new HashMap<>();
                for (SCMProductConsumptionData consumption : productConsumptionDataList) {
                    int productId = consumption.getProductId();
                    if (excludeProductIds.contains(productId)) {
                        addProductConsumption(consumableMap, consumption, productId, Objects.nonNull(varianceTillNow) ? varianceTillNow.getOrDefault(productId, BigDecimal.ZERO) : BigDecimal.ZERO);
                    }
                }
                for (Map.Entry<Integer,Consumable> entry : consumableMap.entrySet()) {
                    DayCloseFrequencyMismatch dayCloseFrequencyMismatch = DayCloseFrequencyMismatch.builder().businessDate(availableConsumptionData.getBusinessDate())
                            .productId(entry.getKey()).unitId(availableConsumptionData.getUnitId()).startOrderId(productConsumptionDataList.get(0).getProductConsumptionId())
                            .endOrderId(productConsumptionDataList.get(productConsumptionDataList.size() - 1).getProductConsumptionId()).build();
                    availableConsumptionData.getDayCloseFrequencyMismatchList().add(dayCloseFrequencyMismatch);
                }
                availableConsumptionData.getConsumables().addAll(consumableMap.values());
                return availableConsumptionData;
            } else {
                ConsumptionData consumptionData = new ConsumptionData();
                consumptionData.setBusinessDate(businessDate);
                consumptionData.setUnitId(unitId);
                consumptionData.setStartOrderId(productConsumptionDataList.get(0).getProductConsumptionId());
                consumptionData.setEndOrderId(
                        productConsumptionDataList.get(productConsumptionDataList.size() - 1).getProductConsumptionId());
                consumptionData.setClosureId(productConsumptionDataList.get(0).getEventId().getEventId());
                Map<Integer, Consumable> consumableMap = new HashMap<>();
                for (SCMProductConsumptionData consumption : productConsumptionDataList) {
                    int productId = consumption.getProductId();
                    if (!excludeProductIds.contains(productId)) {
                        addProductConsumption(consumableMap, consumption, productId, Objects.nonNull(varianceTillNow) ? varianceTillNow.getOrDefault(productId, BigDecimal.ZERO) : BigDecimal.ZERO);
                    }
                }
                consumptionData.getConsumables().addAll(consumableMap.values());
                return consumptionData;
            }
        } else {
            if (Objects.nonNull(availableConsumptionData)) {
                return availableConsumptionData;
            }
            return null;
        }
    }

    private void addProductConsumption(Map<Integer, Consumable> consumableMap, SCMProductConsumptionData consumption, int productId, BigDecimal varianceTillNow) {
        Consumable consumable = consumableMap.get(productId);
        if (consumption.getConsumption() != null) {
            if (consumable == null) {
                consumable = new Consumable();
                consumable.setId(consumption.getProductConsumptionId());
                consumable.setQuantity(SCMUtil.convertToBigDecimal(consumption.getConsumption()));
                consumable.setUom(consumption.getUnitOfMeasure());
                consumable.setProductId(productId);
                consumable.setCost(consumption.getConsumptionCost());
                consumable.setTaxPercentage(consumption.getTaxPercentage());
                consumable.setVarianceTillNow(varianceTillNow);
            } else {
                consumable.setQuantity(consumable.getQuantity()
                        .add(SCMUtil.convertToBigDecimal(consumption.getConsumption())));
                consumable.setCost(AppUtils.add(consumable.getCost(), (consumption.getConsumptionCost())));
            }
            consumableMap.put(productId, consumable);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public ConsumptionData loadConsumptionData(int unitId, Date businessDate,int kettleClosureId) {
        LOG.info("loading consumption for unit: {} for business date :{}",unitId,businessDate);
        Integer event = stockManagementDao.getSCMDayCloseEventForBusinessDateForClosingType(unitId, businessDate);
		LOG.info("fetched the eventId :{}",event);
		List<SCMProductConsumptionData> productConsumptionDataList = stockManagementDao.getConsumption(event);
        LOG.info("scm consumption size is:{}",productConsumptionDataList.size());
		if (productConsumptionDataList != null && productConsumptionDataList.size() > 0) {
			ConsumptionData consumptionData = new ConsumptionData();
			consumptionData.setBusinessDate(businessDate);
			consumptionData.setUnitId(unitId);
			consumptionData.setStartOrderId(productConsumptionDataList.get(0).getProductConsumptionId());
			consumptionData.setEndOrderId(
					productConsumptionDataList.get(productConsumptionDataList.size() - 1).getProductConsumptionId());
			consumptionData.setClosureId(productConsumptionDataList.get(0).getEventId().getEventId());
			Map<Integer, Consumable> consumableMap = new HashMap<>();
			for (SCMProductConsumptionData consumption : productConsumptionDataList) {
				int productId = consumption.getProductId();
				Consumable consumable = consumableMap.get(productId);
				if (consumption.getConsumption() != null) {
					if (consumable == null) {
						consumable = new Consumable();
						consumable.setId(consumption.getProductConsumptionId());
						consumable.setQuantity(SCMUtil.convertToBigDecimal(consumption.getConsumption()));
						consumable.setUom(consumption.getUnitOfMeasure());
						consumable.setProductId(productId);
						consumable.setCost(consumption.getConsumptionCost());
						consumable.setTaxPercentage(consumption.getTaxPercentage());
						consumable.setPrice(consumption.getConsumptionPrice());
						consumable.setTaxableQuantity(SCMUtil.convertToBigDecimal(consumption.getTaxableConsumption()));
					} else {
						consumable.setQuantity(consumable.getQuantity()
								.add(SCMUtil.convertToBigDecimal(consumption.getConsumption())));
						consumable.setTaxableQuantity(consumable.getTaxableQuantity()
								.add(SCMUtil.convertToBigDecimal(consumption.getTaxableConsumption())));
						consumable.setCost(AppUtils.add(consumable.getCost(), (consumption.getConsumptionCost())));
					}
					consumableMap.put(productId, consumable);
				}
			}
			consumptionData.getConsumables().addAll(consumableMap.values());
			consumptionData.setClosureId(kettleClosureId);
			return consumptionData;
		} else {
			return null;
		}
	}

    private List<SCMProductConsumptionData> createConsumptionRecords(ConsumptionData consumption,
                                                                     SCMDayCloseEventData eventData, StockTakeType stockType) throws SumoException {
        List<SCMProductConsumptionData> scmProductConsumptionDataList = new ArrayList<>();
        Map<Integer, SCMProductConsumptionData> consumptionDataMap = new HashMap<>();

        List<String> missingTaxesProducts = new ArrayList<>();
        if (consumption != null && consumption.getConsumables() != null) {
            for (Consumable consumptionData : consumption.getConsumables()) {
                addToProductConsumption(consumptionDataMap, consumptionData, eventData, StockEventType.CONSUMPTION, missingTaxesProducts);
            }
        }

        if (!missingTaxesProducts.isEmpty()) {
            throw new SumoException("Missing Tax Data For Products  " , "Missing Tax Data For Products : " + Arrays.asList(missingTaxesProducts.toArray()));
        }

        if (consumption != null) {
            if (consumption.getCodConsumable() != null) {
                addConsumptionDrillDown(consumption.getCodConsumable(), eventData, AppConstants.COD);
            }
            if (consumption.getCafeConsumables() != null) {
                addConsumptionDrillDown(consumption.getCafeConsumables(), eventData, AppConstants.CAFE);
            }
            if (consumption.getEmployeeMealConsumable() != null) {
                addConsumptionDrillDown(consumption.getEmployeeMealConsumable(), eventData,
                        AppConstants.ORDER_TYPE_EMPLOYEE_MEAL);
            }
        }

        if (consumptionDataMap != null && consumptionDataMap.size() > 0) {
            try {
                for (SCMProductConsumptionData productConsumptionData : consumptionDataMap.values()) {
                    productConsumptionData.setStockType(stockType.name());
                    stockManagementDao.add(productConsumptionData, false);
                    scmProductConsumptionDataList.add(productConsumptionData);
                }
                stockManagementDao.flush();
            } catch (Exception e) {
                LOG.error("got error while adding consumption data", e);
                throw e;
            }
        }

        return scmProductConsumptionDataList;
    }

    private void addConsumptionDrillDown(List<Consumable> list, SCMDayCloseEventData event, String type)
            throws SumoException {
        for (Consumable c : list) {
            SCMProductConsumptionDrillDown d = new SCMProductConsumptionDrillDown();
            d.setConsumption(c.getQuantity());
            d.setTaxableConsumption(c.getTaxableQuantity());
            d.setProductId(c.getProductId());
            d.setUnitId(event.getUnitId());
            d.setConsumptionType(type);
            d.setEventId(event);
            d.setUnitOfMeasure(c.getUom());
            stockManagementDao.add(d, false);
        }
    }

    private Map<Integer, SCMProductConsumptionData> updateConsumptionData(StockTakeType stockType,
                                                                          Map<Integer, SCMProductConsumptionData> productConsumptionDataList, Map<Integer, BigDecimal> openingInventoryData,
                                                                          List<Integer> inActiveProductsList) throws SumoException {
        try {
            List<SCMProductConsumptionData> needToUpdateList = new ArrayList<>();
            List<SCMProductConsumptionData> needToAddList = new ArrayList<>();
            for (SCMProductConsumptionData productConsumptionData : productConsumptionDataList.values()) {
                productConsumptionData.setStockType(stockType.name());
                if (productConsumptionData.getProductConsumptionId() != null) {
                    needToUpdateList.add(productConsumptionData);
                } else {
                    needToAddList.add(productConsumptionData);
                }
            }
            if (!needToUpdateList.isEmpty()) {
                stockManagementDao.update(needToUpdateList, true);
            }
            if (!needToAddList.isEmpty()) {
                //TODO : To delete No movement Products from Cost Detail Data during Monthly Inventory time of 0 stock .
//                if (Objects.nonNull(openingInventoryData)) {
//                    LOG.info("Before Filtering Of Consumption Data Size Of need To Add List is : {}", needToAddList.size());
//                    needToAddList = filterMovementProductsNeedToAdd(needToAddList, openingInventoryData, inActiveProductsList);
//                    LOG.info("After Filtering Of Consumption Data Size Of need To Add List is : {} and Products Of Inactive List Are : {} ", needToAddList.size(), Arrays.toString(inActiveProductsList.toArray()));
//                }
                stockManagementDao.addAll(needToAddList);
            }
            return productConsumptionDataList;
        } catch (Exception e) {
            LOG.error("got error while updating consumption data", e);
            throw e;
        }
    }

    private List<SCMProductConsumptionData> filterMovementProductsNeedToAdd(List<SCMProductConsumptionData> needToAddList, Map<Integer, BigDecimal> openingInventoryData, List<Integer> inActiveProductsList) {
        List<SCMProductConsumptionData> result = new ArrayList<>();
        for (SCMProductConsumptionData productConsumptionData : needToAddList) {
            ProductDefinition productDefinition = scmCache.getProductDefinition(productConsumptionData.getProductId());
            if (!productDefinition.getProductStatus().equals(ProductStatus.ACTIVE)) {
                if (openingInventoryData.getOrDefault(productConsumptionData.getProductId(), BigDecimal.ZERO).compareTo(BigDecimal.ZERO) != 0) {
                    result.add(productConsumptionData);
                } else if (Objects.nonNull(productConsumptionData.getReceived()) && productConsumptionData.getReceived().compareTo(BigDecimal.ZERO) != 0) {
                    result.add(productConsumptionData);
                } else if (Objects.nonNull(productConsumptionData.getTransferOut()) && productConsumptionData.getTransferOut().compareTo(BigDecimal.ZERO) != 0) {
                    result.add(productConsumptionData);
                } else if (Objects.nonNull(productConsumptionData.getWastage()) && productConsumptionData.getWastage().compareTo(BigDecimal.ZERO) != 0) {
                    result.add(productConsumptionData);
                } else if (Objects.nonNull(productConsumptionData.getConsumption()) && productConsumptionData.getConsumption().compareTo(BigDecimal.ZERO) != 0) {
                    result.add(productConsumptionData);
                } else {
                    inActiveProductsList.add(productConsumptionData.getProductId());
                }
            } else {
                result.add(productConsumptionData);
            }
        }
        return result;
    }

    private void addToProductConsumption(Map<Integer, SCMProductConsumptionData> productConsumptionDataList,
                                         Consumable consumable, SCMDayCloseEventData eventData, StockEventType eventType,
                                         List<String> missingTaxesProducts) {

        SCMProductConsumptionData productConsumptionData = null;
        if(Objects.isNull(consumable.getProductId())){
            LOG.info("consumable : {} ", new Gson().toJson(consumable));
        }
        int productId = consumable.getProductId();
        State state = masterDataCache.getUnit(eventData.getUnitId()).getLocation().getState();
        if(scmCache.getProductDefinition(productId) ==null){
            LOG.info("consumable tax: {} ", new Gson().toJson(consumable));
        }
        String taxCode = scmCache.getProductDefinition(productId).getTaxCode();
        TaxData taxData = taxCache.getTaxData(state.getId(), taxCode);
        ProductDefinition productDefinition = scmCache.getProductDefinition(productId);
        if (Objects.isNull(taxData)) {
            LOG.info("Tax Data is Missing for Product Id : {} : {}", productId,productDefinition.getProductName());
            missingTaxesProducts.add(productDefinition.getProductName() + " [ " + productId + "  ]");
            return;
        }
        if (Objects.isNull(taxData.getState())) {
            LOG.info("Tax Data(State) is Missing for Product Id : {} : {}", productId,productDefinition.getProductName());
            missingTaxesProducts.add(productDefinition.getProductName() + " [ " + productId + "  ]");
            return;
        }
        if (!productConsumptionDataList.containsKey(productId)) {
            productConsumptionData = new SCMProductConsumptionData();
            productConsumptionData.setProductId(productId);
            productConsumptionData.setUnitOfMeasure(consumable.getUom());
            productConsumptionData.setUnitId(eventData.getUnitId());
            productConsumptionData.setEventId(eventData);
            productConsumptionData.setBusinessDate(eventData.getBusinessDate());
        } else {
            productConsumptionData = productConsumptionDataList.get(productId);
        }

        switch (eventType) {
            case CONSUMPTION:
                productConsumptionData.setConsumption(consumable.getQuantity());
                productConsumptionData.setTaxableConsumption(consumable.getTaxableQuantity());
                productConsumptionData.setConsumptionCost(consumable.getCost());
                productConsumptionData
                        .setConsumptionPrice(AppUtils.divideWithScale10(consumable.getCost(), consumable.getQuantity()));
                productConsumptionData.setTaxableAmount(AppUtils.multiplyWithScale10(consumable.getTaxableQuantity(),
                        AppUtils.divideWithScale10(consumable.getCost(), consumable.getQuantity())));
                productConsumptionData.setTaxPercentage(SCMUtil.convertToBigDecimal(taxData.getState().getIgst()));
                productConsumptionData.setTaxAmount(AppUtils.percentageOf(productConsumptionData.getTaxPercentage(),
                        productConsumptionData.getTaxableAmount()));
                productConsumptionData.setVarianceTillNow(SCMUtil.convertToBigDecimal(consumable.getVarianceTillNow()));
                break;
            case TRANSFER_OUT:
                productConsumptionData.setTransferOut(consumable.getQuantity());
                productConsumptionData.setTransferOutCost(consumable.getCost());
                productConsumptionData
                        .setTransferOutPrice(AppUtils.divideWithScale10(consumable.getCost(), consumable.getQuantity()));
                productConsumptionData.setVarianceTillNow(SCMUtil.convertToBigDecimal(consumable.getVarianceTillNow()));
                break;
            case RECEIVED:
                productConsumptionData.setReceived(consumable.getQuantity());
                productConsumptionData.setReceivedCost(consumable.getCost());
                productConsumptionData
                        .setReceivedPrice(AppUtils.divideWithScale10(consumable.getCost(), consumable.getQuantity()));
                productConsumptionData.setVarianceTillNow(SCMUtil.convertToBigDecimal(consumable.getVarianceTillNow()));
                break;
            case WASTAGE:
                productConsumptionData.setWastage(consumable.getQuantity());
                productConsumptionData.setWastageCost(consumable.getCost());
                productConsumptionData
                        .setWastagePrice(AppUtils.divideWithScale10(consumable.getCost(), consumable.getQuantity()));
                productConsumptionData.setVarianceTillNow(SCMUtil.convertToBigDecimal(consumable.getVarianceTillNow()));
                break;
            default:
                break;
        }
        productConsumptionDataList.put(productId, productConsumptionData);
    }

    public List<Pair<StockTakeType, Integer>> getLatestClosureEvents(Integer unit) {
        return stockManagementDao.getLatestClosureEvents(unit, null, false);
    }

    public Map<Integer, BigDecimal> getCurrentStock(Integer unit) {
        return stockManagementDao.getCurrentStock(unit);
    }

    @Override
    public Map<Integer, BigDecimal> getLiveStock(Integer unit) throws URISyntaxException {
        String unitZone =masterDataCache.getUnitBasicDetail(unit).getUnitZone();
        String endpoint = props.getInventoryServiceBasePath() +
                InventoryServiceClientEndpoints.INVENTORY_SERVICE_ENTRY_POINT
                + (Objects.nonNull(unitZone) ? unitZone.toLowerCase() : AppConstants.DEFAULT_UNIT_ZONE) +
                InventoryServiceClientEndpoints.INVENTORY_SERVICE_VERSION +
                InventoryServiceClientEndpoints.GET_SCM_PRODUCT_INVENTORY_TRIMMED;
        Map<String, Integer> uriVariables = new HashMap<>();
        uriVariables.put("unitId", unit);
        Map result = WebServiceHelper.exchangeWithAuth(endpoint, props.getAuthToken(), HttpMethod.GET, Map.class, null, uriVariables);
        Map<Integer, BigDecimal> liveStock = new HashMap<>();
        result.forEach((o, o2) -> {
            if(o2 instanceof  Double){
                liveStock.put(Integer.valueOf(o.toString()), BigDecimal.valueOf((double)o2));
            }
            if(o2 instanceof  Integer){
                liveStock.put(Integer.valueOf(o.toString()), BigDecimal.valueOf((int)o2));
            }

        });
        return liveStock;
    }

    @Override
    public PriceManagementDao getPriceDao() {
        return goodsReceiveManagementService.getPriceDao();
    }

    @Override
    public InventoryService getInventoryService() {
        return inventoryService;
    }

    @Override
    public MasterDataCache getMasterDataCache() {
        return masterDataCache;
    }

    @Override
    public EnvProperties getProperties() {
        return props;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void calculateWastageAggregate(BudgetDetail budgetDetail, UnitDetail unitDetail, Date businessDate,
                                          StockTakeType type) {

        List<SCMWastageData> items = stockManagementDao
                .getWastageEventListByDayCloseEventRangeData(unitDetail.getUnitId(), businessDate, type);

        BigDecimal unsatifiedCustomerCost = BigDecimal.ZERO;
        BigDecimal ppeCost = BigDecimal.ZERO;
        BigDecimal expiryWastage = BigDecimal.ZERO;
        BigDecimal wastageOther = BigDecimal.ZERO;
        BigDecimal marketingAndSampling = BigDecimal.ZERO;
        BigDecimal trainingCogs = BigDecimal.ZERO;
        BigDecimal unsatifiedCustomerCostTax = BigDecimal.ZERO;
        BigDecimal ppeCostTax = BigDecimal.ZERO;
        BigDecimal expiryWastageTax = BigDecimal.ZERO;
        BigDecimal wastageOtherTax = BigDecimal.ZERO;
        BigDecimal marketingAndSamplingTax = BigDecimal.ZERO;
        BigDecimal trainingCogsTax = BigDecimal.ZERO;

        //TODO calculate wastage tax here
        for (SCMWastageData item : items) {
            if (SCMServiceConstants.WASTAGE_TYPE_UNSATISFIED_CUSTOMER.equalsIgnoreCase(item.getComment())) {
                unsatifiedCustomerCost = unsatifiedCustomerCost.add(item.getCost());
                unsatifiedCustomerCostTax = AppUtils.add(unsatifiedCustomerCostTax, item.getTax());
            } else if (SCMServiceConstants.WASTAGE_TYPE_PPE.equalsIgnoreCase(item.getComment())) {
                ppeCost = ppeCost.add(item.getCost());
                ppeCostTax = AppUtils.add(ppeCostTax, item.getTax());
            } else if (SCMServiceConstants.WASTAGE_TYPE_EXPIRY.equalsIgnoreCase(item.getComment())) {
                expiryWastage = expiryWastage.add(item.getCost());
                expiryWastageTax = AppUtils.add(expiryWastageTax, item.getTax());
            } else if (SCMServiceConstants.WASTAGE_TYPE_SAMPLING_AND_MARKETING.equalsIgnoreCase(item.getComment())) {
                marketingAndSampling = marketingAndSampling.add(item.getCost());
                marketingAndSamplingTax = AppUtils.add(marketingAndSamplingTax, item.getTax());
            } else if (SCMServiceConstants.WASTAGE_TYPE_TRAINING.equalsIgnoreCase(item.getComment())) {
                trainingCogs = trainingCogs.add(item.getCost());
                trainingCogsTax = AppUtils.add(trainingCogsTax, item.getTax());
            } else {
                wastageOther = wastageOther.add(item.getCost());
                wastageOtherTax = AppUtils.add(wastageOtherTax, item.getTax());
            }
        }

        budgetDetail.setWastage(new WastageAggregate());
        budgetDetail.getWastage().setExpiryWastage(expiryWastage);
        budgetDetail.getWastage().setMarketingAndSampling(marketingAndSampling);
        budgetDetail.getWastage().setUnsatifiedCustomerCost(unsatifiedCustomerCost);
        budgetDetail.getWastage().setPPECost(ppeCost);
        budgetDetail.getWastage().setWastageOther(wastageOther);
        budgetDetail.getWastage().setTrainingCogs(trainingCogs);
        budgetDetail.getWastage().setExpiryWastageTax(expiryWastageTax);
        budgetDetail.getWastage().setMarketingAndSamplingTax(marketingAndSamplingTax);
        budgetDetail.getWastage().setUnsatifiedCustomerCostTax(unsatifiedCustomerCostTax);
        budgetDetail.getWastage().setPPECostTax(ppeCostTax);
        budgetDetail.getWastage().setWastageOtherTax(wastageOtherTax);
        budgetDetail.getWastage().setTrainingCogsTax(trainingCogsTax);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void calculateInventoryAggregate(BudgetDetail b, UnitDetail u, Date businessDate, StockTakeType type) {
        // Calculate and set to BudgetDetail
        long startTime = System.currentTimeMillis();
        BigDecimal dineInCogs = BigDecimal.ZERO;
        BigDecimal deliveryCogs = BigDecimal.ZERO;
        BigDecimal employeeMealCogs = BigDecimal.ZERO;
        BigDecimal dineInCogsTax = BigDecimal.ZERO;
        BigDecimal deliveryCogsTax = BigDecimal.ZERO;
        BigDecimal employeeMealCogsTax = BigDecimal.ZERO;

        // get Day close event
        List<Integer> events = stockManagementDao.getDayCloseEventForBusinessDate(u.getUnitId(), businessDate, type);
        LOG.info("Number of Day Close events for closing {}", events.size());
        if (events.size() == 1) {
            b.getKey().setSumoClosureId(events.get(0));
        }

        if (events == null || events.isEmpty()) {
            return;
        }

        // get product consumption with cost
        List<Pair<String, Pair<BigDecimal, BigDecimal>>> l = stockManagementDao.getCOGSAggregate(events);
        for (Pair<String, Pair<BigDecimal, BigDecimal>> p : l) {
            if (AppConstants.CAFE.equalsIgnoreCase(p.getKey())) {
                dineInCogs = p.getValue().getKey();
                dineInCogsTax = p.getValue().getValue();
            } else if (AppConstants.COD.equalsIgnoreCase(p.getKey())) {
                deliveryCogs = p.getValue().getKey();
                deliveryCogsTax = p.getValue().getValue();
            } else if (AppConstants.ORDER_TYPE_EMPLOYEE_MEAL.equalsIgnoreCase(p.getKey())) {
                employeeMealCogs = p.getValue().getKey();
                employeeMealCogsTax = p.getValue().getValue();
            }
        }
        LOG.info("Inventory Aggregate - COGS Aggregate calculated in {} milliseconds",
                System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();

        Integer scmDayCloseEventId = stockManagementDao.getDayCloseEvent(u.getUnitId(), businessDate, type);

		/*if (StockTakeType.MONTHLY.equals(type)) {
			scmDayCloseEventId = stockManagementDao.getDayCloseEventIdOfType(u.getUnitId(), StockTakeType.MONTHLY,
					StockEventType.STOCK_TAKE, SCMUtil.getMonthlyCheckDate(businessDate),
					SCMUtil.getMonthlyCheckDateEnd(businessDate));
		} else {
			scmDayCloseEventId = stockManagementDao.getSCMDayCloseEventForBusinessDate(u.getUnitId(), businessDate);
		}*/

        // calculate stock variance for today
        // for ZERO Variance Products only
        InventoryAggregate inventoryAggregate = stockManagementDao.getStockVarianceAmount(scmDayCloseEventId, type);

        LOG.info("Inventory Aggregate - Stock Variance (current day) calculated in {} milliseconds",
                System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();

        // calculate stock variance MTD
        InventoryAggregate monthlyInventoryAggregate = stockManagementDao.getStockVarianceAmountMTD(u.getUnitId(), businessDate, type);

        LOG.info("Inventory Aggregate - Stock Variance (MTD)  calculated in {} milliseconds",
                System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();

        b.setInventory(inventoryAggregate);
        b.getInventory().setDeliveryCogs(deliveryCogs);
        b.getInventory().setDineInCogs(dineInCogs);
        b.getInventory().setEmployeeMealCogs(employeeMealCogs);
        b.getInventory().setDeliveryCogsTax(deliveryCogsTax);
        b.getInventory().setDineInCogsTax(dineInCogsTax);
        b.getInventory().setEmployeeMealCogsTax(employeeMealCogsTax);

        b.getInventory().setStockVarianceMTD(monthlyInventoryAggregate.getStockVariance());
        b.getInventory().setZeroVarianceMTD(monthlyInventoryAggregate.getZeroVariance());
        b.getInventory().setVariancePccMTD(monthlyInventoryAggregate.getVariancePCC());
        b.getInventory().setVarianceYcMTD(monthlyInventoryAggregate.getVarianceYC());
        b.getInventory().setVarianceOthersMTD(monthlyInventoryAggregate.getVarianceOthers());

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void calculateConsumablesAggregate(BudgetDetail b, UnitDetail u, Date businessDate, StockTakeType type) {
        // Calculate and set to BudgetDetail
        BigDecimal consumableUtility = BigDecimal.ZERO;
        BigDecimal consumableStationary = BigDecimal.ZERO;
        BigDecimal consumableUniform = BigDecimal.ZERO;
        BigDecimal consumableEquipment = BigDecimal.ZERO;
        BigDecimal consumableCutlery = BigDecimal.ZERO;
        BigDecimal consumableMarketing = BigDecimal.ZERO;
        BigDecimal consumableOthers = BigDecimal.ZERO;
        BigDecimal consumableDisposable = BigDecimal.ZERO;
        BigDecimal consumableLhi = BigDecimal.ZERO;
        BigDecimal consumableIt = BigDecimal.ZERO;
        BigDecimal consumableMaintenance = BigDecimal.ZERO;
        BigDecimal consumableOfficeEquipment = BigDecimal.ZERO;
        BigDecimal consumableKitchenEquipment = BigDecimal.ZERO;
        BigDecimal consumableChaiMonk = BigDecimal.ZERO;

        BigDecimal consumableUtilityTax = BigDecimal.ZERO;
        BigDecimal consumableStationaryTax = BigDecimal.ZERO;
        BigDecimal consumableUniformTax = BigDecimal.ZERO;
        BigDecimal consumableEquipmentTax = BigDecimal.ZERO;
        BigDecimal consumableCutleryTax = BigDecimal.ZERO;
        BigDecimal consumableMarketingTax = BigDecimal.ZERO;
        BigDecimal consumableOthersTax = BigDecimal.ZERO;
        BigDecimal consumableDisposableTax = BigDecimal.ZERO;

        BigDecimal consumableLhiTax = BigDecimal.ZERO;
        BigDecimal consumableItTax = BigDecimal.ZERO;
        BigDecimal consumableMaintenanceTax = BigDecimal.ZERO;
        BigDecimal consumableOfficeEquipmentTax = BigDecimal.ZERO;
        BigDecimal consumableKitchenEquipmentTax = BigDecimal.ZERO;
        BigDecimal consumableChaiMonkTax = BigDecimal.ZERO;

        BigDecimal depreciationFurnitureFixture = BigDecimal.ZERO;
        BigDecimal depreciationOfficeEquipment = BigDecimal.ZERO;
        BigDecimal depreciationKitchenEquipment = BigDecimal.ZERO;
        BigDecimal depreciationEquipment = BigDecimal.ZERO;
        BigDecimal depreciationIt = BigDecimal.ZERO;
        BigDecimal depreciationVehicle = BigDecimal.ZERO;
        BigDecimal depreciationOthers = BigDecimal.ZERO;


        Map<Integer, Pair<BigDecimal, BigDecimal>> l = stockManagementDao.getConsumableAggregate(u.getUnitId(), businessDate,
                type);

        for (Integer k : l.keySet()) {
            Pair<BigDecimal, BigDecimal> p = l.get(k);
            if (SCMServiceConstants.SUB_CATEGORY_UTILITY == k) {
                consumableUtility = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_STATIONERY == k) {
                consumableStationary = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_UNIFORM == k) {
                consumableUniform = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_EQUIPMENT == k) {
                consumableEquipment = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CUTLERY == k) {
                consumableCutlery = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_MARKETING == k) {
                consumableMarketing = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_DISPOSABLE == k) {
                consumableDisposable = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_IT == k) {
                consumableIt = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_LHI == k) {
                consumableLhi = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_MAINTENANCE == k) {
                consumableMaintenance = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_OFFICE_EQUIPMENT == k) {
                consumableOfficeEquipment = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CHAI_MONK == k) {
                consumableChaiMonk = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_KITCHEN_EQUIPMENT == k) {
                consumableKitchenEquipment = AppUtils.subtract(p.getKey(), p.getValue());
            } else {
                consumableOthers = AppUtils.add(consumableOthers, p.getValue());
            }
        }

        Map<Integer, Pair<BigDecimal, BigDecimal>> t = stockManagementDao.getConsumableTaxAggregate(u.getUnitId(), businessDate,
                type);

        for (Integer k : t.keySet()) {
            Pair<BigDecimal, BigDecimal> p = t.get(k);
            if (SCMServiceConstants.SUB_CATEGORY_UTILITY == k) {
                consumableUtilityTax = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_STATIONERY == k) {
                consumableStationaryTax = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_UNIFORM == k) {
                consumableUniformTax = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_EQUIPMENT == k) {
                consumableEquipmentTax = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CUTLERY == k) {
                consumableCutleryTax = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_MARKETING == k) {
                consumableMarketingTax = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_DISPOSABLE == k) {
                consumableDisposableTax = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_IT == k) {
                consumableIt = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_LHI == k) {
                consumableLhi = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_MAINTENANCE == k) {
                consumableMaintenance = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_OFFICE_EQUIPMENT == k) {
                consumableOfficeEquipment = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_KITCHEN_EQUIPMENT == k) {
                consumableKitchenEquipment = AppUtils.subtract(p.getKey(), p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CHAI_MONK == k) {
                consumableChaiMonk = AppUtils.subtract(p.getKey(), p.getValue());
            } else {
                consumableOthersTax = AppUtils.add(consumableOthersTax, p.getValue());
            }
        }

        // add type in calls

        BigDecimal fixedAssetDepreciation = scmAssetManagementService.getDepreciationOnUnit(u.getUnitId(), businessDate,
                type);
        Map<Integer, BigDecimal> fixedAssetSeperateDepreciation = scmAssetManagementService.getDepreciationOnUnitForCategories(u.getUnitId(), businessDate);

        for (Integer k : fixedAssetSeperateDepreciation.keySet()) {
            switch (k) {
                case SCMServiceConstants.SUB_CATEGORY_FA_FURNITURE:
                    depreciationFurnitureFixture=fixedAssetSeperateDepreciation.get(k);
                    break;
                case SCMServiceConstants.SUB_CATEGORY_FA_OFFICE:
                    depreciationOfficeEquipment=fixedAssetSeperateDepreciation.get(k);
                    break;
                case SCMServiceConstants.SUB_CATEGORY_FA_KITCHEN:
                    depreciationKitchenEquipment=fixedAssetSeperateDepreciation.get(k);
                    break;
                case SCMServiceConstants.SUB_CATEGORY_EQUIPMENT:
                    depreciationEquipment=fixedAssetSeperateDepreciation.get(k);
                    break;
                case SCMServiceConstants.SUB_CATEGORY_FA_IT:
                    depreciationIt=fixedAssetSeperateDepreciation.get(k);
                    break;
                case SCMServiceConstants.SUB_CATEGORY_FA_VEHICLE:
                    depreciationVehicle=fixedAssetSeperateDepreciation.get(k);
                    break;
                default:
                    depreciationOthers=AppUtils.add(depreciationOthers,fixedAssetSeperateDepreciation.get(k));
            }
        }

        BigDecimal fixedAssetLost = scmAssetManagementService.getLostAssetAmountOnUnit(u.getUnitId(), businessDate,
                type);
        BigDecimal fixedAssetDamage = scmAssetManagementService.getDamageAssetAmountOnUnit(u.getUnitId(), businessDate,
                type);
        Date handoverDate = masterDataCache.getUnit(b.getKey().getUnitId()).getHandoverDate();
        if (handoverDate == null) {
            handoverDate = AppUtils.getCurrentDate();
        }
        ConsumablesAggregate consumablesWithFixedAssets = stockManagementDao.getFixedAssetsAggregate(u.getUnitId(),
                businessDate, type, handoverDate);

        b.setConsumables(consumablesWithFixedAssets);
        b.getConsumables().setConsumable(b.getConsumables().getConsumable().add(consumableCutlery));
        b.getConsumables().setConsumable(b.getConsumables().getConsumable().add(consumableEquipment));
        b.getConsumables().setConsumable(b.getConsumables().getConsumable().add(consumableStationary));
        b.getConsumables().setConsumable(b.getConsumables().getConsumable().add(consumableUniform));
        b.getConsumables().setConsumable(b.getConsumables().getConsumable().add(consumableUtility));
        b.getConsumables().setConsumable(b.getConsumables().getConsumable().add(consumableOthers));
        b.getConsumables().setConsumable(b.getConsumables().getConsumable().add(consumableLhi));
        b.getConsumables().setConsumable(b.getConsumables().getConsumable().add(consumableIt));
        b.getConsumables().setConsumable(b.getConsumables().getConsumable().add(consumableMaintenance));
        b.getConsumables().setConsumable(b.getConsumables().getConsumable().add(consumableOfficeEquipment));
        b.getConsumables().setConsumable(b.getConsumables().getConsumable().add(consumableChaiMonk));
        b.getConsumables().setConsumable(b.getConsumables().getConsumable().add(consumableKitchenEquipment));

        b.getConsumables().setConsumableTax(b.getConsumables().getConsumableTax().add(consumableCutleryTax));
        b.getConsumables().setConsumableTax(b.getConsumables().getConsumableTax().add(consumableEquipmentTax));
        b.getConsumables().setConsumableTax(b.getConsumables().getConsumableTax().add(consumableStationaryTax));
        b.getConsumables().setConsumableTax(b.getConsumables().getConsumableTax().add(consumableUniformTax));
        b.getConsumables().setConsumableTax(b.getConsumables().getConsumableTax().add(consumableUtilityTax));
        b.getConsumables().setConsumableTax(b.getConsumables().getConsumableTax().add(consumableOthersTax));
        b.getConsumables().setConsumableTax(b.getConsumables().getConsumableTax().add(consumableLhiTax));
        b.getConsumables().setConsumableTax(b.getConsumables().getConsumableTax().add(consumableItTax));
        b.getConsumables().setConsumableTax(b.getConsumables().getConsumableTax().add(consumableMaintenanceTax));
        b.getConsumables().setConsumableTax(b.getConsumables().getConsumableTax().add(consumableOfficeEquipmentTax));
        b.getConsumables().setConsumableTax(b.getConsumables().getConsumableTax().add(consumableChaiMonkTax));
        b.getConsumables().setConsumableTax(b.getConsumables().getConsumableTax().add(consumableKitchenEquipmentTax));

        // b.getConsumables().setConsumableTax(b.getConsumables().getConsumableTax().add(consumableDisposableTax));
        b.getConsumables().setConsumableMarketing(consumableMarketing);
        b.getConsumables().setConsumableOthers(consumableOthers);
        b.getConsumables().setConsumableCutlery(consumableCutlery);
        b.getConsumables().setConsumableEquipment(consumableEquipment);
        b.getConsumables().setConsumableStationary(consumableStationary);
        b.getConsumables().setConsumableUniform(consumableUniform);
        b.getConsumables().setConsumableUtility(consumableUtility);
        b.getConsumables().setConsumableDisposable(consumableDisposable);
        b.getConsumables().setConsumableLhi(consumableLhi);
        b.getConsumables().setConsumableIt(consumableIt);
        b.getConsumables().setConsumableMaintenance(consumableMaintenance);
        b.getConsumables().setConsumableOfficeEquipment(consumableOfficeEquipment);
        b.getConsumables().setConsumableChaiMonk(consumableChaiMonk);
        b.getConsumables().setConsumableKitchenEquipment(consumableKitchenEquipment);

        b.getConsumables().setConsumableMarketingTax(consumableMarketingTax);
        b.getConsumables().setConsumableOthersTax(consumableOthersTax);
        b.getConsumables().setConsumableCutleryTax(consumableCutleryTax);
        b.getConsumables().setConsumableEquipmentTax(consumableEquipmentTax);
        b.getConsumables().setConsumableStationaryTax(consumableStationaryTax);
        b.getConsumables().setConsumableUniformTax(consumableUniformTax);
        b.getConsumables().setConsumableUtilityTax(consumableUtilityTax);
        b.getConsumables().setConsumableDisposableTax(consumableDisposableTax);
        b.getConsumables().setConsumableLhiTax(consumableLhiTax);
        b.getConsumables().setConsumableItTax(consumableItTax);
        b.getConsumables().setConsumableMaintenanceTax(consumableMaintenanceTax);
        b.getConsumables().setConsumableOfficeEquipmentTax(consumableOfficeEquipmentTax);
        b.getConsumables().setConsumableChaiMonkTax(consumableChaiMonkTax);
        b.getConsumables().setConsumableKitchenEquipmentTax(consumableKitchenEquipmentTax);

        b.getConsumables().setFixedAssetDepreciation(fixedAssetDepreciation);
        b.getConsumables().setFixedAssetLost(fixedAssetLost);
        b.getConsumables().setFixedAssetDamage(fixedAssetDamage);

        b.getConsumables().setDepreciationFurnitureFixture(depreciationFurnitureFixture);
        b.getConsumables().setDepreciationOfficeEquipment(depreciationOfficeEquipment);
        b.getConsumables().setDepreciationKitchenEquipment(depreciationKitchenEquipment);
        b.getConsumables().setDepreciationEquipment(depreciationEquipment);
        b.getConsumables().setDepreciationIt(depreciationIt);
        b.getConsumables().setDepreciationVehicle(depreciationVehicle);
        b.getConsumables().setDepreciationOthers(depreciationOthers);

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ConsumablesAggregate getConsumablesAggregateForMonth(int unitId) {
        ConsumablesAggregate consumables = new ConsumablesAggregate();

        BigDecimal consumableUtility = BigDecimal.ZERO;
        BigDecimal consumableStationary = BigDecimal.ZERO;
        BigDecimal consumableUniform = BigDecimal.ZERO;
        BigDecimal consumableEquipment = BigDecimal.ZERO;
        BigDecimal consumableCutlery = BigDecimal.ZERO;
        BigDecimal consumableMarketing = BigDecimal.ZERO;
        BigDecimal consumableOthers = BigDecimal.ZERO;
        BigDecimal consumableDisposable = BigDecimal.ZERO;

        BigDecimal consumableLhi = BigDecimal.ZERO;
        BigDecimal consumableIt = BigDecimal.ZERO;
        BigDecimal consumableMaintenance = BigDecimal.ZERO;
        BigDecimal consumableOfficeEquipment = BigDecimal.ZERO;
        BigDecimal consumableKitchenEquipment = BigDecimal.ZERO;
        BigDecimal consumableChaiMonk = BigDecimal.ZERO;


        Date businessDate = AppUtils.getCurrentDate();
        Date monthStartDate = AppUtils.getFirstDayOfMonth(businessDate);

        List<Pair<Integer, BigDecimal>> l = stockManagementDao.getConsumableAggregateForMonth(unitId, monthStartDate,
                businessDate);
        for (Pair<Integer, BigDecimal> p : l) {
            if (SCMServiceConstants.SUB_CATEGORY_UTILITY == p.getKey()) {
                consumableUtility = p.getValue();
            } else if (SCMServiceConstants.SUB_CATEGORY_STATIONERY == p.getKey()) {
                consumableStationary = p.getValue();
            } else if (SCMServiceConstants.SUB_CATEGORY_UNIFORM == p.getKey()) {
                consumableUniform = p.getValue();
            } else if (SCMServiceConstants.SUB_CATEGORY_EQUIPMENT == p.getKey()) {
                consumableEquipment = p.getValue();
            } else if (SCMServiceConstants.SUB_CATEGORY_CUTLERY == p.getKey()) {
                consumableCutlery = p.getValue();
            } else if (SCMServiceConstants.SUB_CATEGORY_MARKETING == p.getKey()) {
                consumableMarketing = p.getValue();
            } else if (SCMServiceConstants.SUB_CATEGORY_DISPOSABLE == p.getKey()) {
                consumableDisposable = p.getValue();
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_IT == p.getKey()) {
                consumableIt = p.getValue();
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_LHI == p.getKey()) {
                consumableLhi = p.getValue();
            } else if (SCMServiceConstants.SUB_CATEGORY_MAINTENANCE == p.getKey()) {
                consumableMaintenance = p.getValue();
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_OFFICE_EQUIPMENT == p.getKey()) {
                consumableOfficeEquipment = p.getValue();
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_KITCHEN_EQUIPMENT == p.getKey()) {
                consumableKitchenEquipment = p.getValue();
            } else if (SCMServiceConstants.SUB_CATEGORY_CHAI_MONK == p.getKey()) {
                consumableChaiMonk = p.getValue();
            } else {
                consumableOthers = AppUtils.add(consumableOthers, p.getValue());
            }
        }

        List<Pair<Integer, BigDecimal>> rl = stockManagementDao.getRequestedConsumableAggregate(unitId, monthStartDate,
                businessDate);
        for (Pair<Integer, BigDecimal> p : rl) {
            if (SCMServiceConstants.SUB_CATEGORY_UTILITY == p.getKey()) {
                consumableUtility = AppUtils.add(consumableUtility, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_STATIONERY == p.getKey()) {
                consumableStationary = AppUtils.add(consumableStationary, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_UNIFORM == p.getKey()) {
                consumableUniform = AppUtils.add(consumableUniform, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_EQUIPMENT == p.getKey()) {
                consumableEquipment = AppUtils.add(consumableEquipment, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CUTLERY == p.getKey()) {
                consumableCutlery = AppUtils.add(consumableCutlery, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_MARKETING == p.getKey()) {
                consumableMarketing = AppUtils.add(consumableMarketing, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_DISPOSABLE == p.getKey()) {
                consumableDisposable = AppUtils.add(consumableDisposable, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_LHI == p.getKey()) {
                consumableLhi = AppUtils.add(consumableLhi, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_IT == p.getKey()) {
                consumableIt = AppUtils.add(consumableIt, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_MAINTENANCE == p.getKey()) {
                consumableMaintenance = AppUtils.add(consumableMaintenance, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_OFFICE_EQUIPMENT == p.getKey()) {
                consumableOfficeEquipment = AppUtils.add(consumableOfficeEquipment, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_KITCHEN_EQUIPMENT == p.getKey()) {
                consumableKitchenEquipment = AppUtils.add(consumableKitchenEquipment, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CHAI_MONK == p.getKey()) {
                consumableChaiMonk = AppUtils.add(consumableChaiMonk, p.getValue());
            } else {
                consumableOthers = AppUtils.add(consumableOthers, p.getValue());
            }
        }

        List<Pair<Integer, BigDecimal>> tl = stockManagementDao.getTransferredConsumableAggregateForMonth(unitId,
                monthStartDate, businessDate);
        for (Pair<Integer, BigDecimal> p : tl) {
            if (SCMServiceConstants.SUB_CATEGORY_UTILITY == p.getKey()) {
                consumableUtility = AppUtils.add(consumableUtility, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_STATIONERY == p.getKey()) {
                consumableStationary = AppUtils.add(consumableStationary, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_UNIFORM == p.getKey()) {
                consumableUniform = AppUtils.add(consumableUniform, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_EQUIPMENT == p.getKey()) {
                consumableEquipment = AppUtils.add(consumableEquipment, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CUTLERY == p.getKey()) {
                consumableCutlery = AppUtils.add(consumableCutlery, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_MARKETING == p.getKey()) {
                consumableMarketing = AppUtils.add(consumableMarketing, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_DISPOSABLE == p.getKey()) {
                consumableDisposable = AppUtils.add(consumableDisposable, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_LHI == p.getKey()) {
                consumableLhi = AppUtils.add(consumableLhi, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_IT == p.getKey()) {
                consumableIt = AppUtils.add(consumableIt, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_MAINTENANCE == p.getKey()) {
                consumableMaintenance = AppUtils.add(consumableMaintenance, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_OFFICE_EQUIPMENT == p.getKey()) {
                consumableOfficeEquipment = AppUtils.add(consumableOfficeEquipment, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CONSUM_KITCHEN_EQUIPMENT == p.getKey()) {
                consumableKitchenEquipment = AppUtils.add(consumableKitchenEquipment, p.getValue());
            } else if (SCMServiceConstants.SUB_CATEGORY_CHAI_MONK == p.getKey()) {
                consumableChaiMonk = AppUtils.add(consumableChaiMonk, p.getValue());
            } else {
                consumableOthers = AppUtils.add(consumableOthers, p.getValue());
            }
        }

        consumables.setConsumable(consumables.getConsumable().add(consumableCutlery));
        consumables.setConsumable(consumables.getConsumable().add(consumableEquipment));
        consumables.setConsumable(consumables.getConsumable().add(consumableStationary));
        consumables.setConsumable(consumables.getConsumable().add(consumableUniform));
        consumables.setConsumable(consumables.getConsumable().add(consumableUtility));
        consumables.setConsumable(consumables.getConsumable().add(consumableDisposable));
        consumables.setConsumable(consumables.getConsumable().add(consumableIt));
        consumables.setConsumable(consumables.getConsumable().add(consumableLhi));
        consumables.setConsumable(consumables.getConsumable().add(consumableMaintenance));
        consumables.setConsumable(consumables.getConsumable().add(consumableOfficeEquipment));
        consumables.setConsumable(consumables.getConsumable().add(consumableChaiMonk));
        consumables.setConsumable(consumables.getConsumable().add(consumableKitchenEquipment));
        consumables.setConsumableMarketing(consumableMarketing);
        consumables.setConsumableOthers(consumableOthers);

        return consumables;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ConsumablesAggregate getFixedAssetAggregateForMonth(int unitId) {
        ConsumablesAggregate consumables = new ConsumablesAggregate();
        BigDecimal currentFixedAssets = BigDecimal.ZERO;
        BigDecimal requestedFixedAssets = BigDecimal.ZERO;
        BigDecimal transferredFixedAssets = BigDecimal.ZERO;

        Date businessDate = AppUtils.getCurrentDate();
        Date monthStartDate = AppUtils.getFirstDayOfMonth(businessDate);

        currentFixedAssets = stockManagementDao.getFixedAssetsAggregateForMonth(unitId, monthStartDate, businessDate);
        requestedFixedAssets = stockManagementDao.getRequestedFixedAssetsAggregate(unitId, monthStartDate,
                businessDate);
        transferredFixedAssets = stockManagementDao.getTransferredFixedAssetsForMonth(unitId, monthStartDate,
                businessDate);
        currentFixedAssets = AppUtils.add(currentFixedAssets, requestedFixedAssets);
        currentFixedAssets = AppUtils.add(currentFixedAssets, transferredFixedAssets);

        consumables.setFixedAssets(currentFixedAssets);
        return consumables;
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.scm.core.service.StockManagementService#getTaxAggregate(int,
     * java.util.Date, java.util.List)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public BigDecimal getTaxAggregate(int unitId, Date businessDate, List<Integer> categories,
                                      StockTakeType stockTakeType) {
        return stockManagementDao.getTaxAggregate(unitId, businessDate, categories, stockTakeType);
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.scm.core.service.StockManagementService#getFATaxAggregate(int,
     * java.util.Date, java.math.BigDecimal)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public BigDecimal getFATaxAggregate(int unitId, Date businessDate, ThresholdType type, BigDecimal threshold,
                                        StockTakeType stockTakeType) {
        return stockManagementDao.getFATaxAggregate(unitId, businessDate, type, threshold, stockTakeType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, BigDecimal> getFATaxAggregate(int unitId, Date businessDate, ThresholdType type, BigDecimal threshold,
                                                      StockTakeType stockTakeType, Date handoverDate, boolean beforehandOver) {
        return stockManagementDao.getFATaxAggregate(unitId, businessDate, type, threshold, stockTakeType, handoverDate, beforehandOver);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Integer checkSpecialOrders(int unitId) {
        return stockManagementDao.checkSpecialOrders(unitId, SCMUtil.getCurrentBusinessDate());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public DayCloseEvent fetchUnitLastDayClose(int unitId) {
        return SCMDataConverter.convert(stockManagementDao.fetchUnitLastDayClose(unitId), masterDataCache);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Integer> getUnitsWithMonthlyDone(Date date) {
        return stockManagementDao.getUnitsWithMonthlyDone(date);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<DayCloseEvent> getInitiatedStockEvents(Integer unitId) {
        List<DayCloseEvent> events = null;
        List<SCMDayCloseEventData> dataList = stockManagementDao.getInitiatedStockEvents(unitId);
        if (dataList != null && !dataList.isEmpty()) {
            events = dataList.stream()
                    .map(data -> SCMDataConverter.convert(data, masterDataCache))
                    .collect(Collectors.toList());
        }
        return events;
    }

    private boolean calendarValidationRequired(StockTakeType stockType, StockInventoryData detail) {
        return UnitCategory.CAFE.equals(masterDataCache.getUnit(detail.getUnit()).getFamily()) &&
                (StockTakeType.MONTHLY.equals(stockType) || StockTakeType.WEEKLY.equals(stockType));
    }

    private Integer getWeeklyDayCloseDay(Integer unitId){
        Map<Integer,Integer> unitDayCloseDayMap = new HashMap<>();
        List<UnitDayCloseDayMappingData> unitDayCloseDayMappingDataList = dayCloseEventDao.findAll(UnitDayCloseDayMappingData.class);
        unitDayCloseDayMappingDataList.forEach(unitDayCloseDayMappingData -> {
            Integer dayId = unitDayCloseDayMappingData.getDayId();
            Arrays.stream(unitDayCloseDayMappingData.getUnitIds().split(",")).map(
                    s -> Integer.parseInt(s)
            ).map(i -> {
                unitDayCloseDayMap.put(i,dayId);
                return i;
            }).collect(Collectors.toList());

        });
        return unitDayCloseDayMap.containsKey(unitId) ? unitDayCloseDayMap.get(unitId) : 1;

    }

    private void generateNextStockCalendarEvent(StockInventoryData detail, StockTakeType stockType)
            throws SumoException {
        Date nextDate = null;
        if (StockTakeType.WEEKLY.equals(stockType)) {
            Integer dayId = getWeeklyDayCloseDay(detail.getUnit());
            nextDate = SCMUtil.getNextWeeklyInventoryDate(dayId);
        }
        if (StockTakeType.MONTHLY.equals(stockType)) {
            nextDate = SCMUtil.getNextMonthlyInventoryDate();
        }
        stockManagementDao.addStockCalendarEvent(detail.getUnit(), stockType, nextDate);
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public StockEventCalendarData addStockCalendarEvent(Integer unitId, StockTakeType stockType, Date nextDate)
            throws SumoException {
        Unit unit = masterDataCache.getUnit(unitId);
        if(Objects.equals(unit.getClosure(), "PROCESSING") && stockType != StockTakeType.MONTHLY ){
            throw new SumoException(String.format("Unit Closure in processing cannot initiate the stock take type  %s ", stockType));
        }
        return stockManagementDao.addStockCalendarEvent(unitId, stockType, nextDate);
    }

    private void flushUnitPosSessions(Integer unitId) {
        if (!SCMUtil.checkForNewDayCloseTimeline(SCMUtil.getCurrentTimestamp())) {
            String endpoint = props.getMasterServiceBasePath() + MasterServiceClientEndpoints.FLUSH_UNIT_POS_SESSION;
            Map<String, Integer> uriVars = new HashMap<>();
            uriVars.put("unitId", unitId);
            try {
                WebServiceHelper.exchangeWithAuth(endpoint, props.getAuthToken(), HttpMethod.GET, null, null, uriVars);
            } catch (URISyntaxException e) {
                LOG.error("Error flushing unit sessions after inventory", e);
            }catch (Exception e1){
                LOG.error("Error flushing unit sessions after inventory :::::", e1);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, BigDecimal> getInTransitStock(int unitId, Boolean isF9) {
        List<GoodsReceived> goodsReceiveds = goodsReceiveManagementService.getPendingGrsWithGrItems(unitId);
        Map<Integer, BigDecimal> transitMap = new HashMap<>();
        for (GoodsReceived received : goodsReceiveds) {
            GoodsReceivedData goodsReceivedData = stockManagementDao.find(GoodsReceivedData.class, received.getId());
            goodsReceivedData = goodsReceiveManagementService.getOriginalGrData(goodsReceivedData);
            if (isF9) {
                if (Objects.nonNull(goodsReceivedData.getRequestOrderData()) && Objects.nonNull(goodsReceivedData.getRequestOrderData().getReferenceOrderData())
                        && goodsReceivedData.getRequestOrderData().getReferenceOrderData().getRefOrderSource().equalsIgnoreCase("FOUNTAIN9_DATA_SOURCE_ORDERING")) {
                    for (GoodsReceivedItem goodsReceivedItem : received.getGoodsReceivedItems()) {
                        if (!transitMap.containsKey(goodsReceivedItem.getProductId())) {
                            transitMap.put(goodsReceivedItem.getProductId(), new BigDecimal(goodsReceivedItem.getTransferredQuantity()));
                            continue;
                        }
                        if (transitMap.containsKey(goodsReceivedItem.getProductId())) {
                            BigDecimal tempQuantity = SCMUtil.add(transitMap.get(goodsReceivedItem.getProductId()), new BigDecimal(goodsReceivedItem.getTransferredQuantity()));
                            transitMap.replace(goodsReceivedItem.getProductId(), tempQuantity);
                        }
                    }
                }
            } else {
                for (GoodsReceivedItem goodsReceivedItem : received.getGoodsReceivedItems()) {
                    if (!transitMap.containsKey(goodsReceivedItem.getProductId())) {
                        transitMap.put(goodsReceivedItem.getProductId(), new BigDecimal(goodsReceivedItem.getTransferredQuantity()));
                        continue;
                    }
                    if (transitMap.containsKey(goodsReceivedItem.getProductId())) {
                        BigDecimal tempQuantity = SCMUtil.add(transitMap.get(goodsReceivedItem.getProductId()), new BigDecimal(goodsReceivedItem.getTransferredQuantity()));
                        transitMap.replace(goodsReceivedItem.getProductId(), tempQuantity);
                    }
                }
            }
        }
        return transitMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<WastageEvent> addReverseWastageEvent(ProductionBooking productionBooking, ReverseProductionBooking booking, ReverseProductionBookingData reverseProductionBookingData) throws DataNotFoundException, InventoryUpdateException, SumoException {
        Map<Integer, BookingConsumption> consumptionMap = new HashMap<>();
        for (BookingConsumption c : productionBooking.getBookingConsumption()) {
            flattenReverseConsumptionItems(consumptionMap, c);
        }
        List<ReverseBookingConsumptionData> reverseBookingConsumptionData = reverseProductionBookingData.getConsumption();
        for (ReverseBookingConsumptionData data : reverseBookingConsumptionData) {
            if (consumptionMap.containsKey(data.getSkuId())) {
                BookingConsumption bookingConsumption = consumptionMap.get(data.getSkuId());
                if (Objects.nonNull(bookingConsumption) && Objects.nonNull(bookingConsumption.getWastageQuantity())) {
                    data.setWastageQuantity(bookingConsumption.getWastageQuantity());
                    stockManagementDao.update(data, true);
                }
            }
        }
        WastageEvent wastageEvent = WastageEvent.builder().generatedBy(AppConstants.SYSTEM_EMPLOYEE_ID)
                .status(StockEventStatus.SETTLED)
                .unitId(booking.getUnitId())
                .generationTime(AppUtils.getCurrentTimestamp())
                .businessDate(AppUtils.getBusinessDate()).build();
        List<WastageData> wastageDataList = new ArrayList<>();
        for(Integer key : consumptionMap.keySet()) {
            WastageData data = generateWastageEventObject(consumptionMap.get(key));
            if (Objects.nonNull(data)){
                wastageDataList.add(data);
            }
        }
        wastageEvent.setItems(wastageDataList);
        if (wastageDataList.isEmpty()) {
            return new ArrayList<>();
        }
        return addManualWastageEvent(Arrays.asList(wastageEvent),false,productionBooking.getUnitId());
    }
    @Override
    public FixedAssetDayCloseResponseObject getAssetsToDayCloseToday(Integer unitId) {
        LOG.info("Getting Fixed Assets to DayClose for UNIT : {}", unitId);
        if( !scmAssetManagementService.checkFaDaycloseEnabled(unitId)){
//                (unitId == 26091)
            LOG.info("FA DayClose Not Enabled for Unit :: {}", unitId);
            FixedAssetDayCloseResponseObject response
                    = new FixedAssetDayCloseResponseObject();
            return response;
        }
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        List<FixedAssetCompactDefinition> availableAssets = scmAssetManagementService.viewAllFixedAssetsFromUnit(unitId);
        LOG.info("Get All Asset for Unit : {}s",  watch.stop().elapsed(TimeUnit.SECONDS));
        watch.start();
//        Date lastDailyDayClose = stockManagementDao.getLastDayCloseDone(unitId, StockTakeSubType.DAILY_DAYCLOSE.value());
//        LOG.info("Last Daily Day Close Date : {}", lastDailyDayClose != null ? lastDailyDayClose.toString():"");
        Date lastWeeklyDayClose = stockManagementDao.getLastDayCloseDone(unitId,StockTakeSubType.WEEKLY_DAYCLOSE.value());
        LOG.info("Last Weekly Day Close Date : {}", lastWeeklyDayClose != null ? lastWeeklyDayClose.toString():"");
        Date lastMonthlyDayClose = stockManagementDao.getLastDayCloseDone(unitId,StockTakeSubType.MONTHLY_DAYCLOSE.value());
        LOG.info("Last Monthly Day Close Date : {}", lastMonthlyDayClose != null ? lastMonthlyDayClose.toString():"");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(SCMUtil.getCurrentTimestamp());
        Integer dayToday = calendar.get(Calendar.DAY_OF_WEEK);
        Integer dateToday = calendar.get(Calendar.DAY_OF_MONTH);

        LOG.info("Get last day close done : {}s",  watch.stop().elapsed(TimeUnit.SECONDS));
        watch.start();
//        long dailyDayDiff = -1;
//        if(!Objects.isNull(lastDailyDayClose)){
//            dailyDayDiff = SCMUtil.getDayDifference(lastDailyDayClose, SCMUtil.getCurrentTimestamp());
//            LOG.info("Daily Day Close Difference from Current Date : {}", dailyDayDiff);
//        }
        long weeklyDayDiff = -1;
        if(!Objects.isNull(lastWeeklyDayClose)){
            weeklyDayDiff = SCMUtil.getDayDifference(AppUtils.getDate(lastWeeklyDayClose), SCMUtil.getCurrentBusinessDate());
            LOG.info("Daily Day Close Difference from Current Date : {}", weeklyDayDiff);
        }
//        long monthlyDayDiff = -1;
//        if(!Objects.isNull(lastMonthlyDayClose)){
//            monthlyDayDiff = SCMUtil.getDayDifference(lastMonthlyDayClose, SCMUtil.getCurrentTimestamp());
//        }
        org.apache.commons.math3.util.Pair< Map<String ,List<FixedAssetCompactDefinition>>, Integer> result;
        Map< String ,List<FixedAssetCompactDefinition> > assetMap;
        boolean showWeekly = false;
        boolean blockweekly = false;
        result = filterAssetsForDayClose(availableAssets,unitId);
        if(Objects.isNull(result)){
            return null;
        }
        assetMap = result.getFirst();
        if(Objects.nonNull(lastWeeklyDayClose) && Objects.nonNull(result.getSecond())){
            showWeekly = true;
            LOG.info("Selected Inventory List Id : {}", result.getSecond());
            Integer actualDayCloseDay = result.getSecond();
            actualDayCloseDay = actualDayCloseDay - 16;
            LOG.info("Actual Day Close Day : {}", actualDayCloseDay);
            LOG.info("Day Today : {}", dayToday);
            com.stpl.tech.master.domain.model.Pair<Date,Date> week = new com.stpl.tech.master.domain.model.Pair<>();
            if(Objects.equals(dayToday, actualDayCloseDay)){
                week.setKey(SCMUtil.getCurrentBusinessDate());
                week.setValue(SCMUtil.getDayBeforeOrAfterDay(week.getKey(),7));
            }else if( dayToday > actualDayCloseDay){
                week.setKey(SCMUtil.getDayBeforeOrAfterDay(SCMUtil.getCurrentBusinessDate(),-(dayToday-actualDayCloseDay)));
                week.setValue(SCMUtil.getDayBeforeOrAfterDay(week.getKey(),7));
            }else if(dayToday < actualDayCloseDay){
                week.setValue(SCMUtil.getDayBeforeOrAfterDay(SCMUtil.getCurrentBusinessDate(),actualDayCloseDay-dayToday));
                week.setKey(SCMUtil.getDayBeforeOrAfterDay(week.getValue(),-7));
            }
            LOG.info("Week start : {}", week.getKey().toString());
            LOG.info("Week end : {}", week.getValue().toString());
            if(lastWeeklyDayClose.compareTo(week.getKey()) >=0 && lastWeeklyDayClose.compareTo(week.getValue()) <=0){
                showWeekly = false;
            }else{
                if(SCMUtil.getDayDifference(AppUtils.getDate(lastWeeklyDayClose), week.getKey()) > 7){
                    blockweekly = true;
                    LOG.info(":::::::::::WEEKLY BLOCKED:::::::::: ");
                }
                Date bufferLimit = SCMUtil.getDayBeforeOrAfterDay(week.getKey(),5);
                LOG.info("Buffer Limit : {}", bufferLimit.toString());
                if(bufferLimit.compareTo(SCMUtil.getCurrentBusinessDate()) < 0){
                    blockweekly = true;
                    LOG.info(":::::::::::WEEKLY BLOCKED:::::::::: ");
                }
            }
        } else if(Objects.isNull(lastWeeklyDayClose)){
            showWeekly = true;
            try{
                Date newBlockingFlowActivationDate = AppUtils.getDate(23,8,2023);
                Date today = AppUtils.getDate(SCMUtil.getCurrentTimestamp());
//                StockEventDefinitionData latestNsoEvent = scmassetManagementDao.getLatestNSOEventByUnit(unitId, StockEventStatusType.COMPLETED.value(), StockTakeSubType.NSO.value());
                Date unitHandoverDate = AppUtils.getDate(masterDataCache.getUnitBasicDetail(unitId).getHandOverDate());
                Integer limit = AppUtils.getAbsDaysDiff(unitHandoverDate,today);
                if(Objects.nonNull(limit) && Math.abs(limit) > 7 && today.compareTo(newBlockingFlowActivationDate) >= 0){
                    blockweekly = true;
                }
            }catch(Exception e){
                LOG.error("Failed to block due to weekly dayclose, not able to fetch handover date.");
            }
        }
        LOG.info("asset filtering : {}s",  watch.stop().elapsed(TimeUnit.SECONDS));
        watch.start();
        boolean showMonthly = false;
        boolean blockMonthly = false;
        calendar.setTime(SCMUtil.getCurrentTimestamp());
        Integer currentDay = calendar.get(Calendar.DAY_OF_MONTH);
        Integer currentMonth = calendar.get(Calendar.MONTH) + 1;
        if(Objects.nonNull(lastMonthlyDayClose)){
            calendar.setTime(lastMonthlyDayClose);
            Integer lastMonthlyDay = calendar.get(Calendar.DAY_OF_MONTH);
            Integer lastMonthlyMonth = calendar.get(Calendar.MONTH) + 1;
            if(currentMonth - (lastMonthlyMonth%12) == 1){
                if(lastMonthlyDay >= 25 && currentDay >= 25){
                    showMonthly = true;
                }else if(lastMonthlyDay < 25){
                    blockMonthly = true;
                }
            }else if(Objects.equals(currentMonth, lastMonthlyMonth)){
                if(currentDay >= 25){
                    showMonthly = true;
                }
                if(lastMonthlyDay >= 25){
                    showMonthly = false;
                }
            }else {
                blockMonthly = true;
            }
        } else {
            Date unitStartDate = masterDataCache.getUnitBasicDetail(unitId).getHandOverDate();
            if(SCMUtil.getDayDifference(unitStartDate ,AppUtils.getDate(SCMUtil.getCurrentTimestamp())) > 30){
                StockEventDefinitionData auditEvent = scmAssetManagementService.getLatestNSOEventByUnit(unitId,
                        StockEventStatusType.COMPLETED.value(), StockTakeSubType.AUDIT.value());
                StockEventDefinitionData regularEvent = scmAssetManagementService.getLatestNSOEventByUnit(unitId,
                        StockEventStatusType.COMPLETED.value(), StockTakeSubType.REGULAR.value());
                if(Objects.nonNull(auditEvent) || Objects.nonNull(regularEvent)){
                    showMonthly = true;
                }else {
                    blockMonthly = true;
                }
            } else {
                showMonthly = true;
            }
        }
        if(AppUtils.getDate(SCMUtil.getCurrentTimestamp()).compareTo(AppUtils.getDate(1,9,2023)) < 0){
            if(Boolean.TRUE.equals(blockMonthly)){
                blockMonthly = false;
                showMonthly = true;
            }
        }
        LOG.info("asset filtering monthly : {}s",  watch.stop().elapsed(TimeUnit.SECONDS));

        FixedAssetDayCloseResponseObject response
                = new FixedAssetDayCloseResponseObject(
                null,
                (showWeekly || blockweekly)  ? assetMap.get(StockTakeSubType.WEEKLY_DAYCLOSE.value()) : null,
                (Objects.equals(showMonthly,true) || Objects.equals(blockMonthly,true)) ? assetMap.get(StockTakeSubType.MONTHLY_DAYCLOSE.value()) : null,
                null, lastWeeklyDayClose, lastMonthlyDayClose, null, blockweekly,blockMonthly);
        return response;
    }

    @Override
    public Map<String, List<ApprovalDetail>> checkForAssetApprovals(HttpServletRequest request,Integer unitId) throws SumoException {
        List<ApprovalDetailData> approvalList = new ArrayList<>();
        List<ApprovalDetail> approvals = new ArrayList<>();
        List<ApprovalDetail> tagList = new ArrayList<>();
        List<ApprovalDetail> assetList = new ArrayList<>();
        List<Integer> eventIdsList = new ArrayList<>();
        Map<String, List<ApprovalDetail>> result = new HashMap<>();
        Integer userId = getLoggedInUser(request);
        approvalList = stockManagementDao.getAssetApprovals(null, unitId,null,null,ApprovalStatus.PENDING.value(),null);
        if(!approvalList.isEmpty()){
            for(ApprovalDetailData approval : approvalList){
                approvals.add(SCMDataConverter.convertToApprovalDetail(approval, scmCache, masterDataCache));
                if(Objects.equals(approval.getType(), ApprovalType.LOST_ASSET.value())){
                    eventIdsList.add(approval.getEventId());
                }
            }
            Set<Integer> auditEventIdsSet = new HashSet<>(stockManagementDao.getAuditEventIdList(eventIdsList));
            if(props.isUserHasAuditorAccess(userId)){
                for(ApprovalDetail approval : approvals){
                    if(Objects.equals(approval.getType(), ApprovalType.LOST_ASSET.value()) &&
                            Objects.nonNull(auditEventIdsSet) && auditEventIdsSet.contains(approval.getEventId())){
                        assetList.add(approval);
                    } else if (Objects.equals(approval.getType(), ApprovalType.LOST_TAG.value())) {
                        tagList.add(approval);
                    }
                }
            }else{
                for(ApprovalDetail approval : approvals){
                    if(Objects.equals(approval.getType(), ApprovalType.LOST_ASSET.value()) &&
                            Objects.nonNull(auditEventIdsSet) && !auditEventIdsSet.contains(approval.getEventId())){
                        assetList.add(approval);
                    } else if (Objects.equals(approval.getType(), ApprovalType.LOST_TAG.value())) {
                        tagList.add(approval);
                    }
                }
            }
        }
        result.put(ApprovalType.LOST_TAG.value(),tagList);
        result.put(ApprovalType.LOST_ASSET.value(),assetList);
        return result;
    }

    private void approveFoundAssetRequests(ApprovalDetailData approvalDetailData, Integer userId) throws SumoException {
        try {
            AssetDefinitionDataLog previousLog = scmAssetManagementDao.getFilteredLatestAssetDefinitionDataLog(approvalDetailData.getAssetId(), List.of(AssetStatusType.LOST_ADJUSTED,AssetStatusType.LOST_IDENTIFIED,AssetStatusType.PENDING_LOST));
            AssetDefinitionData foundAsset = scmAssetManagementDao.getAssetDefinitionDataByAssetId(approvalDetailData.getAssetId());
            foundAsset.setAssetStatus(previousLog.getAssetStatus());
            foundAsset.setUnitId(approvalDetailData.getUnitId());
            foundAsset = scmAssetManagementDao.update(foundAsset,true);
            scmCache.updateAssetToCache(scmAssetManagementService.convertAssetDefinitionDataToAssetDefinition(foundAsset,false));
            scmAssetManagementService.logCurrentStatus(foundAsset);
            approvalDetailData.setStatus(ApprovalStatus.APPROVED.value());
            approvalDetailData.setApprovedBy(userId);
            approvalDetailData.setApprovalDate(SCMUtil.getCurrentBusinessDate());
            stockManagementDao.update(approvalDetailData,true);
            List<StockEventAssetMappingDefinitionData> eventAsset =  scmAssetManagementDao.findAssetsByEventIdAndAssetId(approvalDetailData.getEventId(),approvalDetailData.getAssetId());
            if(eventAsset.size()==1){
                eventAsset.get(0).setAssetStatus(AssetStatusType.FOUND_APPROVED.value());
                scmAssetManagementDao.update(eventAsset,true);
            }
             List<StockEventAssetMappingDefinitionData> allEventAssets =  scmAssetManagementDao.findAssetsByEventId(approvalDetailData.getEventId());
            boolean checkAllAssetStatus = true;
            for(StockEventAssetMappingDefinitionData s : allEventAssets){
                if(!Objects.equals(s.getAssetStatus(), AssetStatusType.FOUND_APPROVED.value())){
                    checkAllAssetStatus = false;
                    break;
                }
            }
            if(checkAllAssetStatus){
                StockEventDefinitionData stockEventDefinition = scmAssetManagementDao.find(StockEventDefinitionData.class,approvalDetailData.getEventId());
                stockEventDefinition.setEventStatus(StockEventStatusType.COMPLETED.value());
                stockEventDefinition.setLastUpdationTime(SCMUtil.getCurrentDateIST());
                scmAssetManagementDao.update(stockEventDefinition,true);
            }

        }catch (Exception e){
            LOG.info("Error while approving found asset request, approval ID : {}, asset ID : {}",approvalDetailData.getApprovalRequestId(),approvalDetailData.getAssetId());
           throw new SumoException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean processApprovalRequest(Integer requestId, Integer userId, Boolean response) throws SumoException {
        List<ApprovalDetailData> approvalList = new ArrayList<>();
            if (Objects.isNull(response) || Objects.equals(response, Boolean.TRUE)) {
                approvalList = stockManagementDao.getAssetApprovals(requestId, null, null, null, ApprovalStatus.PENDING.value(), null);
                if(approvalList.size()>0){
                if (Objects.equals(approvalList.get(0).getType(), ApprovalType.FOUND_ASSET.value()) && Objects.equals(response, Boolean.TRUE)) {
                    approveFoundAssetRequests(approvalList.get(0), userId);
                   return true;
                }
                approvalList.get(0).setStatus(ApprovalStatus.APPROVED.value());
                approvalList.get(0).setApprovedBy(userId);
                approvalList.get(0).setApprovalDate(SCMUtil.getCurrentBusinessDate());
                Integer eventId = approvalList.get(0).getEventId();
                Integer assetId = approvalList.get(0).getAssetId();
                List<StockEventDefinitionData> childEvent = scmAssetManagementService.getParentOrChildEvent(eventId, null, null);
                if(props.isUserHasAuditorAccess(userId)) {
                    approvalList.get(0).setRequestedTo(SCMServiceConstants.AUDITOR_HEAD_USER_ID);
                }
                stockManagementDao.update(approvalList, false);
                List<ApprovalDetailData> pendingApprovals = stockManagementDao.getAssetApprovals(null, null, eventId, null, ApprovalStatus.PENDING.value(), null);
                List<StockEventAssetMappingDefinitionData> pendingLostAssets = new ArrayList<>();
                List<StockEventAssetMappingDefinitionData> childEventAssets = scmAssetManagementService.findAssetsByEventId(childEvent.get(0).getEventId());
                pendingLostAssets = childEventAssets.stream().filter(asset -> asset.getAssetStatus().equals(AssetStatusType.PENDING_LOST.value())).collect(Collectors.toList());
                if (Objects.equals(response, Boolean.TRUE)) {
                    // create entry in recovery table
                    AssetDefinitionData assetDefinitionData = stockManagementDao.find(AssetDefinitionData.class, assetId);
                    AssetDefinition assetDefinition = scmAssetManagementService.updateAssetStatus(assetDefinitionData,
                            AssetStatusType.LOST_IDENTIFIED);
                    AssetRecoveryDefinitionData assetRecoveryDefinitionData = convert(assetDefinition,
                            approvalList.get(0).getRequestedBy(), approvalList.get(0).getRequestedBy(),
                            SCMUtil.getCurrentTimestamp(), childEvent.get(0));
                    stockManagementDao.add(assetRecoveryDefinitionData, false);
                    AssetRecoveryData assetRecoveryData = convert(assetDefinition, approvalList.get(0).getRequestedBy(), SCMUtil.getCurrentTimestamp(), childEvent.get(0));
                    stockManagementDao.add(assetRecoveryData, false);
                    try {
                        Integer poId = null;
                        Integer prId = null;
                        String invoiceId = null;
                        List<StockEventDefinitionData> lastStockTakeEvent = scmAssetManagementService.getLastStockTakeEventByAsset(assetId, StockEventStatusType.COMPLETED.value());
                        try {
                            poId = goodsReceiveManagementDao.findPoWithGrId(assetDefinition.getGrId()).get(0).getPurchaseOrderData().getId();
                        } catch (Exception e) {
                            LOG.error("Failed to find po ID for asset {} having grId {}", assetId, assetDefinition.getGrId());
                        }
                        try {
                            PaymentRequestData paymentRequestData = paymentRequestManagementDao.findPaymentRequestFromVendorGR(assetDefinition.getGrId()).get(0).getPaymentRequestData();
                            prId = paymentRequestData.getId();
                            invoiceId = paymentRequestData.getInvoiceNumber();
                        } catch (Exception e) {
                            LOG.error("Failed to find pr ID for asset {} having grId {}", assetId, assetDefinition.getGrId());
                        }
                        int unitId = childEvent.get(0).getUnitId();
                        Unit unit = masterDataCache.getUnit(unitId);
                        LostAssetEmailObject lostAsset = new LostAssetEmailObject();
                        lostAsset.setAssetName(assetDefinition.getAssetName());
                        lostAsset.setAssetId(assetDefinition.getAssetId());
                        lostAsset.setAssetTag(assetDefinition.getTagValue());
                        lostAsset.setProcurementCost(BigDecimal.valueOf(assetDefinition.getProcurementCost()).setScale(2, RoundingMode.CEILING));
                        lostAsset.setExpectedRecoveryAmount(assetDefinition.getTotalRecoverAmount());
                        lostAsset.setGrId(assetDefinition.getGrId());
                        lostAsset.setPoId(poId);
                        lostAsset.setPrId(prId);
                        lostAsset.setInvoiceId(invoiceId);
                        lostAsset.setCreationDate(assetDefinition.getStartDate());
                        lostAsset.setStockTakeTime(childEvent.get(0).getEventCreationDate());
                        lostAsset.setStockTakingUser(masterDataCache.getEmployeeBasicDetail(childEvent.get(0).getInitiatedBy()).getName() +
                                " (" + childEvent.get(0).getInitiatedBy().toString() + ")");
                        lostAsset.setUnitId(unitId);
                        lostAsset.setUnitName(unit.getName());
                        lostAsset.setUnitAddress(unit.getAddress());
                        lostAsset.setAreaManager(unit.getUnitManager().getName() +
                                " (" + unit.getUnitManager().getId() + ")");
                        lostAsset.setDeputyAreaManager(unit.getCafeManager().getName() +
                                " (" + unit.getCafeManager().getId() + ")");
                        lostAsset.setLossApprovedBy(masterDataCache.getEmployeeBasicDetail(userId).getName() +
                                " (" + masterDataCache.getEmployeeBasicDetail(userId).getId() + ")");
                        lostAsset.setEventId(childEvent.get(0).getEventId());
                        lostAsset.setLastStockTakeTime(lastStockTakeEvent.get(0).getEventCreationDate());
                        lostAsset.setLastStockTakingUnit(masterDataCache.getUnit(lastStockTakeEvent.get(0).getUnitId()).getName() + " (" + lastStockTakeEvent.get(0).getUnitId().toString() + ")");

                        List<String> emailsforEmployeeRecovery = new ArrayList<>();
                        List<String> emailsforInsuranceRecovery = new ArrayList<>();
                        emailsforEmployeeRecovery.add("<EMAIL>");
                        emailsforEmployeeRecovery.add("<EMAIL>");
                        emailsforEmployeeRecovery.add(masterDataCache.getEmployeeBasicDetail(childEvent.get(0).getInitiatedBy()).getEmailId());
                        emailsforEmployeeRecovery.add(unit.getUnitEmail());
                        emailsforEmployeeRecovery.add(unit.getUnitManager().getEmployeeEmail());
                        emailsforEmployeeRecovery.add(masterDataCache.getEmployeeBasicDetail(unit.getCafeManager().getId()).getEmailId());
                        emailsforInsuranceRecovery.add("<EMAIL>");
                        emailsforInsuranceRecovery.add("<EMAIL>");

                        scmNotificationService.sendLostAssetNotification(lostAsset, emailsforInsuranceRecovery, true);
                        lostAsset.setExpectedRecoveryAmount(assetDefinition.getTotalRecoverAmount());
                        scmNotificationService.sendLostAssetNotification(lostAsset, emailsforEmployeeRecovery, false);

                    } catch (Exception e) {
                        LOG.error("Error while sending asset( " + assetId + " ) marked lost email alert");
                    }
                    List<StockEventAssetMappingDefinitionData> lostAsset = childEventAssets.stream().filter(asset -> asset.getAssetId().equals(assetId)).collect(Collectors.toList());
                    lostAsset.get(0).setAssetStatus(AssetStatusType.MARKED_LOST.name());
                    childEventAssets = scmAssetManagementService.findAssetsByEventId(childEvent.get(0).getEventId());
                    pendingLostAssets = childEventAssets.stream().filter(asset -> asset.getAssetStatus().equals(AssetStatusType.PENDING_LOST.value())).collect(Collectors.toList());
                }
//            if(Objects.nonNull(childEvent.get(0).getParentId())){
//                List<StockEventDefinitionData> parentEvent= scmAssetManagementDao.getParentOrChildEvent(childEvent.get(0).getParentId(),null,null);
//                parentEvent.get(0).setEventStatus(StockEventStatusType.COMPLETED.value());
//                stockManagementDao.update(parentEvent,false);
//            }
                if (pendingApprovals.isEmpty() && pendingLostAssets.isEmpty()) {
                    childEvent.get(0).setEventStatus(StockEventStatusType.COMPLETED.value());
                    stockManagementDao.update(childEvent, false);
                    if (Objects.equals(response, Boolean.TRUE)) {
                        try {
                            scmAssetManagementService.sendFAStockTakeReport(eventId, true);
                        } catch (Exception e) {
                            LOG.info(e.getMessage());
                        }
                    }
                } else if (!pendingLostAssets.isEmpty()) {
                    childEvent.get(0).setEventStatus(StockEventStatusType.IN_PROCESS.value());
                    stockManagementDao.update(childEvent, false);
                }

                return true;
                }
            } else {
                approvalList = stockManagementDao.getAssetApprovals(requestId, null, null, null, ApprovalStatus.PENDING.value(), null);
                if (approvalList.size() > 0) {
                    approvalList.get(0).setStatus(ApprovalStatus.REJECTED.value());
                    approvalList.get(0).setApprovedBy(userId);
                    approvalList.get(0).setApprovalDate(SCMUtil.getCurrentBusinessDate());
                    if (!approvalList.isEmpty()) {
                        Integer eventId = approvalList.get(0).getEventId();
                        Integer assetId = approvalList.get(0).getAssetId();
                        AssetDefinitionData assetDefinitionData = stockManagementDao.find(AssetDefinitionData.class, assetId);
                        List<StockEventDefinitionData> childEvent = scmAssetManagementService.getParentOrChildEvent(eventId, null, null);
                        List<StockEventAssetMappingDefinitionData> childEventAssets = scmAssetManagementService.findAssetsByEventId(childEvent.get(0).getEventId())
                                .stream().filter(asset -> asset.getAssetId().equals(assetId)).collect(Collectors.toList());
                        childEventAssets.get(0).setAssetStatus(AssetStatusType.PENDING_LOST.name());
                        childEvent.get(0).setEventStatus(StockEventStatusType.IN_PROCESS.value());
                        stockManagementDao.update(childEventAssets, false);
                        stockManagementDao.update(childEvent, false);
//                if(Objects.nonNull(childEvent.get(0).getParentId())){
//                    List<StockEventDefinitionData> parentEvent= scmAssetManagementDao.getParentOrChildEvent(childEvent.get(0).getParentId(),null,null);
//                    List<StockEventAssetMappingDefinitionData> parentEventAssets = scmAssetManagementDao.findAssetsByEventId(parentEvent.get(0).getEventId())
//                            .stream().filter(asset -> asset.getAssetId().equals(assetId)).collect(Collectors.toList());
//                    parentEventAssets.get(0).setAssetStatus(AssetStatusType.PENDING_LOST.name());
//                    parentEvent.get(0).setEventStatus(StockEventStatusType.IN_PROCESS.value());
//                    stockManagementDao.update(parentEventAssets,false);
//                    stockManagementDao.update(parentEvent,false);
//                }
                        return true;
                    }
                }
            }
        return false;
    }

    private org.apache.commons.math3.util.Pair<Map<String ,List<FixedAssetCompactDefinition>>,Integer> filterAssetsForDayClose(List<FixedAssetCompactDefinition> availableAssets, Integer unitId) {
        Map< String ,List<FixedAssetCompactDefinition> > assetList;
        UnitBasicDetail unit = masterDataCache.getUnitBasicDetail(unitId);
        String unitCategory = unit.getCategory().value();
//        String type= "";
//        if(Objects.equals(unitCategory,"WAREHOUSE")){
//             TODO Define Warehouse Types ( FA, Remote, Goods, Maintenance, IT-Maintenance) and further filter.
//        }else {
//            type= null;
//        }
//        if(Objects.equals(stockTakeEventSubType,StockTakeSubType.WEEKLY_DAYCLOSE)){
//            frequency = StockTakeFrequencyEnum.WEEKLY.value();
//        }else if(Objects.equals(stockTakeEventSubType,StockTakeSubType.MONTHLY_DAYCLOSE)){
//            frequency = StockTakeFrequencyEnum.MONTHLY.value();
//        }
        Map<String, List<Integer>> frequencyClassMapping = new HashMap<String, List<Integer>>();
        List<Integer> classIdWeeklyList = stockManagementDao.getClassListFromUnitType(unitCategory,null,StockTakeFrequencyEnum.WEEKLY.value());
        frequencyClassMapping.put(StockTakeFrequencyEnum.WEEKLY.value(), classIdWeeklyList);
        List<Integer> classIdMonthlyList = stockManagementDao.getClassListFromUnitType(unitCategory,null,StockTakeFrequencyEnum.MONTHLY.value());
        frequencyClassMapping.put(StockTakeFrequencyEnum.MONTHLY.value(), classIdMonthlyList);
        Integer stockTakeDay = stockManagementDao.getInventoryDayForUnit(unitCategory, null, StockTakeFrequencyEnum.WEEKLY.value());
        assetList = getFixedAssetsForDayClose(availableAssets,frequencyClassMapping);

        return new org.apache.commons.math3.util.Pair<>(assetList,stockTakeDay);
    }

    private Map< String ,List<FixedAssetCompactDefinition> > getFixedAssetsForDayClose(List<FixedAssetCompactDefinition> availableAssets, Map<String, List<Integer>> frequencyClassMapping) {

        Map< String ,List<FixedAssetCompactDefinition> > assetsToDayClose = new HashMap<>();
        List<Integer> finalInventoryListsWeekly = frequencyClassMapping.get(StockTakeFrequencyEnum.WEEKLY.value());
        assetsToDayClose.put(StockTakeSubType.WEEKLY_DAYCLOSE.value(), availableAssets.stream()
                .filter(FixedAssetCompactDefinition ->  finalInventoryListsWeekly.contains(FixedAssetCompactDefinition.getClassificationId()))
                .collect(Collectors.toList()));
        List<Integer> finalInventoryListsMonthly = frequencyClassMapping.get(StockTakeFrequencyEnum.MONTHLY.value());
        assetsToDayClose.put(StockTakeSubType.MONTHLY_DAYCLOSE.value(), availableAssets.stream()
                .filter(FixedAssetCompactDefinition ->  finalInventoryListsMonthly.contains(FixedAssetCompactDefinition.getClassificationId()))
                .collect(Collectors.toList()));
        return assetsToDayClose;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public VarianceEdit getCafeVarianceEditProducts(Integer unitId) {
        VarianceEdit varianceEdit = VarianceEdit.builder().canEditVariance(false).build();
        try {
            SCMDayCloseEventData dayCloseEventData = getLastSuccessfulDayCloseEventData(unitId);
            if (Objects.nonNull(dayCloseEventData)) {
                 varianceEdit.setDayCloseEventId(dayCloseEventData.getEventId());
                 varianceEdit.setVarianceEditStatus(dayCloseEventData.getVarianceStatus());
                 varianceEdit.setDayCloseSubmittedOn(dayCloseEventData.getGenerationTime());
                if (Objects.nonNull(dayCloseEventData.getVarianceUpdatedBy())) {
                    varianceEdit.setCanEditVariance(false);
                    varianceEdit.setVarianceUpdatedByName(SCMUtil.getCreatedBy(masterDataCache.getEmployee(dayCloseEventData.getVarianceUpdatedBy()), dayCloseEventData.getVarianceUpdatedBy()));
                }
                Date canEditVarianceTill = SCMUtil.addHoursToDate(AppUtils.getDateAfterDays(dayCloseEventData.getBusinessDate(), 1), 5);
                varianceEdit.setCanEditVarianceTill(canEditVarianceTill);
                if (AppUtils.getCurrentTimestamp().compareTo(canEditVarianceTill) < 0 && Objects.isNull(dayCloseEventData.getVarianceUpdatedBy())) {
                    varianceEdit.setCanEditVariance(true);
                }
                List<SCMProductInventoryData> scmProductInventoryDataList = stockManagementDao.getScmProductInventoryDataList(dayCloseEventData, true);
                List<VarianceEditItem> varianceEditItems = new ArrayList<>();
                scmProductInventoryDataList.forEach( scmProductInventoryData -> {
                    VarianceEditItem varianceEditItem = DomainDataMapper.INSTANCE.toVarianceEditItem(scmProductInventoryData);
                    if (scmProductInventoryData.getVariance().equals(scmProductInventoryData.getOriginalVariance())) {
                        varianceEditItem.setIsEdited(false);
                    }
                    varianceEditItem.setProductName(scmCache.getProductDefinition(varianceEditItem.getProductId()).getProductName());
                    varianceEditItems.add(varianceEditItem);
                });
                varianceEdit.setVarianceEditItems(varianceEditItems);
                return varianceEdit;
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred while getting Variance Edit Products :: ",e);
        }
        return varianceEdit;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public SCMDayCloseEventData getLastSuccessfulDayCloseEventData(Integer unitId) {
        return stockManagementDao.getLastSuccessfulDayCloseEventData(unitId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public VarianceEdit submitCafeVarianceEdit(VarianceEdit varianceEdit) throws SumoException {
        try {
            validateCafeVarianceEdit(varianceEdit);
            SCMDayCloseEventData dayCloseEventData = stockManagementDao.find(SCMDayCloseEventData.class, varianceEdit.getDayCloseEventId());
            if (Objects.nonNull(varianceEdit.getVarianceEditStatus()) && varianceEdit.getVarianceEditStatus().equalsIgnoreCase("ACKNOWLEDGED")) {
                LOG.info("Variance Of Day Close Id : {} is acknowledged By : {}",varianceEdit.getVarianceEditStatus(), varianceEdit.getVarianceUpdatedBy());
                dayCloseEventData.setVarianceUpdatedBy(varianceEdit.getVarianceUpdatedBy());
                dayCloseEventData.setVarianceStatus(varianceEdit.getVarianceEditStatus());
                stockManagementDao.update(dayCloseEventData, true);
                return getCafeVarianceEditProducts(dayCloseEventData.getUnitId());
            }
            LOG.info("Got Request for Editing Of cafe Variance for Day Close Id : {} ", varianceEdit.getDayCloseEventId());
            List<Integer> stockingIds = new ArrayList<>();
            for (VarianceEditItem varianceEditItem : varianceEdit.getVarianceEditItems()) {
                stockingIds.add(varianceEditItem.getStockingId());
            }
            List<SCMProductInventoryData> scmProductInventoryDataList = stockManagementDao.getScmProductInventoryDataListWithIds(stockingIds);
            List<StockEntryEventData> stockEntryEventData = stockManagementDao.getStockEntryEventDataForDayCloseId(dayCloseEventData.getEventId());
            Map<Integer, StockEntryEventData> stockEntryEventDataMap = new HashMap<>();
            if (Objects.nonNull(stockEntryEventData) && !stockEntryEventData.isEmpty()) {
                stockEntryEventDataMap = stockEntryEventData.stream().collect(Collectors.toMap(StockEntryEventData::getProductId, Function.identity()));
            }
            Map<Integer, SCMProductInventoryData> productWiseMap = scmProductInventoryDataList.stream().collect(Collectors.toMap(SCMProductInventoryData::getStockingId, Function.identity()));
            List<SCMProductInventoryData> productInventoryData = stockManagementDao.getScmProductInventoryDataList(dayCloseEventData, false);
            Map<Integer, List<SCMProductInventoryData>> inventoryDataByProductId = new HashMap<>();
            if (Objects.nonNull(productInventoryData) && !productInventoryData.isEmpty()) {
                inventoryDataByProductId = productInventoryData.stream().collect(Collectors.groupingBy(SCMProductInventoryData::getProductId));
            }
            VarianceVO negativeVariance = new VarianceVO(dayCloseEventData.getEventId(), dayCloseEventData.getUnitId(), PriceUpdateEntryType.PRODUCT);
            VarianceVO positiveVariance = new VarianceVO(dayCloseEventData.getEventId(), dayCloseEventData.getUnitId(), PriceUpdateEntryType.PRODUCT);
            VarianceVO all = new VarianceVO(dayCloseEventData.getEventId(), dayCloseEventData.getUnitId(), PriceUpdateEntryType.PRODUCT);
            List<Integer> productIds = new ArrayList<>();
            for (VarianceEditItem varianceEditItem : varianceEdit.getVarianceEditItems()) {
                stockingIds.add(varianceEditItem.getStockingId());
                int typeOfVariance = varianceEditItem.getFinalVariance().compareTo(BigDecimal.ZERO);
                SCMProductInventoryData inventoryData = Objects.isNull(productWiseMap.get(varianceEditItem.getStockingId())) ?
                        stockManagementDao.find(SCMProductInventoryData.class, varianceEditItem.getStockingId()) : productWiseMap.get(varianceEditItem.getStockingId());
                ProductStockForUnit productStockForUnit = getProductStockForUnit(varianceEditItem,inventoryData);
                BigDecimal varianceCost = SCMUtil.multiplyWithScale10(inventoryData.getVariancePrice(), varianceEditItem.getFinalVariance());
                productStockForUnit.setVarianceCost(varianceCost);
                inventoryData.setVarianceCost(varianceCost);
                inventoryData.setActualClosing(varianceEditItem.getFinalClosing());
                inventoryData.setVariance(varianceEditItem.getFinalVariance());
                if (Objects.nonNull(inventoryData.getTaxPercentage())) {
                    inventoryData.setVarianceTax(AppUtils.percentOfWithScale10(inventoryData.getVarianceCost(),
                            inventoryData.getTaxPercentage()));
                }
                if (typeOfVariance > 0) {
                    positiveVariance.getStockList().add(productStockForUnit);
                    productIds.add(productStockForUnit.getProductId());
                } else if (typeOfVariance < 0) {
                    negativeVariance.getStockList().add(productStockForUnit);
                    productIds.add(productStockForUnit.getProductId());
                }
                all.getStockList().add(productStockForUnit);
                inventoryData = stockManagementDao.update(inventoryData, true);
                if (stockEntryEventDataMap.containsKey(inventoryData.getProductId())) {
                    StockEntryEventData entryEventData = stockEntryEventDataMap.get(inventoryData.getProductId());
                    entryEventData.setCurrentStock(varianceEditItem.getFinalClosing());
                    stockManagementDao.update(entryEventData, true);
                }
                productWiseMap.put(varianceEditItem.getStockingId(), inventoryData);
                List<SCMProductInventoryData> inventoryDataList = inventoryDataByProductId.get(varianceEditItem.getProductId());
                if (Objects.nonNull(inventoryDataList) && !inventoryDataList.isEmpty()) {
                    for (SCMProductInventoryData scmProductInventoryData : inventoryDataList) {
                        // recalculating variance based on Closing
                        BigDecimal finalVariance = scmProductInventoryData.getExpectedClosing().subtract(varianceEditItem.getFinalClosing());
                        BigDecimal varCost = SCMUtil.multiplyWithScale10(scmProductInventoryData.getVariancePrice(), finalVariance);
                        scmProductInventoryData.setVarianceCost(varCost);
                        scmProductInventoryData.setActualClosing(varianceEditItem.getFinalClosing());
                        scmProductInventoryData.setVariance(finalVariance);
                        if (Objects.nonNull(scmProductInventoryData.getTaxPercentage())) {
                            scmProductInventoryData.setVarianceTax(AppUtils.percentOfWithScale10(scmProductInventoryData.getVarianceCost(),
                                    scmProductInventoryData.getTaxPercentage()));
                        }
                        stockManagementDao.update(scmProductInventoryData, true);
                    }
                }
            }

            List<CostDetailData> currentPrices = getPriceDao().getCurrentPrices(PriceUpdateEntryType.PRODUCT, dayCloseEventData.getUnitId(), productIds, true);
            Map<Integer, CostDetailData> currentPriceMap = null;
            try {
                currentPriceMap = currentPrices.stream()
                        .collect(Collectors.toMap(CostDetailData::getKeyId, Function.identity()));
            } catch (Exception e) {
                try {
                    currentPriceMap = goodsReceiveManagementService.getPriceDao().fixPricing(currentPrices).stream()
                            .collect(Collectors.toMap(CostDetailData::getKeyId, Function.identity()));
                } catch (Exception e1) {
                    LOG.error("Error while fixing pricing :::: ", e);
                    String message = "Error while fixing prices for duplicate entries for pricing";
                    List<Integer> costIds = currentPrices.stream().mapToInt(CostDetailData::getCostDetailDataId).boxed()
                            .collect(Collectors.toList());
                    message = message + "\n" + costIds;
                    notificationService.sendDayClosureAlarmNotification(dayCloseEventData.getUnitId(), message);
                }
            }
            getPriceDao().reduceConsumable(positiveVariance, false);
            getPriceDao().addReceiving(negativeVariance, false);
            getPriceDao().overrideInventory(all);
            dayCloseEventData.setVarianceStatus("EDITED");
            dayCloseEventData.setVarianceUpdatedBy(varianceEdit.getVarianceUpdatedBy());
            dayCloseEventData = stockManagementDao.update(dayCloseEventData, true);
            getPriceDao().deleteObsoletePrices(dayCloseEventData.getUnitId(), PriceUpdateEntryType.PRODUCT, dayCloseEventData.getEventId());
            reportingService.sendVarianceReport(dayCloseEventData.getBusinessDate(), dayCloseEventData.getUnitId(), false, null);
            publishToLiveInventory(dayCloseEventData.getUnitId(), dayCloseEventData);
            saveAggregatedVarianceData(productWiseMap, dayCloseEventData);
            varianceAcknowledgementDetailsAfterEdit(dayCloseEventData);
            return getCafeVarianceEditProducts(dayCloseEventData.getUnitId());
        } catch (SumoException e) {
            throw e;
        }
        catch (Exception e) {
            LOG.error("Exception Occurred While submitting Cafe Variance Edit :::: ", e);
        }
        return null;
    }

    private void saveAggregatedVarianceData(Map<Integer, SCMProductInventoryData> productWiseMap, SCMDayCloseEventData dayCloseEventData) throws SumoException {
        BigDecimal aggregateCost = BigDecimal.ZERO;
        BigDecimal aggregateTax = BigDecimal.ZERO;
        for (Map.Entry<Integer, SCMProductInventoryData> entry : productWiseMap.entrySet()) {
            SCMProductInventoryData scmProductInventoryData = entry.getValue();
            BigDecimal varianceCost = Objects.nonNull(scmProductInventoryData.getVarianceCost()) ? scmProductInventoryData.getVarianceCost() : BigDecimal.ZERO;
            BigDecimal originalVarianceCost = Objects.nonNull(scmProductInventoryData.getOriginalVarianceCost()) ? scmProductInventoryData.getOriginalVarianceCost() : BigDecimal.ZERO;
            BigDecimal varianceTax = Objects.nonNull(scmProductInventoryData.getVarianceTax()) ? scmProductInventoryData.getVarianceTax() : BigDecimal.ZERO;
            BigDecimal originalVarianceTax = Objects.nonNull(scmProductInventoryData.getOriginalVarianceTax()) ? scmProductInventoryData.getOriginalVarianceTax() : BigDecimal.ZERO;
            aggregateCost = aggregateCost.add(varianceCost.subtract(originalVarianceCost).abs());
            aggregateTax = aggregateTax.add(varianceTax.subtract(originalVarianceTax).abs());
        }
        VarianceEditAggregatedData varianceEditAggregatedData = new VarianceEditAggregatedData();
        varianceEditAggregatedData.setDayCloseEventId(dayCloseEventData.getEventId());
        varianceEditAggregatedData.setAggregateCost(aggregateCost);
        varianceEditAggregatedData.setAggregateTax(aggregateTax);
        stockManagementDao.add(varianceEditAggregatedData, true);
    }

    private void validateCafeVarianceEdit(VarianceEdit varianceEdit) throws SumoException {
        SCMDayCloseEventData dayCloseEventData = stockManagementDao.find(SCMDayCloseEventData.class, varianceEdit.getDayCloseEventId());
        VarianceEdit edit = getCafeVarianceEditProducts(dayCloseEventData.getUnitId());
        if (!edit.getCanEditVariance()) {
            if (Objects.nonNull(edit.getVarianceUpdatedBy())) {
                throw new SumoException("Variance is already " + edit.getVarianceEditStatus(), "Variance for this Unit is already Edited By " + edit.getVarianceUpdatedByName());
            }
            throw new SumoException("Time Limit Exceeded..!", "Last Time to Edit Variance is <b>5:00 AM</b>");
        }
    }

    private ProductStockForUnit getProductStockForUnit(VarianceEditItem varianceEditItem, SCMProductInventoryData inventoryData) {
        ProductStockForUnit stockForUnit = new ProductStockForUnit();
        stockForUnit.setOpening(SCMUtil.convertToBigDecimal(inventoryData.getOpeningStock()));
        stockForUnit.setVariance(SCMUtil.convertToBigDecimal(inventoryData.getVariance()));
        stockForUnit.setOriginalVariance(SCMUtil.convertToBigDecimal(inventoryData.getOriginalVariance()));
        stockForUnit.setUom(inventoryData.getUnitOfMeasure());
        stockForUnit.setProductId(inventoryData.getProductId());
        stockForUnit.setInventoryId(inventoryData.getStockingId());
        stockForUnit.setStockValue(SCMUtil.convertToBigDecimal(varianceEditItem.getFinalClosing()));
        stockForUnit.setOriginalClosing(SCMUtil.convertToBigDecimal(inventoryData.getOriginalClosing()));
        stockForUnit.setExpectedValue(SCMUtil.convertToBigDecimal(inventoryData.getExpectedClosing()));
        stockForUnit.setKeyType(PriceUpdateEntryType.PRODUCT);
        stockForUnit.setPrice(inventoryData.getVariancePrice());
        return stockForUnit;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<WastageEvent> addReverseWastageEvent(ProductionBooking productionBooking, ReverseProductionBooking booking) throws DataNotFoundException, InventoryUpdateException, SumoException {
        Map<Integer, BookingConsumption> consumptionMap = new HashMap<>();
        for (BookingConsumption c : productionBooking.getBookingConsumption()) {
            flattenReverseConsumptionItems(consumptionMap, c);
        }
        WastageEvent wastageEvent = WastageEvent.builder().generatedBy(AppConstants.SYSTEM_EMPLOYEE_ID)
                .status(StockEventStatus.SETTLED)
                .unitId(booking.getUnitId())
                .generationTime(AppUtils.getCurrentTimestamp())
                .businessDate(AppUtils.getBusinessDate()).build();
        List<WastageData> wastageDataList = new ArrayList<>();
        for(Integer key : consumptionMap.keySet()) {
            WastageData data = generateWastageEventObject(consumptionMap.get(key));
            if (Objects.nonNull(data)){
                wastageDataList.add(data);
            }
        }
        wastageEvent.setItems(wastageDataList);
        if (wastageDataList.isEmpty()) {
            return new ArrayList<>();
        }
        return addManualWastageEvent(Arrays.asList(wastageEvent),false, booking.getUnitId());
    }

    private WastageData generateWastageEventObject(BookingConsumption reverseBookingConsumption) {
        if (Objects.nonNull(reverseBookingConsumption.getWastageQuantity()) &&
                reverseBookingConsumption.getWastageQuantity().compareTo(BigDecimal.ZERO) > 0){
            return (WastageData.builder()
                    .quantity(reverseBookingConsumption.getWastageQuantity())
                    .skuId(reverseBookingConsumption.getSkuId())
                    .comment("Wastage By Reverse Production Booking")
                    .build());
        }
        return null;

    }

    private void flattenReverseConsumptionItems(Map<Integer, BookingConsumption> consumptionMap, BookingConsumption c) {
        if (c.getBookingConsumption().size() == 0) {
            if (consumptionMap.get(c.getSkuId()) == null) {
                consumptionMap.put(c.getSkuId(), c);
            } else {
                BookingConsumption c1 = consumptionMap.get(c.getSkuId());
                c1.setCalculatedQuantity(SCMUtil.add(c1.getCalculatedQuantity(), c.getCalculatedQuantity()));
                c1.setWastageQuantity(SCMUtil.add(c1.getWastageQuantity(), c.getWastageQuantity()));
                consumptionMap.put(c1.getSkuId(), c1);
            }
        } else {
            for (BookingConsumption c1 : c.getBookingConsumption()) {
                flattenReverseConsumptionItems(consumptionMap, c1);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<WastageEvent> addManualWastageEvent(List<WastageEvent> wastageEvent, boolean verifyInventory,Integer unitId) throws InventoryUpdateException, DataNotFoundException, SumoException {

        for(WastageEvent w : wastageEvent) {
                List<WastageData> wastageItem = w.getItems();
                for (WastageData wd : wastageItem) {
                    if(wd.getSkuId()!=null) {
                        SkuDefinition sku = scmCache.getSkuDefinition(wd.getSkuId());
                        ProductDefinition pd = scmCache.getProductDefinition(sku.getLinkedProduct().getId());
                        if(pd.getCategoryDefinition().getId()==3){
                            throw new SumoException("Wastage Error","Cannot do wastage of fixed assest, productId : "+pd.getProductId()+" productName : "+pd.getProductName());
                        }
                        if (pd.getCategoryDefinition().getId() == 4 && Objects.equals(wd.getComment(), "Internal Consumption")) {
                            throw new SumoException("Wastage Error", "Cannot do internal consumption of semi finished product, productId : " + pd.getProductId() + " skuId : " + wd.getSkuId() + " skuName : " + sku.getSkuName());
                        }
                    } else if (wd.getProductId()!=null){
                        ProductDefinition pd = scmCache.getProductDefinition(wd.getProductId());
                        if(pd.getCategoryDefinition().getId()==3){
                            throw new SumoException("Wastage Error","Cannot do wastage of fixed assest, productId : "+pd.getProductId()+" productName : "+pd.getProductName());
                        }
                        if (pd.getCategoryDefinition().getId() == 4 && Objects.equals(wd.getComment(), "Internal Consumption")) {
                            throw new SumoException("Wastage Error", "Cannot do internal consumption of semi finished product, productId : " + pd.getProductId() + " productName : "+pd.getProductName());
                        }
                    }
                }
        }
        if(wastageEvent.get(0).getUnitId()<=0){
            wastageEvent.get(0).setUnitId(unitId);
        }
        if (masterDataCache.getUnit(wastageEvent.get(0).getUnitId()).getFamily().value().equalsIgnoreCase(UnitCategory.CAFE.value()) && verifyInventory) {
            verifyInventoryOfWastage(wastageEvent);
        }
//        if (SCMUtil.isWareHouseOrKitchen(scmCache.getUnitDetail(wastageEvent.get(0).getUnitId()))) {
//            verifyInventoryForKeys(wastageEvent.get(0), true);
//        }
        return addManualWastage(wastageEvent);
    }

    private void verifyInventoryOfWastage(List<WastageEvent> wastageEvent) throws SumoException {
        try {
            List<String> exceptionalProducts = new ArrayList<>();
            Map<Integer, BigDecimal> inventory = getScmProductInventory(wastageEvent.get(0).getUnitId(),false, PriceUpdateEntryType.PRODUCT.name());
            for (WastageEvent event : wastageEvent) {
                for (WastageData wastageData : event.getItems()) {
                    BigDecimal inventoryOfCurrent = inventory.getOrDefault(wastageData.getProductId(), BigDecimal.ZERO);
                    if (inventoryOfCurrent.compareTo(wastageData.getQuantity()) < 0) {
                        String errorMsg = "Wastage Added For Product : " + scmCache.getProductDefinition(wastageData.getProductId()).getProductName() +
                                " is : " + wastageData.getQuantity() + " and available inventory is : " + inventoryOfCurrent;
                        exceptionalProducts.add(errorMsg);
                    }
                }
            }
            if (!exceptionalProducts.isEmpty()) {
                LOG.info("Inventory not available for Unit : {} and event generated by : {}",wastageEvent.get(0).getUnitId(), masterDataCache.getEmployee(wastageEvent.get(0).getGeneratedBy()));
                throw new SumoException("Inventory Not Available", "Inventory is not available for : " + Arrays.toString(exceptionalProducts.toArray()));
            }
        } catch (SumoException e) {
            throw e;
        }
        catch (Exception e) {
            LOG.error("Exception Occurred While verifying the inventory of Wastage :::: ",e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public SCMDayCloseEventData getSumoDayCloseStatus(Integer unitId, Date previousDate) {
        List<UnitHours> unitHours = masterDataCache.getOperationalHoursForUnit(unitId);
        int retryCount =0;
        boolean retry = true;
        while (retry && retryCount<7) {
            try {
                UnitHours hours = unitHours.get(AppUtils.getDayOfWeek(previousDate)-1);
                if(Objects.nonNull(hours) && hours.isIsOperational()){
                    retry = false;
                } else {
                    previousDate = AppUtils.getPreviousBusinessDate(previousDate);
                }
            } catch (Exception e) {
                previousDate = AppUtils.getPreviousDate(previousDate);
            }
            retryCount++;
        }

        LOG.info("Checking For Business Date : {} ",previousDate);
        SCMDayCloseEventData dayCloseEventData= stockManagementDao.getSumoDayCloseStatus(unitId, previousDate);
        if (Objects.isNull(dayCloseEventData)) {
            dayCloseEventData= stockManagementDao.getSumoDayCloseStatus(unitId, AppUtils.getCurrentDate());
        }
        return dayCloseEventData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void verifyDayCloses(List<Integer> unitIds) {
        List<SCMDayCloseEventData> scmDayCloseEventData = stockManagementDao.verifyDayCloses(unitIds);
        Set<Integer> unitSet = new HashSet<>(unitIds);
        scmDayCloseEventData.forEach(
                dce -> unitSet.remove(dce.getUnitId())
        );
        List<Integer> failedUnitDayClose = new ArrayList<>(new ArrayList<>(unitSet));
        for(Integer unitId : failedUnitDayClose) {
            Unit unit = masterDataCache.getUnit(unitId);
            String msg = "Unit Operations are shut down due to pending day close on SUMO ("+ unitId + ":::" +unit.getName()+")";
            SlackNotificationService.getInstance().sendNotification(props.getEnvType(), null, SlackNotification.DAY_CLOSURE_STATUS, msg);
            SlackNotificationService.getInstance().sendNotification(props.getEnvType(), null, unit.getCafeManager().getId()+"_notify", msg);
            SlackNotificationService.getInstance().sendNotification(props.getEnvType(), null, unit.getManagerId()+"_notify", msg);
        }
        if(!failedUnitDayClose.isEmpty()){
            postRequestToTogglePartnerOff(failedUnitDayClose, AppConstants.CHAAYOS_BRAND_ID);
            postRequestToTogglePartnerOff(failedUnitDayClose, AppConstants.GNT_BRAND_ID);
            postRequestToInValidateCache(failedUnitDayClose);
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void getF9ScmProductInventory(Integer unitId, Date firstDate, Date lastDate, Set<Integer> exceptionalProducts,
                                                             List<String> exceptionProducts, List<String> dates, Map<Integer, Map<String, Float>> result) {
        try {
            LOG.info("## Trying to get SCM Products Stock from Cost Detail Data ##");
            List<CostDetail> costDetails = fetchUnitProductInventory(unitId, PriceUpdateEntryType.PRODUCT.name());
            for (CostDetail detail : costDetails) {
                if (exceptionalProducts.contains(detail.getKeyId())) {
                    LOG.info("Product Id : {} is already added in Exceptional Products", detail.getKeyId());
                    continue;
                }
                String dayType = AppUtils.getFormattedTime(SCMUtil.getScmBusinessDate(detail.getExpiryDate()), "yyyy-MM-dd");
                if (lastDate.compareTo(SCMUtil.getScmBusinessDate(detail.getExpiryDate())) < 0) {
                    dayType = AppUtils.getFormattedTime(lastDate, "yyyy-MM-dd");
                }
                if (SCMUtil.getScmBusinessDate(detail.getExpiryDate()).compareTo(firstDate) < 0) {
                    LOG.info("In IN_STOCK for Product Id : {} And Quantity : {} Expiry date is : {} and First Ordering Date is : {}",
                            detail.getKeyId(), scmCache.getProductDefinition(detail.getKeyId()).getProductName(), detail.getExpiryDate(), firstDate);
                    String exceptionMessage = scmCache.getProductDefinition(detail.getKeyId()).getProductName() + "_" + detail.getKeyId() + " [ " + detail.getExpiryDate() + " - " + detail.getQuantity() + " ]";
                    exceptionProducts.add(exceptionMessage);
                    exceptionalProducts.add(detail.getKeyId());
                }
                if (!dates.contains(dayType)) {
                    LOG.info("Day type is not in between the Dates IN-stock Inventory ..!");
                    exceptionalProducts.add(detail.getKeyId());
                    continue;
                }
                if (result.containsKey(detail.getKeyId())) {
                    Map<String, Float> innerMap = result.get(detail.getKeyId());
                    if (innerMap.containsKey(dayType)) {
                        innerMap.put(dayType, innerMap.get(dayType) + detail.getQuantity().floatValue());
                    } else {
                        innerMap.put(dayType, detail.getQuantity().floatValue());
                    }
                    result.put(detail.getKeyId(), innerMap);
                } else {
                    Map<String, Float> innerMap = new HashMap<>();
                    innerMap.put(dayType, detail.getQuantity().floatValue());
                    result.put(detail.getKeyId(), innerMap);
                }
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While getting scm product Live stock ::: ", e);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, BigDecimal> getScmProductInventory(Integer unitId, Boolean removeExpired, String keyType) {
        Map<Integer, BigDecimal> result = new HashMap<>();
        try {
            LOG.info("## Trying to get SCM Products Stock from Cost Detail Data ##");
            List<CostDetail> costDetails = fetchUnitProductInventory(unitId, keyType);
            Date requestTime = AppUtils.getCurrentTimestamp();
            for (CostDetail detail : costDetails) {
                if (detail.getExpiryDate().compareTo(requestTime) >= 0 || !removeExpired) {
                    if (result.containsKey(detail.getKeyId())) {
                        result.put(detail.getKeyId(), result.get(detail.getKeyId()).add(detail.getQuantity()));
                    } else {
                        result.put(detail.getKeyId(), detail.getQuantity());
                    }
                }
            }
            Set<Integer> productIds = new HashSet<>(result.keySet());
            Set<Integer> availableProductIds = PriceUpdateEntryType.PRODUCT.name().equalsIgnoreCase(keyType) ? scmCache.getProductDefinitions().keySet() : scmCache.getSkuDefinitions().keySet();
            for (Integer productId : availableProductIds) {
                if (!productIds.contains(productId)) {
                    result.put(productId, BigDecimal.ZERO);
                }
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While getting scm product Live stock ::: ",e);
        }
        return result;
    }

    @Override
    public Map<Integer, BigDecimal> getScmInventoryOfKeys(Integer unitId, List<Integer> keyIds, PriceUpdateEntryType keyType, boolean fetchOnlyLatest) {
        List<CostDetailData> inventory = priceManagementDao.getCurrentPrices(keyType, unitId, keyIds, fetchOnlyLatest);
        return inventory.stream()
                .collect(Collectors.groupingBy(CostDetailData::getKeyId,
                        Collectors.reducing(BigDecimal.ZERO, CostDetailData::getQuantity, SCMUtil::add)
                ));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public <T extends ConsumptionVO> void verifyInventoryForKeys(T rec, boolean ignoreSemiFinished) throws SumoException {
        if (rec.isAssetOrder()) {
            return;
        }
        Map<Integer, BigDecimal> itemsGroupedByQuantity = rec.getItemsGroupedByQuantity();
        boolean isWhKitchen = SCMUtil.isWareHouseOrKitchen(scmCache.getUnitDetail(rec.getUnitId()));
        Map<Integer, BigDecimal> scmInventory = getScmProductInventory(rec.getUnitId(), false, isWhKitchen ? PriceUpdateEntryType.SKU.value() : PriceUpdateEntryType.PRODUCT.value());
        boolean isKitchen = SCMUtil.isKitchen(masterDataCache.getUnit(rec.getUnitId()).getFamily());
        try {
            List<String> exceptionalProducts = new ArrayList<>();
            for (Map.Entry<Integer, BigDecimal> entry : itemsGroupedByQuantity.entrySet()) {
                if (isKitchen && ignoreSemiFinished) {
                    SkuDefinition skuDefinition = scmCache.getSkuDefinition(entry.getKey());
                    if (skuDefinition != null) {
                        ProductDefinition productDefinition = scmCache.getProductDefinition(skuDefinition.getLinkedProduct().getId());
                        if (productDefinition != null && productDefinition.isRecipeRequired()) {
                            LOG.info("Not Checking inventory for Sku : {} , product : {} for unit Id : {}", skuDefinition.getSkuName(), productDefinition.getProductName(), rec.getUnitId());
                            continue;
                        }
                    }
                }
                BigDecimal inventoryOfCurrent = scmInventory.getOrDefault(entry.getKey(), BigDecimal.ZERO);
                if (inventoryOfCurrent.compareTo(entry.getValue()) < 0) {
                    String errorMsg = "For " + (isWhKitchen ? PriceUpdateEntryType.SKU.value() : PriceUpdateEntryType.PRODUCT.value()) +
                            " : " + (isWhKitchen ? scmCache.getSkuDefinition(entry.getKey()).getSkuName() : scmCache.getProductDefinition(entry.getKey()).getProductName()) +
                            " trying to perform action on : " + entry.getValue() + " and available inventory is : " + inventoryOfCurrent;
                    exceptionalProducts.add(errorMsg);
                }
            }
            if (!exceptionalProducts.isEmpty()) {
                throw new SumoException("Inventory Not Available", "Inventory is not available for : " + Arrays.toString(exceptionalProducts.toArray()));
            }
        } catch (SumoException e) {
            throw e;
        }
        catch (Exception e) {
            LOG.error("Exception Occurred While verifying the inventory :::: ",e);
            throw new SumoException("Inventory Not Available", "Inventory is not available for : " + e);
        }
    }

    @Override
    public PendingMilkBread getPendingMilkBread(Integer unitId) {
        PendingMilkBread pendingMilkBread = scmCache.getPendingMilkBread(unitId);
        if (Objects.isNull(pendingMilkBread)) {
            pendingMilkBread = new PendingMilkBread(Boolean.TRUE);
        }
        pendingMilkBread.setServerTime(AppUtils.getCurrentTimestamp());
        return pendingMilkBread;
    }

    @Override
    public List<CostDetail> fetchUnitProductInventory(Integer unitId, String keyType) {
        List<CostDetail> list = new ArrayList<>();
        try {
            List<CostDetailData> l = stockManagementDao.fetchUnitProductInventory(unitId, keyType);
            if(Objects.nonNull(l)) {
                for (CostDetailData d : l) {
                    CostDetail cd = SCMDataConverter.convert(d);
                    if (keyType.equalsIgnoreCase(PriceUpdateEntryType.PRODUCT.value())) {
                        cd.setName(scmCache.getProductDefinition(d.getKeyId()).getProductName());
                    } else {
                        cd.setName(scmCache.getSkuDefinition(d.getKeyId()).getSkuName());
                    }
                    list.add(cd);
                }
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While getting Unit Product Inventory :: ",e);
        }
        return list;
    }

    private void postRequestToInValidateCache(List<Integer> failedUnitDayClose) {
        try {
            LOG.info("In Validating User Session for {} Unit ", failedUnitDayClose.size());
            com.stpl.tech.master.core.WebServiceHelper.postRequestWithAuthInternal(getProperties().getMasterServiceBasePath()+"/master-service" +
                            Endpoints.IN_VALIDATE_USER_SESSION, getProperties().getAuthToken(),
                    failedUnitDayClose);
        } catch (Exception e) {
            LOG.error("Exception caught while In Validating User Session for {} Unit ::: ",failedUnitDayClose.size(),e);
        }
    }

    private void postRequestToTogglePartnerOff(List<Integer> failedUnitDayClose, int brandId) {
        try {
            LOG.info("Toggle {} Unit off on partner", failedUnitDayClose.size());
            com.stpl.tech.master.core.WebServiceHelper.postRequestWithAuthInternal(getProperties().getKnockBaseUrl() +
                            Endpoints.PARTNER_UNIT_TOGGLE_OFF, getProperties().getKnockMasterToken(),
                    UnitPartnerStatusVO.builder()
                            .partnerIds(Arrays.asList(AppConstants.CHANNEL_PARTNER_ZOMATO, AppConstants.CHANNEL_PARTNER_SWIGGY))
                            .brandId(brandId)
                            .status(false)
                            .unitIds(failedUnitDayClose));
        } catch (Exception e) {
            LOG.error("Exception caught while toggling off the {} units on partner for brand Id {} ::: ", failedUnitDayClose.size(), brandId, e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void getMissingPrices() throws DataNotFoundException, EmailGenerationException {
        String recipeApprovedUrl = props.getMasterServiceBasePath() + "/master-service/rest/v1/" + "recipe/get-approved-recipes-by-today";
        ScmMissingPriceResponse recipeResponse = WebServiceHelper.postWithAuth(recipeApprovedUrl,props.getAuthToken(),
                new HashMap<>(), ScmMissingPriceResponse.class);
        if(Objects.isNull(recipeResponse) || Objects.isNull(recipeResponse.getRecipeDetailList())
        || recipeResponse.getRecipeDetailList().isEmpty()){
            LOG.info("No Recipe Found To Check For Missing Prices");
            return;
        }
        LOG.info("{} Recipes Found To Check For Missing Prices ",recipeResponse.getUnitDetailListByRecipeId().size());

        List<RecipeDetail> recipeDetailList = recipeResponse.getRecipeDetailList();
        Map<String,Pair<String,String>> regionToUnitProductMap= new HashMap<>();
        for (RecipeDetail recipeDetail : recipeDetailList) {
            List<RecipeDetail> addOnRecipeDetail = recipeResponse.getAddOnRecipeDetailByRecipeId().get(recipeDetail.getRecipeId());
            List<Integer> addonRecipeProductIds = getAddOnActiveProducts(addOnRecipeDetail);
            List<Integer> recipeProductIds = getRecipeProductIds(recipeDetail);
            Set<Integer> uniqueProducts = new HashSet<>();
            uniqueProducts.addAll(addonRecipeProductIds);
            uniqueProducts.addAll(recipeProductIds);
            Set<Unit> units = recipeResponse.getUnitDetailListByRecipeId().get(recipeDetail.getRecipeId()).stream().
                    map(unitId -> masterDataCache.getUnit(unitId)).collect(Collectors.toSet());
            LOG.info("Recipe Id : {} is Mapped To {} Units ",recipeDetail.getRecipeId(),units.size());
            Map<String,List<Integer>> unitsByRegion = units.stream().collect(Collectors.groupingBy(Unit::getRegion,
                    Collectors.mapping(Unit::getId,Collectors.toList())));
            LOG.info("Recipe Id : {} is Mapped To {} unique Regions ",recipeDetail.getRecipeId(),unitsByRegion.size());

            for(String region : unitsByRegion.keySet()){
                if(Objects.isNull(region)){
                    continue;
                }
                try {
                    getPriceDao().getOrCreateCurrentPrices(PriceUpdateEntryType.PRODUCT,unitsByRegion.get(region).get(0),
                            new ArrayList<>( uniqueProducts),false,true,true);
                    stockManagementDao.flush();
                }catch (Exception e){
                    stockManagementDao.flush();
                    LOG.info("Could n't Create Missing Prices For Some Products");
                }
                List<CostDetailData> costDetailDataList = getPriceDao().getAllCurrentPriceForAnyUnitInRegionByKeyId(PriceUpdateEntryType.PRODUCT,
                        new ArrayList<>( uniqueProducts),Collections.singletonList(region),false);
                Map<Integer,List<CostDetailData>> productCurrentPriceMap = costDetailDataList.stream().collect(Collectors.groupingBy(CostDetailData::getKeyId));
                Set<Integer> foundProductIds = productCurrentPriceMap.keySet();
                Set<Integer> missingProductIds = uniqueProducts.stream().filter(id -> !foundProductIds.contains(id)).collect(Collectors.toSet());
                if(missingProductIds.isEmpty()){
                    continue;
                }
                String unitNames = "";
                String productNames = "";
                if(regionToUnitProductMap.containsKey(region)){
                      Set<String> currentUnits = Arrays.stream(regionToUnitProductMap.get(region).getKey().split(", ")).collect(Collectors.toSet());
                      Set<String> currentProducts = Arrays.stream(regionToUnitProductMap.get(region).getValue().split(", ")).collect(Collectors.toSet());
                      Set<String> newUnits = unitsByRegion.get(region).stream().map(unitId -> masterDataCache.
                              getUnit(unitId).getName()).collect(Collectors.toSet());
                      Set<String> newProducts = missingProductIds.stream().map(productId -> scmCache.getProductDefinition(productId)
                              .getProductName()).collect(Collectors.toSet());
                      currentUnits.addAll(newUnits);
                      currentProducts.addAll(newProducts);
                      unitNames = currentUnits.stream().collect(Collectors.joining(", "));
                      productNames = currentProducts.stream().collect(Collectors.joining(", "));

                }else{
                    unitNames = unitsByRegion.get(region).stream().map(unitId -> masterDataCache.
                            getUnit(unitId).getName()).collect(Collectors.joining(", "));
                    productNames = missingProductIds.stream().map(productId -> scmCache.getProductDefinition(productId)
                            .getProductName()).collect(Collectors.joining(", "));
                }
                regionToUnitProductMap.put(region,new Pair<>(unitNames,productNames));

            }
        }
        if(!regionToUnitProductMap.isEmpty()){
            ScmMissingPricesNotificationTemplate template = new ScmMissingPricesNotificationTemplate(regionToUnitProductMap,props.getBasePath());
            ScmMissingPricesNotification notification = new ScmMissingPricesNotification(template,props.getEnvType(),
                    new ArrayList<>(Arrays.asList("<EMAIL>")));
            notification.sendEmail();
        }
    }

    private List<Integer> getAddOnActiveProducts(List<RecipeDetail> addOnRecipeDetail) {
        List<Integer> productIds = new ArrayList<>();
        try {
            for (RecipeDetail recipe : addOnRecipeDetail) {
                if (recipe == null || recipe.getIngredient() == null) {
                    continue;
                }
                if (recipe.getIngredient().getComponents() != null) {
                    for (IngredientProductDetail ipd : recipe.getIngredient().getComponents()) {
                        if (ipd.getStatus().equalsIgnoreCase(AppConstants.ACTIVE) && Objects.nonNull(recipe.getProduct()) && Objects.nonNull(recipe.getDimension())
                                && Objects.nonNull(recipe.getDimension().getCode())) {
                            productIds.add(ipd.getProduct().getProductId());
                        }
                    }
                }

            }
        } catch (Exception e) {
            LOG.error("Exception Occurred while getting Addon Products Map ::: ", e);
        }
        return productIds;
    }

    @Override
    public  List<Integer> getRecipeProductIds(RecipeDetail recipe) {
        List<Integer> productIds = new ArrayList<>();
        if (recipe == null || recipe.getIngredient() == null) {
            return productIds;
        }

        if (recipe.getIngredient().getProducts() != null) {
            for (IngredientProduct id : recipe.getIngredient().getProducts()) {
                for (IngredientProductDetail ipd : id.getDetails()) {
                    if (Objects.nonNull(ipd.getProduct())) {
                        productIds.add(ipd.getProduct().getProductId());
                    }
                }
            }
        }
        if (recipe.getIngredient().getVariants() != null) {
            for (IngredientVariant iv : recipe.getIngredient().getVariants()) {
                for (IngredientVariantDetail ivd : iv.getDetails()) {
                    if (Objects.nonNull(ivd.getProductId())) {
                        productIds.add(ivd.getProductId());
                    }
                }
            }
        }
        if (recipe.getIngredient().getComponents() != null) {
            for (IngredientProductDetail ipd : recipe.getIngredient().getComponents()) {
                if (Objects.nonNull(ipd.getProduct())) {
                    productIds.add(ipd.getProduct().getProductId());
                }
            }
        }
        if(recipe.getDineInConsumables() != null) {
            for (IngredientProductDetail ipd : recipe.getDineInConsumables()) {
                if (Objects.nonNull(ipd.getProduct())) {
                    productIds.add(ipd.getProduct().getProductId());
                }
            }
        }

        if(recipe.getDeliveryConsumables()!=null){
            for(IngredientProductDetail ipd : recipe.getDeliveryConsumables()){
                if(Objects.nonNull(ipd.getProduct())){
                    productIds.add(ipd.getProduct().getProductId());
                }
            }
        }
        if(recipe.getTakeawayConsumables()!=null){
            for(IngredientProductDetail ipd : recipe.getTakeawayConsumables()){
                if(Objects.nonNull(ipd.getProduct())){
                    productIds.add(ipd.getProduct().getProductId());
                }
            }
        }

        return productIds;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Set<Integer> getUnitsForVarianceAcknowledge(Integer userId,String acknowledgementType) throws Exception {

        Set<Integer> l = new HashSet<>();

        if (acknowledgementType.equals("DAILY")) {
            for (UnitBasicDetail ubd : masterDataCache.getAllUnits()) {
                Unit unit = masterDataCache.getUnit(ubd.getId());

                if (ubd.getCategory().equals(UnitCategory.CAFE)) {
                    if (userId.equals(ubd.getCafeManagerId()) || userId.equals(ubd.getUnitManagerId())) {
                        l.add(ubd.getId());
                    }
                }else if(ubd.getCategory().equals(UnitCategory.WAREHOUSE) || ubd.getCategory().equals(UnitCategory.KITCHEN)) {
                    List<String> varianceAcknowledgementEmpIds;
                    if(Objects.nonNull(unit) && Objects.nonNull(unit.getVarianceAcknowledgementEmployees())){
                            varianceAcknowledgementEmpIds = Arrays.asList(unit.getVarianceAcknowledgementEmployees().split(","));
                            if(varianceAcknowledgementEmpIds.contains(userId.toString())){
                                l.add(ubd.getId());
                            }
                    }
                }
            }
        } else if (acknowledgementType.equals("WEEKLY")) {
            for (UnitBasicDetail ubd : masterDataCache.getAllUnits()) {
                Unit unit = masterDataCache.getUnit(ubd.getId());

                if(ubd.getCategory().equals(UnitCategory.CAFE)){
                    if(userId.equals(100044)   && ubd.getUnitZone().equals("WEST")){//Ajay Gahlot
                        l.add(ubd.getId());
                    }
                    else if(userId.equals(120817) && ubd.getUnitZone().equals("SOUTH")){//Kapil Malhotra
                        l.add(ubd.getId());
                    }
                    else if(userId.equals(121285) && ubd.getUnitZone().equals("NORTH")){//PRINCE YADAV
                        l.add(ubd.getId());
                    }
                }else if(ubd.getCategory().equals(UnitCategory.WAREHOUSE) || ubd.getCategory().equals(UnitCategory.KITCHEN)){
                    if(Objects.nonNull(unit.getVarianceAcknowledgementRequired()) && unit.getVarianceAcknowledgementRequired().equals(SCMUtil.YES)){
                        if(userId.equals(141644)){//Partho Sarkar
                            l.add(ubd.getId());
                        }
                    }
                }
            }
        }
        return l;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VarianceAcknowledgementDetail> getAcknowledgeVarianceData(String acknowledgementType, Integer unitId, String isAcknowledged,Integer userId) throws Exception{
        LOG.info("Getting variance data For Unit : {} and acknowledgementType : {} ",unitId,acknowledgementType);
        List<Integer> unitIds = new ArrayList<>();
        if(Objects.isNull(unitId)){
            Set<Integer> units = getUnitsForVarianceAcknowledge(userId,acknowledgementType);
            if(Objects.nonNull(units)){
                for(Integer id : units){
                    unitIds.add(id);
                }
            }
        }else {
            unitIds.add(unitId);
        }

        List<VarianceAcknowledgementDetail> vadl = new ArrayList<>();
        List<VarianceAcknowledgementData> varianceAcknowledgementDataList = stockManagementDao.getVarianceAcknowledgementData(null,null,unitIds,isAcknowledged,acknowledgementType);
        if(Objects.nonNull(varianceAcknowledgementDataList)){
            for(VarianceAcknowledgementData varianceAcknowledgementData : varianceAcknowledgementDataList){
                if(varianceAcknowledgementData.getAcknowledgementRequired().equals(SCMUtil.YES)){
                    VarianceAcknowledgementDetail vad = SCMDataConverter.convert(varianceAcknowledgementData);
                    vadl.add(vad);
                }
            }
            return vadl;
        }
        return  vadl;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean acknowledgeVariance(Integer id , Integer userId , String comment) throws Exception{
        try{
            LOG.info("Approving variance for id : {}",id);
            VarianceAcknowledgementData varianceAcknowledgementData = stockManagementDao.find(VarianceAcknowledgementData.class,id);
            if(Objects.nonNull(varianceAcknowledgementData)){
                varianceAcknowledgementData.setAcknowledged(SCMUtil.YES);
                varianceAcknowledgementData.setAcknowledgedBy(userId);
                varianceAcknowledgementData.setAcknowledgedTime(SCMUtil.getCurrentTimestamp());
                varianceAcknowledgementData.setComment(comment);
                stockManagementDao.flush();
                return true;
            }
            return false;
        }catch (Exception e){
            LOG.error("Error approving variance for id : {} ",id);
            throw e;
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<VarianceModal> getVarianceDetails(Integer unitId, Date businessDate , String stockTakeType, Integer SCMDayCloseEventId) throws Exception{
        try{
            LOG.info("Getting variance details for unit : {} and businessDate : {} and stockTakeType : {}",unitId,businessDate,stockTakeType);
            List<VarianceModal> varianceModalList;
            boolean isWareHouseOrKitchen = SCMUtil.isWareHouseOrKitchen(scmCache.getUnitDetail(unitId));
            if(isWareHouseOrKitchen){
                SCMDayCloseEventData scmDayCloseEventData = stockManagementDao.find(SCMDayCloseEventData.class,SCMDayCloseEventId);
                varianceModalList = stockManagementDao.getWareHouseVarianceStock(scmDayCloseEventData);
            }else {
                varianceModalList = stockManagementDao.getCafeVarianceStock(unitId, businessDate, StockTakeType.valueOf(stockTakeType));
            }
            if(Objects.nonNull(varianceModalList)){
                return varianceModalList;
            }
            return null;
        }catch (Exception e){
            LOG.error("Error getting variance details unit : {} and businessDate : {} and stockTakeType : {}",unitId,businessDate,stockTakeType);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void setWeeklyVarianceAcknowledgementData() throws Exception {
        try {
            List<Date> datesArray = new ArrayList<>();
            for (int i = 1; i < 8; i++) {
                Date date = SCMUtil.getOldDate(SCMUtil.getCurrentTimestamp(), i);
                datesArray.add(date);
            }
            List<Integer> WH_K_ALLOWED= Arrays.asList(26416, 26422 ,26423,26426,26495,26496,26506,26514,26515);
            List<Integer> unitIds = new ArrayList<>();
            List<UnitBasicDetail> units = masterDataCache.getAllUnits();
            for (UnitBasicDetail unitBasicDetail : units) {
                if (UnitStatus.ACTIVE.equals(unitBasicDetail.getStatus()) &&
                                unitBasicDetail.getCategory().equals(UnitCategory.CAFE) ||
                                        ((unitBasicDetail.getCategory().equals(UnitCategory.WAREHOUSE)  ||
                                unitBasicDetail.getCategory().equals(UnitCategory.KITCHEN)) &&
                                        WH_K_ALLOWED.contains(unitBasicDetail.getId()))  &&
                                                Objects.nonNull(unitBasicDetail.getVarianceAcknowledgementRequired()) &&
                                unitBasicDetail.getVarianceAcknowledgementRequired().equals(SCMUtil.YES))
                       {
                    unitIds.add(unitBasicDetail.getId());
                }
            }

            for (Integer unitId : unitIds) {
                List<Integer> ids = new ArrayList<>();
                ids.add(unitId);
                List<VarianceAcknowledgementData> vadl = stockManagementDao.getVarianceAcknowledgementData(SCMUtil.getOldDate(SCMUtil.getCurrentTimestamp(), 8), SCMUtil.getOldDate(SCMUtil.getCurrentTimestamp(), 1), ids, null,"DAILY");

                BigDecimal totalVarianceCost = BigDecimal.ZERO;
                BigDecimal totalInventoryCost = BigDecimal.ZERO;

                if (Objects.nonNull(vadl) && vadl.size()>0) {
                    VarianceAcknowledgementData varianceAcknowledgementData = new VarianceAcknowledgementData();
                    varianceAcknowledgementData.setGenerationTime(SCMUtil.getCurrentTimestamp());
                    varianceAcknowledgementData.setBusinessDate(SCMUtil.getCurrentDate());
                    varianceAcknowledgementData.setUnitId(unitId);
                    varianceAcknowledgementData.setAcknowledged(SCMUtil.NO);
                    varianceAcknowledgementData.setAcknowledgementRequired(SCMUtil.YES);

                    varianceAcknowledgementData.setAcknowledgementType(String.valueOf(StockTakeType.WEEKLY));

                    for (VarianceAcknowledgementData vad : vadl) {
                        totalInventoryCost = totalInventoryCost.add(vad.getInventoryCost());
                        totalVarianceCost = totalVarianceCost.add(vad.getVarianceCost());
                    }
                    varianceAcknowledgementData.setVarianceCost(totalVarianceCost);
                    varianceAcknowledgementData.setInventoryCost(totalInventoryCost);
                    BigDecimal variancePercentage = SCMUtil.multiplyWithScale(SCMUtil.divideWithScale(totalVarianceCost, totalInventoryCost, 6), BigDecimal.valueOf(100), 6);

//                    if(Objects.nonNull(variancePercentage) && (variancePercentage.compareTo(BigDecimal.valueOf(1)) > 0 || variancePercentage.compareTo(BigDecimal.valueOf(-1))<0)){
//                            varianceAcknowledgementData.setAcknowledgementRequired(SCMUtil.YES);
//                    }
                    varianceAcknowledgementData.setVariancePercentage(variancePercentage);
                    varianceAcknowledgementData = stockManagementDao.add(varianceAcknowledgementData, true);
                }
            }
        } catch (Exception e) {
            LOG.error("Error setting weekly variance Details ");
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public VarianceAcknowledgementCheck checkVarianceAcknowledgement(Integer unitId)  {

        LOG.info("Getting variance details for warning and blocking for unit : {}",unitId);
        VarianceAcknowledgementCheck vac = new VarianceAcknowledgementCheck();
        vac.setVarianceWarning(false);
        vac.setVarianceBlocking(false);
        vac.setVarianceWeeklyWarning(false);
        vac.setVarianceWeeklyBlocking(false);
        vac.setDaysLeftToBlock(0);
        vac.setDaysLeftToBlockWeekly(0);

        String ackReqForUnit = null;
        if(Objects.nonNull(masterDataCache) && Objects.nonNull(masterDataCache.getUnit(unitId))){
            ackReqForUnit = masterDataCache.getUnit(unitId).getVarianceAcknowledgementRequired();
        }

        if(Objects.nonNull(ackReqForUnit) && ackReqForUnit.equals(SCMUtil.YES)){
            VarianceAcknowledgementData vadWeekly= stockManagementDao.getLatestNonAcknowledgedData(unitId,SCMUtil.NO,String.valueOf(StockTakeType.WEEKLY));
            VarianceAcknowledgementData vad= stockManagementDao.getLatestNonAcknowledgedData(unitId,SCMUtil.NO,String.valueOf(StockTakeType.DAILY));

            if(Objects.nonNull(vadWeekly)){
                long daysDiffWeekly = SCMUtil.getDayDifference(vadWeekly.getBusinessDate(),SCMUtil.getCurrentBusinessDate());
                if(daysDiffWeekly>=7){
                    vac.setVarianceWeeklyBlocking(true);
                    return vac;
                }
            }
            boolean isWhKitchen = SCMUtil.isWareHouseOrKitchen(scmCache.getUnitDetail(unitId));
            if(Objects.nonNull(vad) && isWhKitchen ){
                long daysDiff = SCMUtil.getDayDifference(vad.getBusinessDate(),SCMUtil.getCurrentBusinessDate());
                if(daysDiff>=7){
                    vac.setVarianceBlocking(true);
                    return vac;
                }
            }
            if(Objects.nonNull(vadWeekly)) {
                long daysDiffWeekly = SCMUtil.getDayDifference(vadWeekly.getBusinessDate(),SCMUtil.getCurrentBusinessDate());
                if(daysDiffWeekly>=4){
                    vac.setVarianceWeeklyWarning(true);
                    vac.setDaysLeftToBlockWeekly((int) daysDiffWeekly);
                    return vac;
                }
            }
            if(Objects.nonNull(vad) && isWhKitchen) {
                long daysDiff = SCMUtil.getDayDifference(vad.getBusinessDate(),SCMUtil.getCurrentBusinessDate());
                if(daysDiff>=4){
                    vac.setVarianceWarning(true);
                    vac.setDaysLeftToBlock((int) daysDiff);
                }
            }
            return vac;
        }else {
            return vac;
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VarianceAcknowledgementDetail> getAcknowledgeVarianceData(String acknowledgementType, Integer unitId, Date date) throws Exception{
        LOG.info("Getting variance data For Unit : {} and acknowledgementType : {} ",unitId,acknowledgementType);

        List<VarianceAcknowledgementDetail> vadl = new ArrayList<>();
        List<VarianceAcknowledgementData> varianceAcknowledgementDataList = stockManagementDao.getVarianceAcknowledgementData(acknowledgementType,unitId,date);
        if(Objects.nonNull(varianceAcknowledgementDataList)){
            for(VarianceAcknowledgementData varianceAcknowledgementData : varianceAcknowledgementDataList){
                if(varianceAcknowledgementData.getAcknowledgementRequired().equals(SCMUtil.YES)){
                    VarianceAcknowledgementDetail vad = SCMDataConverter.convert(varianceAcknowledgementData);
                    vadl.add(vad);
                }
            }
            return vadl;
        }
        return  vadl;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<ApprovalDetail> getFoundAssetApprovals(final Integer unitId) throws SumoException {
        List<ApprovalDetailData> approvalDetailData = stockManagementDao.getAssetApprovals(null,unitId,null,null,ApprovalStatus.PENDING.value(),List.of(ApprovalType.FOUND_ASSET));
        List<ApprovalDetail> res = new ArrayList<>();
            for (ApprovalDetailData approval : approvalDetailData) {
                res.add(SCMDataConverter.convertToApprovalDetail(approval, scmCache, masterDataCache));
            }

        return res;
    }

    @Override
    public List<Integer> getUnitsForStockTakeThroughApp() {
        try {
            String unitList = props.getUnitsForStockTakeThroughApp();
            if (Objects.nonNull(unitList) && !unitList.equalsIgnoreCase("")) {
                return Arrays.stream(unitList.split(",")).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList());
            } else {
                return new ArrayList<>();
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While Getting getUnitsForStockTakeThroughApp ::: ", e);
            return new ArrayList<>();
        }
    }

    @Override
    public SCMDayCloseEventData getLatestKettleDayClose(Integer unitId) {
        try {
            return stockManagementDao.getLatestKettleDayClose(unitId);
        } catch (Exception e) {
            LOG.error("Exception Occurred While Getting getLatestKettleDayClose ::: ", e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public StockTakeSumoDayCloseEventDTO checkForStockTakeSumoDayClose(Integer unitId, Set<Integer> knownProductIds, String status) throws SumoException {
        try {
            LOG.info("Checking For Sumo Day CLose Event for Unit Id : {} and Status : {}", unitId, status);
            StockTakeSumoDayCloseEvent stockTakeSumoDayCloseTempEvent = stockManagementDao.checkForStockTakeSumoDayClose(unitId, Collections.singletonList(status), false, null);
            if (Objects.nonNull(stockTakeSumoDayCloseTempEvent)) {
                StockTakeSumoDayCloseEvent stockTakeSumoDayCloseEvent = stockManagementDao.checkForStockTakeSumoDayClose(unitId, Collections.singletonList(status), true, stockTakeSumoDayCloseTempEvent);
                LOG.info("Loaded Event Data for Unit Id : {} and Status : {}", unitId, status);
                if (status.equalsIgnoreCase(StockTakeSumoDayCloseStatus.SUBMITTED.value())) {
                    knownProductIds = stockTakeSumoDayCloseEvent.getStockTakeSumoDayCloseProducts().stream().mapToInt(StockTakeSumoDayCloseProducts::getProductId).boxed().collect(Collectors.toSet());
                }
                Set<Integer> productIds = new HashSet<>();
                if (Objects.nonNull(knownProductIds)) {
                    LOG.info("Got Some Known Product Ids For Event Id  : {} ", stockTakeSumoDayCloseEvent.getStockTakeSumoDayCloseEventId());
                    productIds = knownProductIds;
                } else {
                    LOG.info("Started Filtering Products For Event Id  : {} ", stockTakeSumoDayCloseEvent.getStockTakeSumoDayCloseEventId());
                    boolean isGntCafe = getCafeGntStatus(unitId);
                    List<ProductDefinition> allProductsByFrequency = getAllProductsByFrequency(StockTakeType.valueOf(stockTakeSumoDayCloseEvent.getStockTakeType()));
                    for (ProductDefinition productDefinition : allProductsByFrequency) {
                        if (stockTakeSumoDayCloseEvent.getStockTakeType().equalsIgnoreCase(StockTakeType.DAILY.value())) {
                            if (productDefinition.getStockKeepingFrequency().equals(StockTakeType.DAILY)) {
                                addToProductList(productIds, isGntCafe, productDefinition);
                            }
                        } else if (stockTakeSumoDayCloseEvent.getStockTakeType().equalsIgnoreCase(StockTakeType.WEEKLY.value())) {
                            if (productDefinition.getStockKeepingFrequency().equals(StockTakeType.DAILY) || productDefinition.getStockKeepingFrequency().equals(StockTakeType.WEEKLY)) {
                                addToProductList(productIds, isGntCafe, productDefinition);
                            }
                        } else {
                            if (productDefinition.getStockKeepingFrequency().equals(StockTakeType.DAILY)
                                    || productDefinition.getStockKeepingFrequency().equals(StockTakeType.WEEKLY)
                                    || productDefinition.getStockKeepingFrequency().equals(StockTakeType.MONTHLY)) {
                                addToProductList(productIds, isGntCafe, productDefinition);
                            }
                        }
                    }
                    List<Integer> unavailableInventory = getUnavailableInventory(unitId, stockTakeSumoDayCloseEvent, productIds);
                    productIds = productIds.stream().filter(e -> !unavailableInventory.contains(e)).collect(Collectors.toSet());
                    LOG.info("Completed Filtering Products For Event Id  : {} ", stockTakeSumoDayCloseEvent.getStockTakeSumoDayCloseEventId());
                }
                //Getting last Successfully Completed Event to get the Updated Time and Sorting Will be done on that
                Map<Integer, Date> lastCompletedEventProductsWithDate = new HashMap<>();
                StockTakeSumoDayCloseEvent lastCompletedTempEvent = stockManagementDao.checkForStockTakeSumoDayClose(unitId, Collections.singletonList(StockTakeSumoDayCloseStatus.COMPLETED.value()), false, null);
                if (Objects.nonNull(lastCompletedTempEvent)) {
                    StockTakeSumoDayCloseEvent lastCompletedEvent = stockManagementDao.checkForStockTakeSumoDayClose(unitId, Collections.singletonList(StockTakeSumoDayCloseStatus.COMPLETED.value()), true, lastCompletedTempEvent);
                    if (Objects.nonNull(lastCompletedEvent)) {
                        for (StockTakeSumoDayCloseProducts stockTakeSumoDayCloseProduct : stockTakeSumoDayCloseEvent.getStockTakeSumoDayCloseProducts()) {
                            lastCompletedEventProductsWithDate.put(stockTakeSumoDayCloseProduct.getProductId(), stockTakeSumoDayCloseProduct.getUpdatedTime());
                        }
                    }
                }
                Map<Integer, List<ProductPackagingMapping>> packagingsByProductId = scmProductManagementService.viewAllProductPackagingMapping();
                StockTakeSumoDayCloseEventDTO stockTakeSumoDayCloseEventDTO = DomainDataMapper.INSTANCE.toStockTakeSumoDayCloseEventDto(stockTakeSumoDayCloseEvent);
                stockTakeSumoDayCloseEventDTO.setEventCreatedByName(SCMUtil.getCreatedBy(masterDataCache.getEmployee(stockTakeSumoDayCloseEvent.getEventCreatedBy()), stockTakeSumoDayCloseEvent.getEventCreatedBy()));
                List<Integer> sumoDayCloseProductItemIds = stockTakeSumoDayCloseEvent.getStockTakeSumoDayCloseProducts().stream().mapToInt(StockTakeSumoDayCloseProducts::getSumoDayCloseProductItemId).boxed().collect(Collectors.toList());
                LOG.info("Started Getting List Of Products Unit ID : {}  For Event Id  : {} ", unitId, stockTakeSumoDayCloseEvent.getStockTakeSumoDayCloseEventId());
                List<StockTakeSumoDayCloseProducts> stockTakeSumoDayCloseProducts = sumoDayCloseProductItemIds.isEmpty() ? new ArrayList<>() : stockManagementDao.getListOfStockTakeSumoDayCloseProducts(sumoDayCloseProductItemIds);
                LOG.info("Completed Getting List Of Products Unit ID : {}  For Event Id  : {} ", unitId, stockTakeSumoDayCloseEvent.getStockTakeSumoDayCloseEventId());
                List<StockTakeSumoDayCloseProductsDTO> stockTakeSumoDayCloseProductsDTOS = new ArrayList<>();
                Set<Integer> addedProductIds = new HashSet<>();
                for (StockTakeSumoDayCloseProducts stockTakeSumoDayCloseProduct : stockTakeSumoDayCloseProducts) {
                    if (productIds.contains(stockTakeSumoDayCloseProduct.getProductId())) {
                        StockTakeSumoDayCloseProductsDTO dayCloseProductDTO = DomainDataMapper.INSTANCE.toStockTakeSumoDayCloseProductsDto(stockTakeSumoDayCloseProduct);
                        List<DayCloseProductPackagingMappingsDTO> packagingMappingsDTOS = new ArrayList<>();
                        Set<Integer> addedPackagingIds = new HashSet<>();
                        for (DayCloseProductPackagingMappings productPackagingMapping : stockTakeSumoDayCloseProduct.getDayCloseProductPackagingMappings()) {
                            DayCloseProductPackagingMappingsDTO packagingMappingsDTO = DomainDataMapper.INSTANCE.toDayCloseProductPackagingMappingsDto(productPackagingMapping);
                            addedPackagingIds.add(productPackagingMapping.getPackagingId());
                            PackagingDefinition packagingDefinition = scmCache.getPackagingDefinition(productPackagingMapping.getPackagingId());
                            packagingMappingsDTO.setPackagingName(packagingDefinition.getPackagingName());
                            packagingMappingsDTO.setConversionRatio(BigDecimal.valueOf(packagingDefinition.getConversionRatio()));
                            packagingMappingsDTOS.add(packagingMappingsDTO);
                        }
                        List<ProductPackagingMapping> productPackMappings = packagingsByProductId.getOrDefault(stockTakeSumoDayCloseProduct.getProductId(), new ArrayList<>());
                        if (Objects.nonNull(productPackMappings)) {
                            for (ProductPackagingMapping productPackMapping : productPackMappings) {
                                if (!addedPackagingIds.contains(productPackMapping.getPackagingId())) {
                                    PackagingDefinition packagingDefinition = scmCache.getPackagingDefinition(productPackMapping.getPackagingId());
                                    if (Objects.nonNull(packagingDefinition)) {
                                        DayCloseProductPackagingMappingsDTO packagingMappingsDTO = new DayCloseProductPackagingMappingsDTO(productPackMapping.getPackagingId(), packagingDefinition.getPackagingName(), packagingDefinition.getConversionRatio());
                                        packagingMappingsDTOS.add(packagingMappingsDTO);
                                    } else {
                                        LOG.info("No Packaging Definition Found for product id : {}", productPackMapping.getProductId());
                                    }
                                }
                            }
                        }
                        dayCloseProductDTO.setDayCloseProductPackagingMappings(packagingMappingsDTOS);
                        dayCloseProductDTO.setProductName(scmCache.getProductDefinition(stockTakeSumoDayCloseProduct.getProductId()).getProductName());
                        dayCloseProductDTO.setUom(scmCache.getProductDefinition(stockTakeSumoDayCloseProduct.getProductId()).getUnitOfMeasure());
                        dayCloseProductDTO.setProductType(scmCache.getProductDefinition(stockTakeSumoDayCloseProduct.getProductId()).getProductType());
                        addedProductIds.add(dayCloseProductDTO.getProductId());
                        stockTakeSumoDayCloseProductsDTOS.add(dayCloseProductDTO);
                    }
                }
                LOG.info("Completed Setting Products And Packaging's Which are available in db Unit ID : {}  For Event Id  : {} ", unitId, stockTakeSumoDayCloseEvent.getStockTakeSumoDayCloseEventId());
                //adding products into list which are not in db list
                for (Integer productId : productIds) {
                    if (!addedProductIds.contains(productId)) {
                        StockTakeSumoDayCloseProductsDTO dayCloseProductsDTO = new StockTakeSumoDayCloseProductsDTO();
                        dayCloseProductsDTO.setProductId(productId);
                        dayCloseProductsDTO.setProductName(scmCache.getProductDefinition(productId).getProductName());
                        dayCloseProductsDTO.setUom(scmCache.getProductDefinition(productId).getUnitOfMeasure());
                        dayCloseProductsDTO.setProductType(scmCache.getProductDefinition(productId).getProductType());
                        if (lastCompletedEventProductsWithDate.containsKey(productId) && Objects.nonNull(lastCompletedEventProductsWithDate.get(productId))) {
                            dayCloseProductsDTO.setUpdatedTime(lastCompletedEventProductsWithDate.get(productId));
                        }
                        List<ProductPackagingMapping> productPackMappings = packagingsByProductId.getOrDefault(productId, new ArrayList<>());
                        if (Objects.nonNull(productPackMappings)) {
                            for (ProductPackagingMapping productPackMapping : productPackMappings) {
                                PackagingDefinition packagingDefinition = scmCache.getPackagingDefinition(productPackMapping.getPackagingId());
                                if (Objects.nonNull(packagingDefinition)) {
                                    DayCloseProductPackagingMappingsDTO packagingMappingsDTO = new DayCloseProductPackagingMappingsDTO(productPackMapping.getPackagingId(), packagingDefinition.getPackagingName(), packagingDefinition.getConversionRatio());
                                    dayCloseProductsDTO.getDayCloseProductPackagingMappings().add(packagingMappingsDTO);
                                } else {
                                    LOG.info("No Packaging Definition Found for product id : {}", productPackMapping.getProductId());
                                }
                            }
                        }
                        stockTakeSumoDayCloseProductsDTOS.add(dayCloseProductsDTO);
                    }
                }
                LOG.info("Completed Setting Products And Packaging's Which are NOT available in db Unit ID : {}  For Event Id  : {} ", unitId, stockTakeSumoDayCloseEvent.getStockTakeSumoDayCloseEventId());
                stockTakeSumoDayCloseEventDTO.setStockTakeSumoDayCloseProductsDTOS(stockTakeSumoDayCloseProductsDTOS);
                return stockTakeSumoDayCloseEventDTO;
            } else {
                return new StockTakeSumoDayCloseEventDTO();
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred while checking for StockTakeSumoDayClose ::: ", e);
            throw new SumoException("DAY_CLOSE_EXCEPTION", "Error Occurred while Checking for In progress Events");
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_COMMITTED)
    public List<Integer> getUnavailableInventory(Integer unitId, StockTakeSumoDayCloseEvent stockTakeSumoDayCloseEvent, Set<Integer> productIds) {
        List<Integer> listOfProds = new ArrayList<>(productIds);
        LOG.info("Getting Un available Products For Unit ID : {}  For Event Id  : {} ", unitId, stockTakeSumoDayCloseEvent.getStockTakeSumoDayCloseEventId());
        List<CostDetailData> inventory = getPriceDao().getCurrentPrices(PriceUpdateEntryType.PRODUCT, unitId, listOfProds, false);
        LOG.info("GOt Un available Products For Unit ID : {}  For Event Id  : {} ", unitId, stockTakeSumoDayCloseEvent.getStockTakeSumoDayCloseEventId());
        List<Integer> unavailableInventory = new ArrayList<>();
        if (inventory != null) {
            Map<Integer, BigDecimal> inventoryLookup = inventory.stream()
                    .collect(Collectors.groupingBy(CostDetailData::getKeyId,
                            Collectors.reducing(BigDecimal.ZERO, CostDetailData::getQuantity, SCMUtil::add)
                    ));
            for (Integer item : productIds) {
                BigDecimal quantity = inventoryLookup.get(item);
                if (quantity == null) {
                    unavailableInventory.add(item);
                }
            }
        }
        return unavailableInventory;
    }

    private void addToProductList(Set<Integer> productIds, boolean isGntCafe, ProductDefinition productDefinition) {
        if (ProductType.GNT.value().equalsIgnoreCase(productDefinition.getProductType())) {
            if (isGntCafe) {
                productIds.add(productDefinition.getProductId());
            }
        } else {
            // Ignoring Discontinued Products
            if (!ProductType.DISCONTINUED.value().equalsIgnoreCase(productDefinition.getProductType())) {
                productIds.add(productDefinition.getProductId());
            }
        }
    }

    private boolean getCafeGntStatus(Integer unitId) {
        try {
            UnitPartnerBrandKey swiggyKey = new UnitPartnerBrandKey(unitId, AppConstants.GNT_BRAND_ID, AppConstants.CHANNEL_PARTNER_SWIGGY);
            UnitPartnerBrandKey zomatoKey = new UnitPartnerBrandKey(unitId, AppConstants.GNT_BRAND_ID, AppConstants.CHANNEL_PARTNER_ZOMATO);
            UnitPartnerBrandMappingData swiggyMapping = masterDataCache.getUnitPartnerBrandMappingMetaData().get(swiggyKey);
            if (Objects.nonNull(swiggyMapping) && AppConstants.ACTIVE.equalsIgnoreCase(swiggyMapping.getStatus())) {
                return true;
            }
            UnitPartnerBrandMappingData zomatoMapping = masterDataCache.getUnitPartnerBrandMappingMetaData().get(zomatoKey);
            if (Objects.nonNull(zomatoMapping) && AppConstants.ACTIVE.equalsIgnoreCase(zomatoMapping.getStatus())) {
                return true;
            }
            return false;
        } catch (Exception e) {
            LOG.error("Exception Occurred while getting GNT Status Of the Cafe ::: ", e);
            return true;
        }
    }


    private Set<Integer> getMandatoryProductIds(StockInventoryData expectedValues, StockTakeSumoDayCloseEvent stockTakeSumoDayCloseEvent) {
        Set<Integer> result = new HashSet<>();
        for (ProductStockForUnit productStockForUnit : expectedValues.getInventoryResponse()) {
            ProductDefinition productDefinition = scmCache.getProductDefinition(productStockForUnit.getProductId());
            if (stockTakeSumoDayCloseEvent.getStockTakeType().equalsIgnoreCase(StockTakeType.DAILY.value())) {
                if (productDefinition.getStockKeepingFrequency().equals(StockTakeType.DAILY) && checkForMovementInProduct(productStockForUnit)) {
                    result.add(productStockForUnit.getProductId());
                }
            } else if (stockTakeSumoDayCloseEvent.getStockTakeType().equalsIgnoreCase(StockTakeType.WEEKLY.value())) {
                if ((productDefinition.getStockKeepingFrequency().equals(StockTakeType.DAILY) || productDefinition.getStockKeepingFrequency().equals(StockTakeType.WEEKLY))
                        && checkForMovementInProduct(productStockForUnit)) {
                    result.add(productStockForUnit.getProductId());
                }
            } else {
                if ((productDefinition.getStockKeepingFrequency().equals(StockTakeType.DAILY)
                        || productDefinition.getStockKeepingFrequency().equals(StockTakeType.WEEKLY)
                        || productDefinition.getStockKeepingFrequency().equals(StockTakeType.MONTHLY))
                        && checkForMovementInProduct(productStockForUnit)) {
                    result.add(productStockForUnit.getProductId());
                }
            }
        }
        return result;
    }

    private boolean checkForMovementInProduct(ProductStockForUnit productStockForUnit) {
        if (Objects.nonNull(productStockForUnit.getOpening()) && productStockForUnit.getOpening().compareTo(BigDecimal.ZERO) != 0) {
            return true;
        } else if (Objects.nonNull(productStockForUnit.getReceived()) && productStockForUnit.getReceived().compareTo(BigDecimal.ZERO) != 0) {
            return true;
        } else if (Objects.nonNull(productStockForUnit.getTransferred()) && productStockForUnit.getTransferred().compareTo(BigDecimal.ZERO) != 0) {
            return true;
        } else if (Objects.nonNull(productStockForUnit.getWasted()) && productStockForUnit.getWasted().compareTo(BigDecimal.ZERO) != 0) {
            return true;
        } else if (Objects.nonNull(productStockForUnit.getConsumption()) && productStockForUnit.getConsumption().compareTo(BigDecimal.ZERO) != 0) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public StockTakeSumoDayCloseEventDTO startStockTakeSumoDayClose(StockTakeSumoDayCloseEventDTO stockTakeSumoDayCloseEventDTO) throws SumoException {
        try {
            StockTakeSumoDayCloseEvent stockTakeSumoDayCloseEvent = stockManagementDao.checkForStockTakeSumoDayClose(stockTakeSumoDayCloseEventDTO.getUnitId(),
                    Arrays.asList(StockTakeSumoDayCloseStatus.IN_PROGRESS.value(), StockTakeSumoDayCloseStatus.SUBMITTED.value()), false, null);
            if (Objects.nonNull(stockTakeSumoDayCloseEvent)) {
                throw new SumoException("DAY_CLOSE_EXCEPTION", "Event already " +
                        (StockTakeSumoDayCloseStatus.IN_PROGRESS.value().equalsIgnoreCase(stockTakeSumoDayCloseEvent.getEventStatus()) ? " CREATED" : " SUBMITTED")
                        + " by : " + SCMUtil.getCreatedBy(masterDataCache.getEmployee(stockTakeSumoDayCloseEvent.getEventCreatedBy()), stockTakeSumoDayCloseEvent.getEventCreatedBy()));
            } else {
                StockTakeSumoDayCloseEvent event = new StockTakeSumoDayCloseEvent();
                event.setEventStatus(StockTakeSumoDayCloseStatus.IN_PROGRESS.value());
                event.setEventCreatedAt(AppUtils.getCurrentTimestamp());
                event.setUnitId(stockTakeSumoDayCloseEventDTO.getUnitId());
                event.setEventCreatedBy(stockTakeSumoDayCloseEventDTO.getEventCreatedBy());
                event.setDeviceInfo(stockTakeSumoDayCloseEventDTO.getDeviceInfo());
                event.setStockTakeType(stockTakeSumoDayCloseEventDTO.getStockTakeType());
                StockTakeSumoDayCloseEvent dayCloseEvent = stockManagementDao.add(event, true);
                return DomainDataMapper.INSTANCE.toStockTakeSumoDayCloseEventDto(dayCloseEvent);
            }
        } catch (SumoException e) {
            throw e;
        } catch (Exception e) {
            LOG.error("Exception Occurred while checking for StockTakeSumoDayClose ::: ", e);
            return new StockTakeSumoDayCloseEventDTO();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateSumoDayCloseDeviceInfo(String deviceInfo, Integer eventId) throws SumoException {
        try {
            StockTakeSumoDayCloseEvent dayCloseEvent = stockManagementDao.find(StockTakeSumoDayCloseEvent.class, eventId);
            if (Objects.nonNull(dayCloseEvent)) {
                dayCloseEvent.setDeviceInfo(deviceInfo);
                dayCloseEvent.setLastUpdatedTime(AppUtils.getCurrentTimestamp());
                stockManagementDao.update(dayCloseEvent, true);
                return true;
            } else {
                throw new SumoException("DAY_CLOSE_ERROR", "Can not Find and Running Stock Take Sumo Day Close Event with Event Id : " + eventId);
            }
        } catch (SumoException e) {
            throw e;
        } catch (Exception e) {
            LOG.error("Exception while updateSumoDayCloseDeviceInfo :: ", e);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateStockTakeSumoDayCloseEvent(Integer eventId, String newStatus) throws SumoException {
        try {
            StockTakeSumoDayCloseEvent dayCloseEvent = stockManagementDao.find(StockTakeSumoDayCloseEvent.class, eventId);
            if (Objects.nonNull(dayCloseEvent)) {
                if (newStatus.equalsIgnoreCase(StockTakeSumoDayCloseStatus.CANCELLED.value())) {
                    dayCloseEvent.setEventStatus(newStatus);
                    dayCloseEvent.setLastUpdatedTime(AppUtils.getCurrentTimestamp());
                    stockManagementDao.update(dayCloseEvent, true);
                    return true;
                }
                SCMDayCloseEventData latestKettleDayClose = getLatestKettleDayClose(dayCloseEvent.getUnitId());
                if (Objects.isNull(latestKettleDayClose)) {
                    throw new SumoException("DAY_CLOSE_EXCEPTION", "No Kettle Day Close Found....!\nPlease Complete Kettle Day Close First...!");
                } else {
                    if (latestKettleDayClose.getStatus().equalsIgnoreCase(StockEventStatus.INITIATED.value())) {
                        StockTakeSumoDayCloseEvent lastSubmittedCompletedEvent = stockManagementDao.checkForStockTakeSumoDayClose(dayCloseEvent.getUnitId(),
                                Arrays.asList(StockTakeSumoDayCloseStatus.SUBMITTED.value(), StockTakeSumoDayCloseStatus.COMPLETED.value()), false, null);
                        if (Objects.nonNull(lastSubmittedCompletedEvent) && lastSubmittedCompletedEvent.getDayCloseEvent().getEventId().equals(latestKettleDayClose.getEventId())) {
                            StringBuilder msg = new StringBuilder("Event already ");
                            msg.append(lastSubmittedCompletedEvent.getEventStatus()).append(" by ").append(
                                            SCMUtil.getCreatedBy(masterDataCache.getEmployee(lastSubmittedCompletedEvent.getEventCreatedBy()), lastSubmittedCompletedEvent.getEventCreatedBy()))
                                    .append("\nCan not Create new Event..!");
                            throw new SumoException("DAY_CLOSE_EXCEPTION", msg.toString());
                        }
                        dayCloseEvent.setDayCloseEvent(latestKettleDayClose);
                    } else {
                        throw new SumoException("DAY_CLOSE_EXCEPTION", "No INITIATED Kettle Day Close Found....!\nPlease Complete Kettle Day Close First...!");
                    }
                }
                dayCloseEvent.setEventStatus(newStatus);
                dayCloseEvent.setLastUpdatedTime(AppUtils.getCurrentTimestamp());
                if (newStatus.equalsIgnoreCase(StockTakeSumoDayCloseStatus.SUBMITTED.value())) {
                    dayCloseEvent.setEventSubmittedAt(AppUtils.getCurrentTimestamp());
                }
                stockManagementDao.update(dayCloseEvent, true);
                return true;
            } else {
                throw new SumoException("DAY_CLOSE_ERROR", "Can not Find and Running Stock Take Sumo Day Close Event with Event Id : " + eventId);
            }
        } catch (SumoException e) {
            throw e;
        } catch (Exception e) {
            LOG.error("Exception while updateSumoDayCloseDeviceInfo :: ", e);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public synchronized boolean saveSubmitDayCloseProducts(List<StockTakeSumoDayCloseProductsDTO> productList, Integer eventId, boolean isSubmit, String deviceInfo) throws SumoException {
        try {
            LOG.info("Started Saving/Submit Of Day Close Products For EVent Id : {}", eventId);
            StockTakeSumoDayCloseEvent dayCloseEvent = stockManagementDao.find(StockTakeSumoDayCloseEvent.class, eventId);
            if (Objects.nonNull(dayCloseEvent) && dayCloseEvent.getEventStatus().equalsIgnoreCase(StockTakeSumoDayCloseStatus.IN_PROGRESS.value())) {
                // need to Add List
                if (!dayCloseEvent.getDeviceInfo().equalsIgnoreCase(deviceInfo)) {
                    throw new SumoException("DEVICE_INFO_MISMATCH", "Event is Current Running On Some Other Device :: " + dayCloseEvent.getDeviceInfo());
                }
                LOG.info("::: Started Segregating Products List ::: For Event id : {}", eventId);
                List<StockTakeSumoDayCloseProductsDTO> needToAddList = new ArrayList<>();
                List<StockTakeSumoDayCloseProductsDTO> needToUpdateList = new ArrayList<>();
                for (StockTakeSumoDayCloseProductsDTO product : productList) {
                    if (Objects.isNull(product.getStockTakeSumoDayCloseEventId())) {
                        needToAddList.add(product);
                    } else {
                        needToUpdateList.add(product);
                    }
                }
                LOG.info("::: Completed Segregating Products List ::: For Event id : {}", eventId);
                LOG.info("::: started Adding New Products List ::: For Event id : {}", eventId);
                //adding List
                for (StockTakeSumoDayCloseProductsDTO product : needToAddList) {
                    StockTakeSumoDayCloseProducts dayCloseProduct = new StockTakeSumoDayCloseProducts();
                    dayCloseProduct.setProductId(product.getProductId());
                    dayCloseProduct.setStockTakeSumoDayCloseEventId(dayCloseEvent);
                    dayCloseProduct.setUpdatedTime(product.getUpdatedTime());
                    dayCloseProduct = stockManagementDao.add(dayCloseProduct, true);
                    for (DayCloseProductPackagingMappingsDTO packagingMappingsDTO : product.getDayCloseProductPackagingMappings()) {
                        if (Objects.nonNull(packagingMappingsDTO.getQuantity())) {
                            DayCloseProductPackagingMappings productPackagingMapping = new DayCloseProductPackagingMappings();
                            productPackagingMapping.setPackagingId(packagingMappingsDTO.getPackagingId());
                            productPackagingMapping.setQuantity(packagingMappingsDTO.getQuantity());
                            productPackagingMapping.setSumoDayCloseProductItemId(dayCloseProduct);
                            stockManagementDao.add(productPackagingMapping, true);
                        }
                    }
                }
                LOG.info("Completed Updating New Products For Event id : {}", eventId);
                List<Integer> sumoDayCloseProductItemIds = needToUpdateList.stream().mapToInt(StockTakeSumoDayCloseProductsDTO::getSumoDayCloseProductItemId).boxed().collect(Collectors.toList());
                // getting list of StockTakeSumoDayCloseProducts
                List<StockTakeSumoDayCloseProducts> stockTakeSumoDayCloseProducts = stockManagementDao.getListOfStockTakeSumoDayCloseProducts(sumoDayCloseProductItemIds);
                Map<Integer, StockTakeSumoDayCloseProducts> mappingsByDayCloseProductItemId = stockTakeSumoDayCloseProducts.stream().collect(Collectors.toMap(StockTakeSumoDayCloseProducts::getSumoDayCloseProductItemId, Function.identity()));
//                    List<DayCloseProductPackagingMappings> productPackagingMappings = stockManagementDao.findAllProductPackagingMappingsByItemIds(sumoDayCloseProductItemIds);
                List<DayCloseProductPackagingMappings> productPackagingMappings = new ArrayList<>();
                for (StockTakeSumoDayCloseProducts dayCloseProduct : stockTakeSumoDayCloseProducts) {
                    productPackagingMappings.addAll(dayCloseProduct.getDayCloseProductPackagingMappings());
                }
                Map<Integer, DayCloseProductPackagingMappings> mappingsById = productPackagingMappings.stream().collect(Collectors.toMap(DayCloseProductPackagingMappings::getProductPackagingMappingId, Function.identity()));
                LOG.info("Completed getting Data From Db related to Products and Packaging's : for event id : {}", eventId);
                List<DayCloseProductPackagingMappings> updatedList = new ArrayList<>();
                LOG.info("Started Updating Data of Update List ::: for event id  : {}", eventId);
                for (StockTakeSumoDayCloseProductsDTO product : needToUpdateList) {
                    StockTakeSumoDayCloseProducts dayCloseProduct;
                    if (mappingsByDayCloseProductItemId.containsKey(product.getSumoDayCloseProductItemId())) {
                        dayCloseProduct = mappingsByDayCloseProductItemId.get(product.getSumoDayCloseProductItemId());
                    } else {
                        dayCloseProduct = stockManagementDao.find(StockTakeSumoDayCloseProducts.class, product.getSumoDayCloseProductItemId());
                    }

                    for (DayCloseProductPackagingMappingsDTO packagingMappingsDTO : product.getDayCloseProductPackagingMappings()) {
                        if (Objects.nonNull(packagingMappingsDTO.getProductPackagingMappingId())) {
                            DayCloseProductPackagingMappings mapping;
                            if (mappingsById.containsKey(packagingMappingsDTO.getProductPackagingMappingId())) {
                                mapping = mappingsById.get(packagingMappingsDTO.getProductPackagingMappingId());
                            } else {
                                mapping = stockManagementDao.find(DayCloseProductPackagingMappings.class, packagingMappingsDTO.getProductPackagingMappingId());
                            }
                            mapping.setQuantity(packagingMappingsDTO.getQuantity());
                            updatedList.add(mapping);
                        } else {
                            if (Objects.nonNull(packagingMappingsDTO.getQuantity())) {
                                DayCloseProductPackagingMappings productPackagingMapping = new DayCloseProductPackagingMappings();
                                productPackagingMapping.setPackagingId(packagingMappingsDTO.getPackagingId());
                                productPackagingMapping.setQuantity(packagingMappingsDTO.getQuantity());
                                productPackagingMapping.setSumoDayCloseProductItemId(dayCloseProduct);
                                stockManagementDao.add(productPackagingMapping, true);
                            }
                        }
                    }
                }
                LOG.info("Completed Updating Data of Update List ::: for event id  : {}", eventId);
                if (!updatedList.isEmpty()) {
                    stockManagementDao.update(updatedList, true);
                }
            } else {
                throw new SumoException("DAY_CLOSE_ERROR", "Can not Find and Running Stock Take Sumo Day Close Event with Event Id : " + eventId);
            }
        } catch (SumoException e) {
            throw e;
        } catch (Exception e) {
            LOG.error("Exception while updateSumoDayCloseDeviceInfo :: ", e);
            return false;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void settleStockTakeSumoDayCloseEvent(Integer stockTakeSumoDayCloseEventId, SCMDayCloseEventData stockEvent) {
        try {
            StockTakeSumoDayCloseEvent dayCloseEvent = stockManagementDao.find(StockTakeSumoDayCloseEvent.class, stockTakeSumoDayCloseEventId);
            if (Objects.nonNull(dayCloseEvent)) {
                dayCloseEvent.setEventStatus(StockTakeSumoDayCloseStatus.COMPLETED.value());
                dayCloseEvent.setSumoDayCloseEvent(stockEvent);
                dayCloseEvent.setLastUpdatedTime(AppUtils.getCurrentTimestamp());
                stockManagementDao.update(dayCloseEvent, true);
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While updating the Status Of Stock Take Sumo Day Close Event Id : {} ::",stockTakeSumoDayCloseEventId, e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void cancelPendingStockTakeEventsOfApp() {
        try {
            LOG.info("::: Cancelling Pending Stock take Events :::");
            List<StockTakeSumoDayCloseEvent> pendingStockTakeEvents = stockManagementDao.getPendingStockTakeEventsOfApp();
            if (!pendingStockTakeEvents.isEmpty()) {
                for (StockTakeSumoDayCloseEvent pendingStockTakeEvent : pendingStockTakeEvents) {
                    if (pendingStockTakeEvent.getEventStatus().equalsIgnoreCase(StockTakeSumoDayCloseStatus.IN_PROGRESS.value())) {
                        pendingStockTakeEvent.setEventStatus(StockTakeSumoDayCloseStatus.AUTO_CLOSED_IN_PROGRESS.value());
                    } else {
                        pendingStockTakeEvent.setEventStatus(StockTakeSumoDayCloseStatus.AUTO_CLOSED_SUBMITTED.value());
                    }
                    pendingStockTakeEvent.setLastUpdatedTime(AppUtils.getCurrentTimestamp());
                    stockManagementDao.update(pendingStockTakeEvent, true);
                }
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While cancelPendingStockTakeEventsOfApp ::", e);
        }
    }
}



