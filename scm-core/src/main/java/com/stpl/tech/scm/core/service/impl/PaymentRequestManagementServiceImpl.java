package com.stpl.tech.scm.core.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.itextpdf.text.pdf.PdfWriter;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.scm.core.PaymentBanks;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.TransferOrderCreationException;
import com.stpl.tech.scm.core.mapper.DomainDataMapper;
import com.stpl.tech.scm.core.mapper.PrMetaDataMapper;
import com.stpl.tech.scm.core.mapper.VendorAdvancePaymentMapper;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.GoodsReceiveManagementService;
import com.stpl.tech.scm.core.service.PaymentRequestManagementService;
import com.stpl.tech.scm.core.service.PurchaseOrderManagementService;
import com.stpl.tech.scm.core.service.RequestOrderManagementService;
import com.stpl.tech.scm.core.service.SCMMetadataService;
import com.stpl.tech.scm.core.service.ServiceOrderManagementService;
import com.stpl.tech.scm.core.service.ServiceReceiveManagementService;
import com.stpl.tech.scm.core.service.decorator.LdcVendorServiceDecorator;
import com.stpl.tech.scm.core.util.SCMConstant;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.ValidationUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.GoodsReceiveManagementDao;
import com.stpl.tech.scm.data.dao.LdcVendorDao;
import com.stpl.tech.scm.data.dao.PaymentRequestManagementDao;
import com.stpl.tech.scm.data.dao.SCMMetadataDao;
import com.stpl.tech.scm.data.dao.ServiceOrderManagementDao;
import com.stpl.tech.scm.data.dao.ServiceReceiveManagementDao;
import com.stpl.tech.scm.data.model.AdvancePaymentAuditLogData;
import com.stpl.tech.scm.data.model.AdvancePaymentData;
import com.stpl.tech.scm.data.model.AdvancePaymentStatusLog;
import com.stpl.tech.scm.data.model.BudgetAuditDetailData;
import com.stpl.tech.scm.data.model.BusinessCostCenterData;
import com.stpl.tech.scm.data.model.CapexAuditDetailData;
import com.stpl.tech.scm.data.model.CapexBudgetDetailData;
import com.stpl.tech.scm.data.model.CategorySubCategoryDebitNoteData;
import com.stpl.tech.scm.data.model.CompanyBankMapping;
import com.stpl.tech.scm.data.model.CostCenterData;
import com.stpl.tech.scm.data.model.CostElementData;
import com.stpl.tech.scm.data.model.DebitNoteDetailData;
import com.stpl.tech.scm.data.model.DocumentDetailData;
import com.stpl.tech.scm.data.model.GoodsRecievedToPaymentRequestMapping;
import com.stpl.tech.scm.data.model.GstOfStpl;
import com.stpl.tech.scm.data.model.GstStateMetaData;
import com.stpl.tech.scm.data.model.HolidaysListData;
import com.stpl.tech.scm.data.model.InvoiceDeviationMappingData;
import com.stpl.tech.scm.data.model.InvoiceExcessQuantity;
import com.stpl.tech.scm.data.model.LinkedPaymentsForAdvance;
import com.stpl.tech.scm.data.model.PRPaymentDetailData;
import com.stpl.tech.scm.data.model.PaymentCalendarData;
import com.stpl.tech.scm.data.model.PaymentDeviationData;
import com.stpl.tech.scm.data.model.PaymentInvoiceData;
import com.stpl.tech.scm.data.model.PaymentInvoiceItemData;
import com.stpl.tech.scm.data.model.PaymentInvoiceItemTaxData;
import com.stpl.tech.scm.data.model.PaymentRequestData;
import com.stpl.tech.scm.data.model.PaymentRequestItemMappingData;
import com.stpl.tech.scm.data.model.PaymentRequestLogData;
import com.stpl.tech.scm.data.model.PaymentRequestMetaData;
import com.stpl.tech.scm.data.model.PaymentRequestQueryData;
import com.stpl.tech.scm.data.model.PaymentRequestStatusLogData;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.PurchaseOrderVendorGRMappingData;
import com.stpl.tech.scm.data.model.ServiceOrderData;
import com.stpl.tech.scm.data.model.ServiceOrderItemData;
import com.stpl.tech.scm.data.model.ServiceOrderServiceReceiveMappingData;
import com.stpl.tech.scm.data.model.ServiceReceivedData;
import com.stpl.tech.scm.data.model.ServiceReceivedItemData;
import com.stpl.tech.scm.data.model.SpecializedOrderInvoiceData;
import com.stpl.tech.scm.data.model.TdsLedgerRate;
import com.stpl.tech.scm.data.model.VendorCompanyDebitMapping;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedData;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedItemData;
import com.stpl.tech.scm.domain.model.AdvancePaymentStatus;
import com.stpl.tech.scm.domain.model.BankVO;
import com.stpl.tech.scm.domain.model.BudgetAuditActions;
import com.stpl.tech.scm.domain.model.BusinessCostCenter;
import com.stpl.tech.scm.domain.model.CategorySubCategoryDebitNote;
import com.stpl.tech.scm.domain.model.DebitBalanceVO;
import com.stpl.tech.scm.domain.model.DebitNoteDetail;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.GRProcessingStatus;
import com.stpl.tech.scm.domain.model.HolidayActivateDeactivate;
import com.stpl.tech.scm.domain.model.HolidayDetails;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.InvoiceDeviationMapping;
import com.stpl.tech.scm.domain.model.InvoiceDocType;
import com.stpl.tech.scm.domain.model.LdcVendorDomain;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.PRPaymentDetail;
import com.stpl.tech.scm.domain.model.PaymentCalendar;
import com.stpl.tech.scm.domain.model.PaymentDeviationLevel;
import com.stpl.tech.scm.domain.model.PaymentDeviationType;
import com.stpl.tech.scm.domain.model.PaymentInvoice;
import com.stpl.tech.scm.domain.model.PaymentInvoiceItem;
import com.stpl.tech.scm.domain.model.PaymentInvoiceItemTax;
import com.stpl.tech.scm.domain.model.PaymentRequest;
import com.stpl.tech.scm.domain.model.PaymentRequestItemMapping;
import com.stpl.tech.scm.domain.model.PaymentRequestMetaDataDomain;
import com.stpl.tech.scm.domain.model.PaymentRequestQuery;
import com.stpl.tech.scm.domain.model.PaymentRequestStatus;
import com.stpl.tech.scm.domain.model.PaymentRequestStatusChangeVO;
import com.stpl.tech.scm.domain.model.PaymentRequestType;
import com.stpl.tech.scm.domain.model.PaymentSheetReturnVO;
import com.stpl.tech.scm.domain.model.PaymentSheetVO;
import com.stpl.tech.scm.domain.model.PaymentType;
import com.stpl.tech.scm.domain.model.PrProcessMetaData;
import com.stpl.tech.scm.domain.model.PurchaseOrderStatus;
import com.stpl.tech.scm.domain.model.RejectedPayments;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.ServiceOrderStatus;
import com.stpl.tech.scm.domain.model.VendorAdvancePayment;
import com.stpl.tech.scm.domain.model.VendorAdvancePaymentAuditLog;
import com.stpl.tech.scm.domain.model.VendorAdvancePaymentStatusLog;
import com.stpl.tech.scm.domain.model.VendorDebitBalanceVO;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorEditVO;
import com.stpl.tech.scm.domain.model.VendorGR;
import com.stpl.tech.scm.domain.model.VendorGRPaymentStatus;
import com.stpl.tech.scm.domain.model.VendorPRVO;
import com.stpl.tech.scm.notification.email.PrQueryEmailNotification;
import com.stpl.tech.scm.notification.email.VendorAdvancesEmailNotification;
import com.stpl.tech.scm.notification.email.VendorInvoiceRequestEmailNotification;
import com.stpl.tech.scm.notification.email.VendorPRRejectEmailNotification;
import com.stpl.tech.scm.notification.email.VendorPaymentEmailNotification;
import com.stpl.tech.scm.notification.email.template.PRQueryEmailNotificationTemplate;
import com.stpl.tech.scm.notification.email.template.VendorAdvancesEmailNotificationTemplate;
import com.stpl.tech.scm.notification.email.template.VendorInvoiceRequestEmailNotificationTemplate;
import com.stpl.tech.scm.notification.email.template.VendorPRRejectEmailNotificationTemplate;
import com.stpl.tech.scm.notification.email.template.VendorPaymentEmailNotificationTemplate;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.notification.AttachmentData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by Rahul Singh on 22-06-2016.
 */
@Service
public class PaymentRequestManagementServiceImpl implements PaymentRequestManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(PaymentRequestManagementServiceImpl.class);

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private PaymentRequestManagementDao paymentRequestManagementDao;

    @Autowired
    private ServiceReceiveManagementDao serviceReceiveManagementDao;

    @Autowired
    private ServiceOrderManagementDao serviceOrderManagementDao;

    @Autowired
    private GoodsReceiveManagementDao goodsReceiveManagementDao;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private EnvProperties props;

    @Autowired
    private PurchaseOrderManagementService purchaseOrderService;

    @Autowired
    private GoodsReceiveManagementService goodsReceiveManagementService;

    @Autowired
    private SCMMetadataDao scmMetadataDao;

    @Autowired
    private SCMMetadataService scmMetadataService;

    @Autowired
    private ServiceOrderManagementService serviceOrderManagementService;

    @Autowired
    private ServiceReceiveManagementService serviceReceiveManagementService;

    @Autowired
    private RequestOrderManagementService requestOrderManagementService;

    @Autowired
    private LdcVendorServiceDecorator ldcVendorServiceDecorator;

    @Autowired
    private LdcVendorDao ldcVendorDao;

    private final WorkbookContextFactory ctxFactory = WorkbookContextFactory.useXlsx();

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public PaymentRequest createPaymentRequest(PaymentRequest paymentRequest, Integer milkInvoiceId) throws SumoException {
        if (paymentRequest.getVendorId() == null) {
            throw new SumoException("Please provide vendor detail.");
        }
        if (paymentRequest.getType().value().equalsIgnoreCase(PaymentRequestType.SERVICE_RECEIVED.value())) {
            Integer prId = validateSrInvoiceNumber(paymentRequest);
            if (prId > 0) {
                throw new SumoException(String.format(
                        "Cannot Create Payment Request as a PR with same Invoice Number Already exist in the system with the PR Id [%s] for the vendor : %s and Invoice number : %s",
                        prId, scmCache.getVendorDetail(paymentRequest.getVendorId().getId()).getEntityName(), paymentRequest.getInvoiceNumber()));
            }
        }
        VendorDetail vendorDetail = scmCache.getVendorDetail(paymentRequest.getVendorId().getId());
        if (Objects.nonNull(vendorDetail) && Objects.nonNull(vendorDetail.getVendorBlocked()) && vendorDetail.getVendorBlocked().equals(AppConstants.YES)) {
            if (Objects.nonNull(vendorDetail.getBlockedReason()) && vendorDetail.getBlockedReason().equalsIgnoreCase("MANUAL")) {
                throw new SumoException("Vendor is Blocked...!", "This vendor is blocked for making further PO/GR/SO/SR/PR...!" +
                        "<br><b>Please Contact Finance Team..!</b>");
            }
        }
        Integer companyId = null;
        for (PaymentRequestItemMapping paymentRequestItemMapping : paymentRequest.getRequestItemMappings()) {
            if (PaymentRequestType.GOODS_RECEIVED.equals(paymentRequestItemMapping.getPaymentRequestType())) {
                VendorGoodsReceivedData vendorGoodsReceivedData = paymentRequestManagementDao
                        .find(VendorGoodsReceivedData.class, paymentRequestItemMapping.getPaymentRequestItemId());
                if (vendorGoodsReceivedData == null) {
                    throw new SumoException(
                            "Payment GR id" + paymentRequestItemMapping.getPaymentRequestItemId() + " does not exist!");
                } else if (vendorGoodsReceivedData.getPaymentRequestData() != null) {
                    throw new SumoException("Payment for GR id " + paymentRequestItemMapping.getPaymentRequestItemId()
                            + " has been generated already!");
                } else if (vendorGoodsReceivedData.getUpdatedAt().before(props.getPaymentRequestLaunchDate())) {
                    throw new SumoException("GR id " + paymentRequestItemMapping.getPaymentRequestItemId()
                            + " has been generated before 1 Nov. 2017 hence cannot be paid!");
                } else {
                    companyId = vendorGoodsReceivedData.getCompanyId();
                }
            }
        }
        Date currentTime = SCMUtil.getCurrentTimestamp();
        PaymentRequestData paymentRequestData = new PaymentRequestData();
        paymentRequestData.setIsSoContractBreach(paymentRequest.getIsSoContractBreach());
        paymentRequestData.setSoContractBreachApprovalDoc(paymentRequest.getSoContractBreachApprovalDoc());
        paymentRequestData.setAmountsMatch(SCMUtil.setStatus(paymentRequest.isAmountsMatch()));
        paymentRequestData.setCreatedBy(paymentRequest.getCreatedBy().getId());
        paymentRequestData.setCreationTime(currentTime);
        if (Objects.nonNull(paymentRequest.getPaymentCard())) {
            paymentRequestData.setPaymentCard(paymentRequest.getPaymentCard());
        }
        if (Objects.nonNull(paymentRequest.getCardPaymentTransactionNumber())) {
            paymentRequestData.setCardPaymentTransactionNumber(paymentRequest.getCardPaymentTransactionNumber());
        }
        if (Objects.nonNull(paymentRequest.getCardPaymentProof())) {
            paymentRequestData.setCardPaymentProof(paymentRequest.getCardPaymentProof());
        }
        if (Objects.nonNull(paymentRequest.getCardPaymentComment())) {
            paymentRequestData.setCardPaymentComment(paymentRequest.getCardPaymentComment());
        }
        if (Objects.nonNull(paymentRequest.getGrDocType())) {
            if (InvoiceDocType.INVOICE.equals(paymentRequest.getGrDocType())) {
                paymentRequestData.setCurrentStatus(PaymentRequestStatus.CREATED.value());
                paymentRequestData.setInvoiceNumber(paymentRequest.getPaymentInvoice().getInvoiceNumber());
                if (Objects.nonNull(paymentRequest.getAdvancePayment())  && Objects.nonNull(paymentRequest.getAdvancePayment().getUsedAmount())) {
                    validateSrsForpayment(paymentRequest);
                    List<AdvancePaymentData> advancePaymentDataList = paymentRequestManagementDao.getAdvancePaymentByAdvanceIds(paymentRequest.getAdvancePaymentIds());
                    //validating with data got from UI
                    validateAvailableAmount(advancePaymentDataList, paymentRequest.getAdvancePayment().getAvailableAmount());
                    //validating with the Available Advances in DB
                    AdvancePaymentData advancePaymentData = paymentRequestManagementDao.find(AdvancePaymentData.class, paymentRequest.getAdvancePaymentIds().get(0));
                    if (Objects.nonNull(advancePaymentData.getPurchaseOrderData())) {
                        validateAvailableAmount(advancePaymentData.getPurchaseOrderData().getAdvancePaymentDatas(), paymentRequest.getAdvancePayment().getAvailableAmount());
                    } else {
                        validateAvailableAmount(advancePaymentData.getServiceOrderData().getAdvancePaymentDatas(), paymentRequest.getAdvancePayment().getAvailableAmount());
                    }
                    if(!Objects.equals(paymentRequest.getAdvancePayment().getUsedAmount(),BigDecimal.ZERO)){
                        paymentRequestData.setCurrentStatus(PaymentRequestStatus.PENDING_HOD_APPROVAL.value());
                    }
                    createLinkedAdvanceOrRefund(paymentRequest, paymentRequestData, true);
                    if (Objects.nonNull(paymentRequest.getAdvancePayment().getPoSoClosed()) && paymentRequest.getAdvancePayment().getPoSoClosed().equalsIgnoreCase(SCMServiceConstants.NO)) {
                        paymentRequestData.setCurrentStatus(PaymentRequestStatus.PENDING_HOD_APPROVAL.value());
                    }
                }
                if (!paymentRequest.isForceCreate() && paymentRequest.getPaymentInvoice().getInvoiceDate()
                        .compareTo(props.getFinancialCalendarStart()) < 0) {
                    throw new SumoException("Invoice date does not belong to this financial year.");
                }
            }
            if (InvoiceDocType.DELIVERY_CHALLAN.equals(paymentRequest.getGrDocType())) {
                paymentRequestData.setCurrentStatus(PaymentRequestStatus.INITIATED.value());
                if (Objects.nonNull(paymentRequest.getAdvancePayment())) {
                    List<AdvancePaymentData> advancePaymentDataList = paymentRequestManagementDao.getAdvancePaymentByAdvanceIds(paymentRequest.getAdvancePaymentIds());
                    validateAvailableAmount(advancePaymentDataList, paymentRequest.getAdvancePayment().getAvailableAmount());
                    createLinkedAdvanceOrRefund(paymentRequest, paymentRequestData, false);
                }
            }
        } else {
            paymentRequestData.setCurrentStatus(PaymentRequestStatus.CREATED.value());
        }
        paymentRequestData.setDeviationCount(paymentRequest.getDeviationCount());
        if (Objects.nonNull(paymentRequest.getGrDocType())) {
            paymentRequestData.setGrDocType(paymentRequest.getGrDocType().value());
        }
        VendorDetailData vendorDetailData = paymentRequestManagementDao.find(VendorDetailData.class,
                paymentRequest.getVendorId().getId());
        paymentRequestData.setLastUpdated(currentTime);
        paymentRequestData.setPaidAmount(paymentRequest.getPaidAmount());
        paymentRequestData.setDuplicatePaidAmount(paymentRequest.getDuplicatePaidAmount());
        paymentRequestData.setProposedAmount(paymentRequest.getProposedAmount());
        paymentRequestData.setRemarks(paymentRequest.getRemarks());
        if (companyId != null) {
            paymentRequestData.setCompanyId(companyId);
            if (paymentRequest.getRequestingUnit() != null) {
                paymentRequestData.setRequestingUnit(paymentRequest.getRequestingUnit().getId());
            }
        } else {
            if (paymentRequest.getRequestingUnit() != null) {
                paymentRequestData.setRequestingUnit(paymentRequest.getRequestingUnit().getId());
                paymentRequestData
                        .setCompanyId(scmCache.getUnitDetail(paymentRequestData.getRequestingUnit()).getCompanyId());
            } else {
                paymentRequestData.setCompanyId(paymentRequest.getCompanyId());
            }
        }
        //setting payment date from payment invoice date
        if ((Objects.nonNull(paymentRequest.getGrDocType()) && InvoiceDocType.INVOICE.equals(paymentRequest.getGrDocType())) || paymentRequest.getType().value().equalsIgnoreCase(AppConstants.ADVANCE_PAYMENT)) {
            /*Integer creditDays = 0;
            if (vendorDetailData != null && vendorDetailData.getCompanyDetails() != null && vendorDetailData.getCompanyDetails().getCreditDays() != null) {
                creditDays = vendorDetailData.getCompanyDetails().getCreditDays();
            }
            Date paymentDate = SCMUtil.addDays(paymentRequest.getPaymentInvoice().getInvoiceDate(), creditDays);
            if (creditDays == 0) {
                paymentDate = paymentRequest.getPaymentInvoice().getInvoiceDate();
            }*/
            paymentRequestData.setVendorPaymentDate(getPaymentDate(paymentRequest, vendorDetailData));
            Date dateAfterRemoving2Days = getDateAfterRemovingThreshold(paymentRequestData.getVendorPaymentDate());
            paymentRequestData.setPaymentDate(getWorkingDay(dateAfterRemoving2Days));
        }
        paymentRequestData.setType(paymentRequest.getType().value());
        paymentRequestData.setVendorId(paymentRequest.getVendorId().getId());
        if (paymentRequest.getState() != null) {
            paymentRequestData.setStateCode(paymentRequest.getState().getCode());
            paymentRequestData.setStateName(paymentRequest.getState().getName());
        }
        paymentRequestData.setMandatoryRequiredDocument(paymentRequest.getMandatoryRequiredDocument());
        paymentRequestData = paymentRequestManagementDao.add(paymentRequestData, true);
        if ((paymentRequestData.getCurrentStatus().equalsIgnoreCase(PaymentRequestStatus.PENDING_HOD_APPROVAL.value()) || paymentRequestData.getCurrentStatus().equalsIgnoreCase(PaymentRequestStatus.INITIATED.value()))
                && Objects.nonNull(paymentRequest.getAdvancePayment()) && Objects.nonNull(paymentRequest.getAdvancePayment().getUsedAmount()) && paymentRequest.getAdvancePayment().getUsedAmount().compareTo(BigDecimal.ZERO) > 0) {
            // re calculate PR
            BigDecimal remainingAmount = paymentRequest.getAdvancePayment().getUsedAmount();
            List<AdvancePaymentData> advancePaymentDataList = paymentRequestManagementDao.getAdvancePaymentByAdvanceIds(paymentRequest.getAdvancePaymentIds());
            calculateAdvance(advancePaymentDataList,paymentRequestData,remainingAmount,paymentRequest,true);
        }
        if (paymentRequestData != null) {
            if (Objects.nonNull(paymentRequest.getType()) && paymentRequest.getType().equals(PaymentRequestType.MILK_BAKERY)) {
                SpecializedOrderInvoiceData invoiceData = paymentRequestManagementDao.find(SpecializedOrderInvoiceData.class, milkInvoiceId);
                if (invoiceData.getIsPrRaised().equalsIgnoreCase(AppUtils.setStatus(true))) {
                    throw new SumoException("Pr Already Raised For This Invoice!!", "PLease Refresh Once");
                }
                invoiceData.setIsPrRaised(AppUtils.setStatus(true));
                invoiceData.setPrId(paymentRequestData.getId());
                paymentRequestManagementDao.update(invoiceData, true);
            }
            PaymentInvoiceData paymentInvoiceData = new PaymentInvoiceData();
            PaymentInvoice paymentInvoice = paymentRequest.getPaymentInvoice();
            paymentInvoiceData.setCalculatedInvoiceAmount(paymentInvoice.getCalculatedInvoiceAmount());
            paymentInvoiceData.setInvoiceAmount(paymentInvoice.getInvoiceAmount());
            if (Objects.nonNull(paymentRequest.getGrDocType()) && InvoiceDocType.INVOICE.equals(paymentRequest.getGrDocType())) {
                paymentInvoiceData.setInvoiceDocumentHandle(paymentInvoice.getInvoiceDocumentHandle());
                paymentInvoiceData.setInvoiceNumber(paymentInvoice.getInvoiceNumber());
                paymentInvoiceData.setInvoiceDate(paymentInvoice.getInvoiceDate());
            }
            if (paymentRequest.getType().value().equalsIgnoreCase(AppConstants.ADVANCE_PAYMENT)) {
                paymentInvoiceData.setInvoiceDate(paymentInvoice.getInvoiceDate());
                paymentInvoiceData.setInvoiceDocumentHandle(paymentInvoice.getInvoiceDocumentHandle());
            }
            paymentInvoiceData.setPaymentAmount(paymentInvoice.getPaymentAmount());
            paymentInvoiceData.setPaymentRequestData(paymentRequestData);
            paymentInvoiceData.setExtraCharges(
                    paymentInvoice.getExtraCharges() == null ? BigDecimal.ZERO : paymentInvoice.getExtraCharges());
            paymentInvoiceData = paymentRequestManagementDao.add(paymentInvoiceData, false);
            if (paymentInvoiceData != null) {
                for (InvoiceDeviationMapping invoiceDeviationMapping : paymentInvoice.getDeviations()) {
                    invoiceDeviationMapping.setDeviationItemType(PaymentDeviationLevel.INVOICE);
                    invoiceDeviationMapping.setDeviationItemId(paymentInvoiceData.getId());
                    addDeviation(invoiceDeviationMapping);
                }
                for (PaymentInvoiceItem paymentInvoiceItem : paymentInvoice.getPaymentInvoiceItems()) {
                    PaymentInvoiceItemData paymentInvoiceItemData = new PaymentInvoiceItemData();
                    paymentInvoiceItemData.setConversionRatio(paymentInvoiceItem.getConversionRatio());
                    paymentInvoiceItemData.setHsn(paymentInvoiceItem.getHsn());
                    if (paymentInvoiceItem.getSkuDate() != null) {
                        paymentInvoiceItemData.setSkuDate(paymentInvoiceItem.getSkuDate());
                    }
                    if (paymentInvoiceItem.getToSkuDate() != null) {
                        paymentInvoiceItemData.setToSkuDate(paymentInvoiceItem.getToSkuDate());
                    }
                    paymentInvoiceItemData.setPackagingId(paymentInvoiceItem.getPackagingId());
                    paymentInvoiceItemData.setPackagingName(paymentInvoiceItem.getPackagingName());
                    paymentInvoiceItemData.setPaymentInvoiceData(paymentInvoiceData);
                    paymentInvoiceItemData.setQuantity(paymentInvoiceItem.getQuantity());
                    paymentInvoiceItemData.setSkuId(paymentInvoiceItem.getSkuId());
                    paymentInvoiceItemData.setSkuName(paymentInvoiceItem.getSkuName());
                    paymentInvoiceItemData.setTotalAmount(paymentInvoiceItem.getTotalAmount());
                    paymentInvoiceItemData.setTotalPrice(paymentInvoiceItem.getTotalPrice());
                    paymentInvoiceItemData.setTotalTax(paymentInvoiceItem.getTotalTax());
                    paymentInvoiceItemData.setUnitPrice(paymentInvoiceItem.getUnitPrice());
                    paymentInvoiceItemData.setUom(paymentInvoiceItem.getUom());
                    paymentInvoiceItemData.setServiceReceivedId(paymentInvoiceItem.getServiceReceivedId());
                    paymentInvoiceItemData.setServiceReceivedItemId(paymentInvoiceItem.getServiceReceivedItemId());
                    paymentInvoiceItemData.setPackagingPrice(paymentInvoiceItem.getPackagingPrice());
                    paymentInvoiceItemData = paymentRequestManagementDao.add(paymentInvoiceItemData, false);
                    if (paymentInvoiceItemData != null) {
                        List<PaymentInvoiceItemTaxData> taxes = new ArrayList<>();
                        for (PaymentInvoiceItemTax paymentInvoiceItemTax : paymentInvoiceItem.getTaxes()) {
                            PaymentInvoiceItemTaxData paymentInvoiceItemTaxData = new PaymentInvoiceItemTaxData();
                            paymentInvoiceItemTaxData.setPaymentInvoiceItemData(paymentInvoiceItemData);
                            paymentInvoiceItemTaxData.setTaxPercentage(paymentInvoiceItemTax.getTaxPercentage());
                            paymentInvoiceItemTaxData.setTaxType(paymentInvoiceItemTax.getTaxType());
                            paymentInvoiceItemTaxData.setTaxValue(paymentInvoiceItemTax.getTaxValue());
                            paymentInvoiceItemTaxData = paymentRequestManagementDao.add(paymentInvoiceItemTaxData,
                                    false);
                            if (paymentInvoiceItemTaxData == null) {
                                throw new SumoException("Error creating payment request.");
                            }
                            taxes.add(paymentInvoiceItemTaxData);
                        }
                        paymentInvoiceItemData.setTaxes(taxes);
                        for (InvoiceDeviationMapping invoiceDeviationMapping : paymentInvoiceItem.getDeviations()) {
                            invoiceDeviationMapping.setDeviationItemType(PaymentDeviationLevel.INVOICE_ITEM);
                            invoiceDeviationMapping.setDeviationItemId(paymentInvoiceItemData.getId());
                            addDeviation(invoiceDeviationMapping);
                        }
                    }
                }
                for (PaymentRequestItemMapping paymentRequestItemMapping : paymentRequest.getRequestItemMappings()) {
                    paymentRequestItemMapping.setPaymentRequestId(paymentRequestData.getId());
                    if (PaymentRequestType.GOODS_RECEIVED.equals(paymentRequestItemMapping.getPaymentRequestType())) {
                        VendorGoodsReceivedData vendorGoodsReceivedData = paymentRequestManagementDao.find(
                                VendorGoodsReceivedData.class, paymentRequestItemMapping.getPaymentRequestItemId());
                        if (vendorGoodsReceivedData != null) {
                            vendorGoodsReceivedData.setPaymentRequestData(paymentRequestData);
//                            vendorGoodsReceivedData.getPaymentRequestData().getId();
                            saveGrTopaymentmapping(vendorGoodsReceivedData, PaymentRequestStatus.CREATED);
                            if (vendorGoodsReceivedData.getPaymentStatus() == null) {
                                vendorGoodsReceivedData.setPaymentStatus(VendorGRPaymentStatus.FRESH.value());
                            }
                            paymentRequestManagementDao.update(vendorGoodsReceivedData, false);

                            List<PaymentRequestItemMappingData> mappings = paymentRequestManagementDao
                                    .findMappedItemsByGRId(vendorGoodsReceivedData.getGoodsReceivedId(),
                                            PaymentRequestType.GOODS_RECEIVED);
                            for (PaymentRequestItemMappingData data : mappings) {
                                if (data.getLinkedPaymentRequestId() == null) {
                                    data.setLinkedPaymentRequestId(paymentRequestData.getId());
                                    paymentRequestManagementDao.update(data, false);
                                }
                            }
                        } else {
                            throw new SumoException("Mapped GR id is not valid");
                        }
                    }
                    if (PaymentRequestType.SERVICE_RECEIVED.equals(paymentRequestItemMapping.getPaymentRequestType())) {
                        ServiceReceivedData serviceReceivedData = paymentRequestManagementDao
                                .find(ServiceReceivedData.class, paymentRequestItemMapping.getPaymentRequestItemId());
                        if (serviceReceivedData != null) {
                            updateInvoiceQunatity(paymentInvoice.getPaymentInvoiceItems(), serviceReceivedData.getServiceItemList());
                            serviceReceivedData.setPaymentRequestData(paymentRequestData);
                            if (serviceReceivedData.getPaymentStatus() == null) {
                                serviceReceivedData.setPaymentStatus(VendorGRPaymentStatus.FRESH.value());
                            }
                            serviceReceivedData.setUpdatedBy(paymentRequest.getCreatedBy().getId());
                            serviceReceivedData.setUpdatedAt(currentTime);
                            paymentRequestManagementDao.update(serviceReceivedData, false);
                            List<PaymentRequestItemMappingData> mappings = paymentRequestManagementDao
                                    .findMappedItemsByGRId(serviceReceivedData.getServiceReceivedId(),
                                            PaymentRequestType.SERVICE_RECEIVED);
                            for (PaymentRequestItemMappingData data : mappings) {
                                if (data.getLinkedPaymentRequestId() == null) {
                                    data.setLinkedPaymentRequestId(paymentRequestData.getId());
                                    paymentRequestManagementDao.update(data, false);
                                }
                            }
                        } else {
                            throw new SumoException("Mapped SR id is not valid");
                        }
                    }
                    PaymentRequestItemMappingData paymentRequestItemMappingData = new PaymentRequestItemMappingData();
                    paymentRequestItemMappingData.setPaymentRequestId(paymentRequestItemMapping.getPaymentRequestId());
                    paymentRequestItemMappingData
                            .setPaymentRequestItemId(paymentRequestItemMapping.getPaymentRequestItemId());
                    paymentRequestItemMappingData
                            .setPaymentRequestType(paymentRequestItemMapping.getPaymentRequestType().value());
                    paymentRequestItemMappingData = paymentRequestManagementDao.add(paymentRequestItemMappingData,
                            false);
                    if (paymentRequestItemMappingData == null) {
                        throw new SumoException("Error adding request item mapping while creating payment request.");
                    }
                }
                paymentRequestManagementDao.flush();
                paymentRequest.setPaymentRequestId(paymentRequestData.getId());
                String logData = "Payment request id: " + paymentRequestData.getId() + " created by: "
                        + masterDataCache.getEmployee(paymentRequest.getCreatedBy().getId()) + "["
                        + paymentRequest.getCreatedBy().getId() + "].";
                logPaymentRequestData(logData, paymentRequestData.getId());
                if (InvoiceDocType.DELIVERY_CHALLAN.equals(paymentRequest.getGrDocType())) {
                    // sending mail to vendor
                    sendVendorInvoiceRequestNotification(vendorDetailData, paymentRequestData, paymentRequest);
                }
            } else {
                throw new SumoException("Error creating payment request.");
            }
        }
        return paymentRequest;
    }

    private void calculateAdvance(List<AdvancePaymentData> advancePaymentDataList, PaymentRequestData paymentRequestData, BigDecimal remainingAmount, PaymentRequest paymentRequest, Boolean isAddLog) throws SumoException {
        for (AdvancePaymentData advancePaymentData : advancePaymentDataList) {
            if (advancePaymentData.getAvailableAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (remainingAmount.compareTo(advancePaymentData.getAvailableAmount()) >= 0) {
                    remainingAmount = remainingAmount.subtract(advancePaymentData.getAvailableAmount());
                    logAdvancePaymentAudit(advancePaymentData.getAvailableAmount(), paymentRequestData, advancePaymentData, paymentRequest.getCreatedBy().getId(), AdvancePaymentStatus.BLOCKED.value());
                    paymentRequestData.setAdvanceAmount(paymentRequest.getAdvancePayment().getUsedAmount());
                    paymentRequestData.setAdvancePaymentData(advancePaymentData);
                    paymentRequestData = paymentRequestManagementDao.add(paymentRequestData, true);
                    LinkedPaymentsForAdvance linkedPaymentsForAdvance = new LinkedPaymentsForAdvance();
                    linkedPaymentsForAdvance.setAdvancePaymentData(advancePaymentData);
                    linkedPaymentsForAdvance.setPaymentRequestData(paymentRequestData);
                    paymentRequestManagementDao.add(linkedPaymentsForAdvance, true);
                } else {
                    LinkedPaymentsForAdvance linkedPaymentsForAdvance = new LinkedPaymentsForAdvance();
                    linkedPaymentsForAdvance.setAdvancePaymentData(advancePaymentData);
                    linkedPaymentsForAdvance.setPaymentRequestData(paymentRequestData);
                    paymentRequestManagementDao.add(linkedPaymentsForAdvance, true);
                    boolean shouldBreak = true;
                    if (Objects.nonNull(advancePaymentData.getRefundDate()) || Objects.nonNull(advancePaymentData.getAdjustedPoSo())) {
                        if (Objects.nonNull(advancePaymentData.getRefundDate())) {
                            if (remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
                                logAdvancePaymentAudit(remainingAmount, paymentRequestData, advancePaymentData, paymentRequest.getCreatedBy().getId(), AdvancePaymentStatus.BLOCKED.value());
                            }
                            String prevStatus = advancePaymentData.getAdvanceStatus();
                            advancePaymentData.setAdvanceStatus(AdvancePaymentStatus.REFUND_INITIATED.value());
                            advancePaymentData.setRefundInitiatedBy(paymentRequest.getCreatedBy().getId());
                            advancePaymentData.setRefundDate(AppUtils.getDate(paymentRequest.getAdvancePayment().getRefundDate(), "yyyy-MM-dd"));
                            // dont log agian
                           if(isAddLog) logAdvancePaymentStatus(prevStatus, advancePaymentData.getAdvanceStatus(), advancePaymentData, paymentRequest.getCreatedBy().getId());

                           paymentRequestData.setAdvanceAmount(paymentRequest.getAdvancePayment().getUsedAmount());
                            paymentRequestData.setAdvancePaymentData(advancePaymentData);
                            paymentRequestData = paymentRequestManagementDao.add(paymentRequestData, true);
                            advancePaymentData.setLastUpdatedDate(AppUtils.getCurrentTimestamp());
                            advancePaymentData.setLastUpdatedBy(paymentRequest.getCreatedBy().getId());
                            paymentRequestManagementDao.update(advancePaymentData, true);
                            shouldBreak = false;
                        }
                        if (Objects.nonNull(advancePaymentData.getAdjustedPoSo())) {
                            if (remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
                                logAdvancePaymentAudit(remainingAmount, paymentRequestData, advancePaymentData, paymentRequest.getCreatedBy().getId(), AdvancePaymentStatus.BLOCKED.value());
                            }
                            paymentRequestData.setAdvanceAmount(paymentRequest.getAdvancePayment().getUsedAmount());
                            paymentRequestData.setAdvancePaymentData(advancePaymentData);
                            paymentRequestData = paymentRequestManagementDao.add(paymentRequestData, true);
                            advancePaymentData.setLastUpdatedDate(AppUtils.getCurrentTimestamp());
                            advancePaymentData.setLastUpdatedBy(paymentRequest.getCreatedBy().getId());
                            paymentRequestManagementDao.update(advancePaymentData, true);
                            shouldBreak = false;
                        }
                        remainingAmount = BigDecimal.ZERO;
                        if (shouldBreak) {
                            break;
                        }
                    } else {
                        logAdvancePaymentAudit(remainingAmount, paymentRequestData, advancePaymentData, paymentRequest.getCreatedBy().getId(), AdvancePaymentStatus.BLOCKED.value());
                        paymentRequestData.setAdvanceAmount(paymentRequest.getAdvancePayment().getUsedAmount());
                        paymentRequestData.setAdvancePaymentData(advancePaymentData);
                        paymentRequestData = paymentRequestManagementDao.add(paymentRequestData, true);
                        remainingAmount = BigDecimal.ZERO;
                        break;
                    }
                }
            }
        }
    }
    private Integer validateSrInvoiceNumber(PaymentRequest paymentRequest) {
        try {
            List<Integer> prIds = serviceOrderManagementDao.getExistingSrInFinancialYear(paymentRequest);
            if (prIds == null || prIds.size() == 0) {
                return -1;
            } else {
                return prIds.get(0);
            }
        } catch (Exception e) {
            LOG.error("Error while looking up for Existing Sr In Financial Year", e);
            return 1;
        }
    }


    private void validateAvailableAmount(List<AdvancePaymentData> advancePaymentDataList, BigDecimal availableAmount) throws SumoException {
        BigDecimal avblAmount = BigDecimal.ZERO;
        for (AdvancePaymentData ad : advancePaymentDataList) {
            avblAmount = avblAmount.add(ad.getAvailableAmount());
        }
        if (availableAmount.compareTo(avblAmount) != 0) {
            throw new SumoException("Available Amount MisMatch..!", "Please Check Available Amount of Vendor Advance is not matching..!" +
                    "<br>Current Available Amount is : " + avblAmount + " but Available in this is : " + availableAmount + "" +
                    "<br>Please Retry to create PR Again..!");
        }
    }

    private void createLinkedAdvanceOrRefund(PaymentRequest paymentRequest, PaymentRequestData paymentRequestData, Boolean setPendingForApproval) throws SumoException {
        if (Objects.nonNull(paymentRequest.getAdvancePayment().getPoSoClosed()) && paymentRequest.getAdvancePayment().getPoSoClosed().equalsIgnoreCase(SCMServiceConstants.YES)) {
            if (Objects.nonNull(paymentRequest.getAdvancePayment().getAdvanceType())) {
                List<AdvancePaymentData> advancePaymentDataList = paymentRequestManagementDao.getAdvancePaymentByAdvanceIds(paymentRequest.getAdvancePaymentIds());
                serviceOrderManagementService.validateSoPoForAdjustmentOrRefund(advancePaymentDataList);
                if (Objects.nonNull(paymentRequest.getAdvancePayment().getSelectedSoPo())) {
                    AdvancePaymentData linkedAdvancePayment = createLinkedAdvancePayment(null, paymentRequest.getAdvancePayment(), paymentRequest.getCreatedBy().getId(), paymentRequest, advancePaymentDataList);
                    BigDecimal remainingAmount = paymentRequest.getAdvancePayment().getUsedAmount();
                    for (AdvancePaymentData advancePaymentData : advancePaymentDataList) {
                        if (advancePaymentData.getAvailableAmount().compareTo(BigDecimal.ZERO) > 0) {
                            if (remainingAmount.compareTo(advancePaymentData.getAvailableAmount()) >= 0) {
                                remainingAmount = remainingAmount.subtract(advancePaymentData.getAvailableAmount());
                            }else{
                                advancePaymentData.setAdjustedPoSo(paymentRequest.getAdvancePayment().getSelectedSoPo());
                                advancePaymentData.setChildAdvance(linkedAdvancePayment);
                            }
                        }
                    }
                }
                if (Objects.nonNull(paymentRequest.getAdvancePayment().getRefundDate())) {
                    BigDecimal remainingAmount = paymentRequest.getAdvancePayment().getUsedAmount();
                    for (AdvancePaymentData advancePaymentData : advancePaymentDataList) {
                        if (advancePaymentData.getAvailableAmount().compareTo(BigDecimal.ZERO) > 0) {
                            if (remainingAmount.compareTo(advancePaymentData.getAvailableAmount()) >= 0) {
                                remainingAmount = remainingAmount.subtract(advancePaymentData.getAvailableAmount());
                            } else {
                                remainingAmount = BigDecimal.ZERO;
                                advancePaymentData.setRefundInitiatedBy(paymentRequest.getCreatedBy().getId());
                                advancePaymentData.setRefundDate(AppUtils.getDate(paymentRequest.getAdvancePayment().getRefundDate(), "yyyy-MM-dd"));
                                advancePaymentData.setLastUpdatedDate(AppUtils.getCurrentTimestamp());
                                advancePaymentData.setLastUpdatedBy(paymentRequest.getCreatedBy().getId());
                                paymentRequestManagementDao.update(advancePaymentData, true);
                            }
                        }
                    }
                }
            }
            if (setPendingForApproval) {
                paymentRequestData.setCurrentStatus(PaymentRequestStatus.PENDING_HOD_APPROVAL.value());
            }
        }
    }

    private void validateSrsForpayment(PaymentRequest paymentRequest) throws SumoException {
        if (paymentRequest.getAdvancePayment().getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE) && Objects.nonNull(paymentRequest.getAdvanceSrs()) &&
                !paymentRequest.getAdvanceSrs().isEmpty()) {
            LOG.info("Got Pr Request For Srs :: {}", Arrays.toString(paymentRequest.getAdvanceSrs().toArray()));
            List<Integer> srsForSo = serviceReceiveManagementService.reCheckAllSrsForAdvance(paymentRequest.getAdvanceSrs().get(0));
            List<Integer> missingSrs = new ArrayList<>();
            List<Integer> cancelledSr = new ArrayList<>();
            for (Integer srSo : srsForSo) {
                boolean found = false;
                for (Integer srId : paymentRequest.getAdvanceSrs()) {
                    if (srSo.equals(srId)) {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    missingSrs.add(srSo);
                }
            }
            if (!missingSrs.isEmpty()) {
                throw new SumoException("Missing SR's", "Please Check..! There are Some Missing SR's related to the Same SO of Advance Payment..!<br>" +
                        "Missing Srs in this PR are : " + Arrays.toString(missingSrs.toArray()));
            }
            for (Integer srId : paymentRequest.getAdvanceSrs()) {
                boolean found = false;
                for (Integer srSo : srsForSo) {
                    if (srSo.equals(srId)) {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    cancelledSr.add(srId);
                }
            }
            if (!cancelledSr.isEmpty()) {
                throw new SumoException("Looks Like Some SR's are Cancelled..!", "Please Check..! Some Of the Sr's are Cancelled..!<br>" +
                        "Cancelled Sr Id's are : " + Arrays.toString(cancelledSr.toArray()));
            }
        }
    }

    @Override
    public AdvancePaymentData createLinkedAdvancePayment(AdvancePaymentData advancePaymentData, VendorAdvancePayment vendorAdvancePayment, Integer userId, PaymentRequest paymentRequest, List<AdvancePaymentData> finalAdpList) throws SumoException {
        List<AdvancePaymentData> advancePaymentDataList = getAdvanceList(vendorAdvancePayment, true);
        BigDecimal amountWith5Percent = BigDecimal.ZERO;
        BigDecimal advancePrsAmount = BigDecimal.ZERO;
        vendorAdvancePayment.setPrAmount(vendorAdvancePayment.getFinalAvailable());
        if (vendorAdvancePayment.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
            vendorAdvancePayment.setSoId(vendorAdvancePayment.getSelectedSoPo());
            ServiceOrderData serviceOrderData = paymentRequestManagementDao.find(ServiceOrderData.class, vendorAdvancePayment.getSelectedSoPo());
            amountWith5Percent = SCMUtil.getFivePlusPercentage(serviceOrderData.getTotalAmount());
        }
        if (vendorAdvancePayment.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
            vendorAdvancePayment.setPoId(vendorAdvancePayment.getSelectedSoPo());
            PurchaseOrderData purchaseOrderData = paymentRequestManagementDao.find(PurchaseOrderData.class, vendorAdvancePayment.getSelectedSoPo());
            amountWith5Percent = SCMUtil.getFivePlusPercentage(purchaseOrderData.getPaidAmount());
        }
        for (AdvancePaymentData paymentData : advancePaymentDataList) {
            advancePrsAmount = advancePrsAmount.add(paymentData.getPrAmount());
        }
        BigDecimal afterAddingCurrentPrAmount = advancePrsAmount;
        afterAddingCurrentPrAmount = afterAddingCurrentPrAmount.add(vendorAdvancePayment.getPrAmount());
        if (afterAddingCurrentPrAmount.compareTo(amountWith5Percent) <= 0) {
            vendorAdvancePayment.setCreatedById(userId);
            AdvancePaymentData advPaymentData;
            if (Objects.nonNull(finalAdpList)) {
                advPaymentData = createAdvancePaymentData(vendorAdvancePayment, finalAdpList.get(finalAdpList.size() - 1).getPaymentRequestId());
            } else {
                advPaymentData = createAdvancePaymentData(vendorAdvancePayment, null);
            }
            List<AdvancePaymentData> advancePaymentDatas = Objects.nonNull(finalAdpList) ? finalAdpList : paymentRequestManagementDao.getAdvancePaymentByAdvanceIds(paymentRequest.getAdvancePaymentIds());
            List<AdvancePaymentData> advancePaymentDataForAdjustmentList = new ArrayList<>();
            for (AdvancePaymentData advancePaymentDataForAdjustment : advancePaymentDatas) {
                if (advancePaymentDataForAdjustment.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.CREATED.value())) {
                    advancePaymentDataForAdjustmentList.add(advancePaymentDataForAdjustment);
                }
            }
            advPaymentData.setAdvanceStatus(AdvancePaymentStatus.ADJUST_INITIATED.value());
            if (vendorAdvancePayment.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                PurchaseOrderData purchaseOrderData = paymentRequestManagementDao.find(PurchaseOrderData.class, vendorAdvancePayment.getPoId());
                advPaymentData.setPurchaseOrderData(purchaseOrderData);
                advPaymentData.setMaxSettlementTime(getAdvanceSettlementTime(purchaseOrderData.getType()));
                advPaymentData = paymentRequestManagementDao.add(advPaymentData, true);
                paymentRequestManagementDao.update(purchaseOrderData, true);
            }
            if (vendorAdvancePayment.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
                ServiceOrderData serviceOrderData = paymentRequestManagementDao.find(ServiceOrderData.class, vendorAdvancePayment.getSoId());
                advPaymentData.setServiceOrderData(serviceOrderData);
                advPaymentData.setMaxSettlementTime(getAdvanceSettlementTime(serviceOrderData.getType()));
                advPaymentData = paymentRequestManagementDao.add(advPaymentData, true);
                paymentRequestManagementDao.update(serviceOrderData, true);
            }
            for (AdvancePaymentData paymentData : advancePaymentDataForAdjustmentList) {
                LinkedPaymentsForAdvance linkedPaymentsForAdvance = new LinkedPaymentsForAdvance();
                linkedPaymentsForAdvance.setAdvancePaymentData(advPaymentData);
                linkedPaymentsForAdvance.setPaymentRequestData(paymentData.getPaymentRequestId());
                paymentRequestManagementDao.add(linkedPaymentsForAdvance, true);
            }
            return advPaymentData;
        } else {
            StringBuilder msg = new StringBuilder("Max Limit : " + amountWith5Percent + "<br>");
            if (!advancePaymentDataList.isEmpty()) {
                msg.append("Created Advances : ").append(advancePrsAmount).append(" [").append(Arrays.toString(advancePaymentDataList.stream().mapToInt(AdvancePaymentData::getAdvancePaymentId).boxed().toArray())).append("]");
            }
            msg.append("<br>Current Advance : ").append(vendorAdvancePayment.getFinalAvailable());
            BigDecimal diffAmount = amountWith5Percent.subtract(advancePrsAmount);
            if (diffAmount.compareTo(BigDecimal.ZERO) > 0) {
                msg.append("<br> You Can Only Create Advance Payment Of Amount : ").append(diffAmount);
            }
            throw new SumoException("Advance Limit Exceeded..!", msg.toString());
        }
    }

    private void updateInvoiceQunatity(List<PaymentInvoiceItem> paymentInvoiceItems,
                                       List<ServiceReceivedItemData> serviceItemList) {
        for (PaymentInvoiceItem paymentInvoiceItem : paymentInvoiceItems) {
            ServiceReceivedItemData serviceItemData = serviceItemList.stream()
                    .filter(s -> s.getItemId().equals(paymentInvoiceItem.getServiceReceivedItemId()))
                    .findFirst()
                    .orElse(null);
            if (serviceItemData != null) {
                serviceItemData.setPendingInvoiceQuantity(
                        serviceItemData.getReceivedQuantity().subtract(paymentInvoiceItem.getQuantity()));
                serviceItemData
                        .setInvoiceQuantity(serviceItemData.getInvoiceQuantity().add(paymentInvoiceItem.getQuantity()));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public PaymentRequest updateInvoicePaymentRequest(PaymentRequest paymentRequest) throws SumoException {
        if (paymentRequest.getPaymentRequestId() == null) {
            throw new SumoException("Payment request invalid.");
        }
        if (!paymentRequest.isForceCreate() && paymentRequest.getPaymentInvoice().getInvoiceDate().compareTo(props.getFinancialCalendarStart()) < 0) {
            throw new SumoException("Invoice date does not belong to this financial year.");
        }
        PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class,
                paymentRequest.getPaymentRequestId());
        if (paymentRequestData != null
                && InvoiceDocType.DELIVERY_CHALLAN.value().equals(paymentRequestData.getGrDocType())) {
            if (!paymentRequestData.getCurrentStatus().equalsIgnoreCase(PaymentRequestStatus.INITIATED.value())) {
                throw new SumoException("Payment Request Already Processed..!", "Please Check the Selected payment Request is already Processed...!");
            }
            Date currentTime = SCMUtil.getCurrentTimestamp();
            paymentRequestData.setAmountsMatch(SCMUtil.setStatus(paymentRequest.isAmountsMatch()));
            paymentRequestData.setCurrentStatus(PaymentRequestStatus.CREATED.value());
            if (Objects.nonNull(paymentRequestData.getAdvancePaymentData()) && Objects.nonNull(paymentRequestData.getAdvanceAmount()) &&  !Objects.equals(paymentRequestData.getAdvanceAmount(),BigDecimal.ZERO)){
                paymentRequestData.setCurrentStatus(PaymentRequestStatus.PENDING_HOD_APPROVAL.value());
            }
            paymentRequestData.setInvoiceNumber(paymentRequest.getPaymentInvoice().getInvoiceNumber());
            paymentRequestData.setDeviationCount(paymentRequest.getDeviationCount());
            paymentRequestData.setLastUpdated(currentTime);
            paymentRequestData.setRemarks(paymentRequest.getRemarks());
            VendorDetailData vendorDetailData = paymentRequestManagementDao.find(VendorDetailData.class,
                    paymentRequest.getVendorId().getId());
            paymentRequestData.setIsBlocked(SCMUtil.setStatus(blockedPayment(vendorDetailData)));
            //setting payment date from payment invoice date
            /*Integer creditDays = 0;
            if (vendorDetailData != null && vendorDetailData.getCompanyDetails() != null && vendorDetailData.getCompanyDetails().getCreditDays() != null) {
                creditDays = vendorDetailData.getCompanyDetails().getCreditDays();
            }
            Date paymentDate = SCMUtil.addDays(paymentRequest.getPaymentInvoice().getInvoiceDate(), creditDays);
            if (creditDays == 0) {
                paymentDate = paymentRequest.getPaymentInvoice().getInvoiceDate();
            }*/
            paymentRequestData.setVendorPaymentDate(getPaymentDate(paymentRequest, vendorDetailData));
            Date dateAfterRemoving2Days = getDateAfterRemovingThreshold(paymentRequestData.getVendorPaymentDate());
            paymentRequestData.setPaymentDate(getWorkingDay(dateAfterRemoving2Days));
            paymentRequestData = paymentRequestManagementDao.update(paymentRequestData, false);
            if (paymentRequestData != null) {
                PaymentInvoiceData paymentInvoiceData = paymentRequestManagementDao
                        .findInvoiceByPaymentRequestId(paymentRequest.getPaymentRequestId());
                PaymentInvoice paymentInvoice = paymentRequest.getPaymentInvoice();
                paymentInvoiceData.setInvoiceDocumentHandle(paymentInvoice.getInvoiceDocumentHandle());
                paymentInvoiceData.setInvoiceNumber(paymentInvoice.getInvoiceNumber());
                paymentInvoiceData.setInvoiceDate(paymentInvoice.getInvoiceDate());
                paymentInvoiceData = paymentRequestManagementDao.add(paymentInvoiceData, false);
                if (paymentInvoiceData != null) {
                    for (InvoiceDeviationMapping invoiceDeviationMapping : paymentInvoice.getDeviations()) {
                        invoiceDeviationMapping.setDeviationItemType(PaymentDeviationLevel.INVOICE);
                        invoiceDeviationMapping.setDeviationItemId(paymentInvoiceData.getId());
                        addDeviation(invoiceDeviationMapping);
                    }
                    for (PaymentInvoiceItem paymentInvoiceItem : paymentInvoice.getPaymentInvoiceItems()) {
                        for (InvoiceDeviationMapping invoiceDeviationMapping : paymentInvoiceItem.getDeviations()) {
                            invoiceDeviationMapping.setDeviationItemType(PaymentDeviationLevel.INVOICE_ITEM);
                            invoiceDeviationMapping.setDeviationItemId(paymentInvoiceItem.getPaymentInvoiceItemId());
                            addDeviation(invoiceDeviationMapping);
                        }
                    }
                } else {
                    throw new SumoException("Error updating invoice details.");
                }
            } else {
                throw new SumoException("Error updating payment request.");
            }
            paymentRequestManagementDao.flush();
            paymentRequest.setPaymentRequestId(paymentRequestData.getId());
            PaymentRequestStatusChangeVO changeRequest = new PaymentRequestStatusChangeVO();
            changeRequest.setCurrentStatus(PaymentRequestStatus.INITIATED);
            changeRequest.setNewStatus(PaymentRequestStatus.CREATED);
            changeRequest.setPaymentRequestId(paymentRequest.getPaymentRequestId());
            changeRequest.setUpdatedBy(paymentRequest.getUpdatedBy().getId());
            logPaymentRequestStatusChange(changeRequest);
            String logData = "Payment request id: " + paymentRequestData.getId() + " status changed from: "
                    + changeRequest.getCurrentStatus() + " to: " + changeRequest.getNewStatus() + " by: "
                    + masterDataCache.getEmployee(changeRequest.getUpdatedBy()) + "[" + changeRequest.getUpdatedBy()
                    + "].";
            logPaymentRequestData(logData, paymentRequestData.getId());
        } else {
            throw new SumoException("Invalid Payment Request.");
        }
        return paymentRequest;
    }

    public MultipartFile compressPdf(File destFile, MultipartFile file, MultipartFile compressedFile) throws IOException, DocumentException, SumoException {

        FileInputStream input = null;
        try {
            PdfReader reader = new PdfReader(file.getInputStream());
            PdfStamper stamper = new PdfStamper(reader, new FileOutputStream(destFile), PdfWriter.VERSION_1_5);
            stamper.getWriter().setCompressionLevel(9);
            int total = reader.getNumberOfPages() + 1;
            for (int i = 1; i < total; i++) {
                reader.setPageContent(i, reader.getPageContent(i));
            }
            stamper.setFullCompression();
            stamper.close();
            reader.close();
            input = new FileInputStream(destFile);
            compressedFile = new MockMultipartFile(file.getName(),
                    file.getOriginalFilename(), file.getContentType(), IOUtils.toByteArray(input));

        } catch (Exception e) {
            LOG.info("Error While Compressing PDF : {} ", file.getOriginalFilename());
            LOG.info(e.getMessage());
            throw new SumoException("Error While Compressing PDF", e.getMessage());
        } finally {
            if (Objects.nonNull(input)) {
                input.close();
            }
        }
        return compressedFile;
    }

    public byte[] compressImage(MultipartFile image, String mimeType) throws IOException {

        InputStream inputStream = image.getInputStream();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        float imageQuality = 0.6f;

        // Create the buffered image
        BufferedImage bufferedImage = ImageIO.read(inputStream);

        // Get image writers
        Iterator<ImageWriter> imageWriters = ImageIO.getImageWritersByFormatName("jpeg"); // Input your Format Name here

        if (!imageWriters.hasNext())
            throw new IllegalStateException("Writers Not Found!!");

        ImageWriter imageWriter = imageWriters.next();
        ImageOutputStream imageOutputStream = ImageIO.createImageOutputStream(outputStream);
        imageWriter.setOutput(imageOutputStream);

        ImageWriteParam imageWriteParam = imageWriter.getDefaultWriteParam();

        // Set the compress quality metrics
        imageWriteParam.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        imageWriteParam.setCompressionQuality(imageQuality);

        // Compress and insert the image into the byte array.
        imageWriter.write(null, new IIOImage(bufferedImage, null, null), imageWriteParam);

        byte[] imageBytes = outputStream.toByteArray();

        // close all streams
        inputStream.close();
        outputStream.close();
        imageOutputStream.close();
        imageWriter.dispose();


        return imageBytes;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public VendorAdvancePayment getVendorAdvancePayment(VendorAdvancePayment vendorAdvanceRequest, Boolean isView, AdvancePaymentData advance) throws SumoException {
        try {
            AdvancePaymentData advancePaymentData = advance;
            if (Objects.isNull(advancePaymentData)) {
                List<String> statusList = vendorAdvanceRequest.getAdvanceStatus().equalsIgnoreCase("ALL") ? Arrays.asList(AdvancePaymentStatus.INITIATED.value(), AdvancePaymentStatus.CREATED.value()) : Collections.singletonList(vendorAdvanceRequest.getAdvanceStatus());
                advancePaymentData = paymentRequestManagementDao.getVendorAdvancePayment(vendorAdvanceRequest.getVendorId(), statusList, vendorAdvanceRequest.getAdvanceType()
                        , vendorAdvanceRequest.getPoId(), vendorAdvanceRequest.getSoId());
            }
            if (Objects.nonNull(advancePaymentData)) {
                List<Integer> exceptionalPrs = new ArrayList<>();
                List<Integer> prIds = advancePaymentData.getAdvancePaymentAuditLogDataList().stream().map(AdvancePaymentAuditLogData::getPrId).collect(Collectors.toList());
                for (Integer prId : prIds) {
                    PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class, prId);
                    if (paymentRequestData.getCurrentStatus().equalsIgnoreCase(PaymentRequestStatus.CANCELLED.value()) ||
                            paymentRequestData.getCurrentStatus().equalsIgnoreCase(PaymentRequestStatus.REJECTED.value())
                            || paymentRequestData.getCurrentStatus().equalsIgnoreCase(PaymentRequestStatus.HOD_REJECTED.value())) {
                        continue;
                    }
                    if (!paymentRequestData.getCurrentStatus().equalsIgnoreCase(PaymentRequestStatus.PAID.value())) {
                        exceptionalPrs.add(prId);
                    }
                }
                if (!exceptionalPrs.isEmpty() && !isView) {
                    throw new SumoException("Please Settle all the PR's Linked to this vendor advance..!", "Please settle the PR's Linked with this Vendor advance payment to create new advance payment ..!" +
                            "<br> List of Prs are : " + Arrays.toString(exceptionalPrs.toArray()));
                }
                VendorAdvancePayment advancePayment = VendorAdvancePayment.builder().advancePaymentId(advancePaymentData.getAdvancePaymentId())
                        .advanceStatus(advancePaymentData.getAdvanceStatus())
                        .advanceType(advancePaymentData.getAdvanceType())
                        .paymentRequestId(advancePaymentData.getPaymentRequestId().getId())
                        .availableAmount(advancePaymentData.getAvailableAmount())
                        .blockedAmount(advancePaymentData.getBlockedAmount())
                        .prAmount(advancePaymentData.getPrAmount())
                        .createdBy(advancePaymentData.getCreatedBy())
                        .rejectedFor(advancePaymentData.getRejectedFor())
                        .pendingPrs(exceptionalPrs)
                        .vendorId(advancePaymentData.getVendorId())
                        .refundInitiatedBy(advancePaymentData.getRefundInitiatedBy())
                        .lastUpdatedBy(advancePaymentData.getLastUpdatedBy())
                        .lastUpdatedDate(advancePaymentData.getLastUpdatedDate())
                        .lastPoSoStatus(advancePaymentData.getLastPoSoStatus())
                        .selectedSoPo(advancePaymentData.getAdjustedPoSo())
                        .maxSettlementTime(advancePaymentData.getMaxSettlementTime())
                        .diffDays(AppUtils.getActualDayDifference(AppUtils.getCurrentTimestamp(), advancePaymentData.getMaxSettlementTime()))
                        .createdAt(advancePaymentData.getCreatedAt()).build();
                if (Objects.nonNull(advancePaymentData.getRefundInitiatedBy())) {
                    advancePayment.setRefundInitiatedByName(SCMUtil.getCreatedBy(masterDataCache.getEmployee(advancePaymentData.getRefundInitiatedBy()), advancePaymentData.getRefundInitiatedBy()));
                }
                if (!advancePayment.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.STAND_ALONE_ADVANCE)) {
                    if (advancePayment.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                        advancePayment.setPoId(advancePaymentData.getPurchaseOrderData().getId());
                    } else {
                        advancePayment.setSoId(advancePaymentData.getServiceOrderData().getId());
                    }
                    if (Objects.nonNull(advancePaymentData.getAdjustedPoSo())) {
                        advancePayment.setPoSoClosed("Yes");
                        advancePayment.setSelectedSoPo(advancePaymentData.getAdjustedPoSo());
                    }
                    if (Objects.nonNull(advancePaymentData.getRefundDate())) {
                        advancePayment.setPoSoClosed("Yes");
                        advancePayment.setRefundDate(AppUtils.getFormattedTime(advancePaymentData.getRefundDate(), "dd-MM-yyyy"));
                    }
                }
                if (Objects.nonNull(advancePaymentData.getAdvancePaymentAuditLogDataList())) {
                    List<VendorAdvancePaymentAuditLog> vendorAdvancePaymentAuditLogs = new ArrayList<>();
                    for (AdvancePaymentAuditLogData advancePaymentAuditLogData : advancePaymentData.getAdvancePaymentAuditLogDataList()) {
                        vendorAdvancePaymentAuditLogs.add(VendorAdvancePaymentMapper.INSTANCE.toVendorAdvancePaymentLog(advancePaymentAuditLogData));
                    }
                    advancePayment.setVendorAdvancePaymentAuditLogs(vendorAdvancePaymentAuditLogs);
                }
                if (Objects.nonNull(vendorAdvanceRequest.getAdvanceType())) {
                    if (vendorAdvanceRequest.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                        PurchaseOrderData purchaseOrderData = advancePaymentData.getPurchaseOrderData();
                        List<PurchaseOrderVendorGRMappingData> grMappingList = purchaseOrderData.getGrMappingList();
                        List<Integer> exceptionalGrs = new ArrayList<>();
                        grMappingList.forEach(item -> {
                            VendorGoodsReceivedData vendorGoodsReceivedData = item.getVendorGoodsReceivedData();
                            if (!vendorGoodsReceivedData.getGrStatus().equalsIgnoreCase(SCMOrderStatus.CANCELLED.value())) {
                                if (Objects.isNull(vendorGoodsReceivedData.getPaymentRequestData())) {
                                    exceptionalGrs.add(vendorGoodsReceivedData.getGoodsReceivedId());
                                } else {
                                    PaymentRequestData paymentRequestData = vendorGoodsReceivedData.getPaymentRequestData();
                                    if (!paymentRequestData.getCurrentStatus().equalsIgnoreCase(PaymentRequestStatus.PAID.value())) {
                                        exceptionalGrs.add(vendorGoodsReceivedData.getGoodsReceivedId());
                                    }
                                }
                            }
                        });
                        advancePayment.setPendingGrs(exceptionalGrs);
                    }
                    if (vendorAdvanceRequest.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
                        ServiceOrderData serviceOrderData = advancePaymentData.getServiceOrderData();
                        List<Integer> pendingSrs = new ArrayList<>();
                        List<ServiceOrderServiceReceiveMappingData> soSrList = serviceReceiveManagementDao.findSrsWithSoId(serviceOrderData.getId());
                        soSrList.forEach(soSrData -> {
                            ServiceReceivedData receivedData = soSrData.getServiceReceivedData();
                            if (Objects.isNull(receivedData.getPaymentRequestData()) && !receivedData.getServiceReceiveStatus().equalsIgnoreCase(SCMOrderStatus.CANCELLED.value())) {
                                pendingSrs.add(receivedData.getServiceReceivedId());
                            }
                        });
                        advancePayment.setPendingSrs(pendingSrs);
                    }
                }
                return advancePayment;
            }
        } catch (SumoException e) {
            throw e;
        } catch (Exception e) {
            LOG.error("Exception Occurred While getting the Vendor Advance Payment ::: ", e);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public PaymentRequest createVendorAdvancePayment(VendorAdvancePayment vendorAdvanceRequest, Integer loggedInUnit) throws SumoException {
        try {
            Unit unit = masterDataCache.getUnit(loggedInUnit);
            Integer companyId = unit.getCompany().getId();
            if(props.getChaayosCompanyIds().contains(companyId)) {
                companyId = SCMServiceConstants.CHAAYOS_COMPANY_ID;
            }
            if (vendorAdvanceRequest.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.STAND_ALONE_ADVANCE)) {
                VendorAdvancePayment vendorAdvancePayment = getVendorAdvancePayment(vendorAdvanceRequest, Boolean.FALSE, null);
                if (Objects.nonNull(vendorAdvancePayment)) {
                    throw new SumoException("Advance Payment Already Created..!", "Advance payment of <b>" + vendorAdvancePayment.getPrAmount() + "</b> for Vendor : " + scmCache.getVendorDetail(vendorAdvanceRequest.getVendorId()).getEntityName()
                            + " With PR ID : " + vendorAdvancePayment.getPaymentRequestId());
                } else {
                    PaymentRequest paymentRequest = createPrForVendorAdvancePayment(vendorAdvanceRequest.getVendorId(), vendorAdvanceRequest.getPrAmount(), vendorAdvanceRequest.getCreatedById(), vendorAdvanceRequest.getAdvanceDocId(), companyId);
                    if (Objects.nonNull(paymentRequest)) {
                        PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class, paymentRequest.getPaymentRequestId());
                        AdvancePaymentData advancePaymentData = createAdvancePaymentData(vendorAdvanceRequest, paymentRequestData);
                        paymentRequestManagementDao.add(advancePaymentData, true);
                        return paymentRequest;
                    }
                }
            } else {
                if (validatePoSoStatus(vendorAdvanceRequest, false)) {
                    List<AdvancePaymentData> advancePaymentDataList = getAdvanceList(vendorAdvanceRequest, false);
                    BigDecimal amountWith5Percent;
                    BigDecimal advancePrsAmount = BigDecimal.ZERO;
                    if (vendorAdvanceRequest.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                        PurchaseOrderData purchaseOrderData = paymentRequestManagementDao.find(PurchaseOrderData.class, vendorAdvanceRequest.getPoId());
                        amountWith5Percent = SCMUtil.getFivePlusPercentage(purchaseOrderData.getPaidAmount());
                    } else {
                        ServiceOrderData serviceOrderData = paymentRequestManagementDao.find(ServiceOrderData.class, vendorAdvanceRequest.getSoId());
                        amountWith5Percent = SCMUtil.getFivePlusPercentage(serviceOrderData.getTotalAmount());
                    }
                    for (AdvancePaymentData advancePaymentData : advancePaymentDataList) {
                        advancePrsAmount = advancePrsAmount.add(advancePaymentData.getPrAmount());
                    }
                    BigDecimal afterAddingCurrentPrAmount = advancePrsAmount;
                    afterAddingCurrentPrAmount = afterAddingCurrentPrAmount.add(vendorAdvanceRequest.getPrAmount());
                    if (afterAddingCurrentPrAmount.compareTo(amountWith5Percent) <= 0) {
                        PaymentRequest paymentRequest = createPrForVendorAdvancePayment(vendorAdvanceRequest.getVendorId(), vendorAdvanceRequest.getPrAmount(), vendorAdvanceRequest.getCreatedById(), vendorAdvanceRequest.getAdvanceDocId(), companyId);
                        if (Objects.nonNull(paymentRequest)) {
                            PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class, paymentRequest.getPaymentRequestId());
                            AdvancePaymentData advancePaymentData = createAdvancePaymentData(vendorAdvanceRequest, paymentRequestData);
                            if (vendorAdvanceRequest.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                                PurchaseOrderData purchaseOrderData = paymentRequestManagementDao.find(PurchaseOrderData.class, vendorAdvanceRequest.getPoId());
                                advancePaymentData.setPurchaseOrderData(purchaseOrderData);
                                advancePaymentData.setMaxSettlementTime(getAdvanceSettlementTime(purchaseOrderData.getType()));
                                advancePaymentData = paymentRequestManagementDao.add(advancePaymentData, true);
                                paymentRequestManagementDao.update(purchaseOrderData, true);
                            } else {
                                ServiceOrderData serviceOrderData = paymentRequestManagementDao.find(ServiceOrderData.class, vendorAdvanceRequest.getSoId());
                                advancePaymentData.setServiceOrderData(serviceOrderData);
                                advancePaymentData.setMaxSettlementTime(getAdvanceSettlementTime(serviceOrderData.getType()));
                                advancePaymentData = paymentRequestManagementDao.add(advancePaymentData, true);
                                paymentRequestManagementDao.update(serviceOrderData, true);
                            }
                            paymentRequestData.setAdvancePaymentData(advancePaymentData);
                            paymentRequestData.setAdvanceAmount(advancePaymentData.getPrAmount());
                            paymentRequestManagementDao.update(paymentRequestData, true);
                            LinkedPaymentsForAdvance linkedPaymentsForAdvance = new LinkedPaymentsForAdvance();
                            linkedPaymentsForAdvance.setAdvancePaymentData(advancePaymentData);
                            linkedPaymentsForAdvance.setPaymentRequestData(paymentRequestData);
                            paymentRequestManagementDao.add(linkedPaymentsForAdvance, true);
                            return paymentRequest;
                        }
                    } else {
                        StringBuilder msg = new StringBuilder("Max Limit : " + amountWith5Percent + "<br>");
                        if (!advancePaymentDataList.isEmpty()) {
                            msg.append("Created Advances : ").append(advancePrsAmount).append(Arrays.toString(advancePaymentDataList.stream().mapToInt(AdvancePaymentData::getAdvancePaymentId).boxed().toArray()));
                        }
                        msg.append("<br>Current Advance : ").append(vendorAdvanceRequest.getPrAmount());
                        BigDecimal diffAmount = amountWith5Percent.subtract(advancePrsAmount);
                        if (diffAmount.compareTo(BigDecimal.ZERO) > 0) {
                            msg.append("<br> You Can Only Create Advance Payment Of Amount : ").append(diffAmount);
                        } else {
                            msg.append("<br> You Can not Create Advance Payment");
                        }
                        throw new SumoException("Advance Limit Exceeded..!", msg.toString());
                    }
                } else {
                    String msg = "";
                    if (vendorAdvanceRequest.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                        msg = "Selected Purchase Order is not eligible to create Vendor Advance ..!";
                    }
                    if (vendorAdvanceRequest.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
                        msg = "Selected Service Order is not eligible to create Vendor Advance ..!";
                    }
                    throw new SumoException("Selected Item is Not In Approved Status..! ", msg);
                }
            }
        } catch (SumoException e) {
            throw e;
        } catch (Exception e) {
            LOG.error("Exception Occurred While creating new Vendor Advance Payment :: ", e);
        }
        return null;
    }

    @Override
    public Boolean validatePoSoStatus(VendorAdvancePayment vendorAdvanceRequest, Boolean checkAdjusted) {
        if (vendorAdvanceRequest.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
            PurchaseOrderData purchaseOrderData = paymentRequestManagementDao.find(PurchaseOrderData.class, checkAdjusted ? vendorAdvanceRequest.getSelectedSoPo() : vendorAdvanceRequest.getPoId());
            return purchaseOrderData.getStatus().equalsIgnoreCase(PurchaseOrderStatus.APPROVED.value());
        }
        if (vendorAdvanceRequest.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
            ServiceOrderData serviceOrderData = paymentRequestManagementDao.find(ServiceOrderData.class, checkAdjusted ? vendorAdvanceRequest.getSelectedSoPo() : vendorAdvanceRequest.getSoId());
            return serviceOrderData.getStatus().equalsIgnoreCase(ServiceOrderStatus.APPROVED.value());
        }
        return false;
    }

    @Override
    public Date getAdvanceSettlementTime(String type) {
        if (Objects.nonNull(type)) {
            if (type.equalsIgnoreCase("CAPEX")) {
                return AppUtils.getDateAfterDays(AppUtils.getCurrentTimestamp(), 180);
            }
            if (type.equalsIgnoreCase("OPEX")) {
                return AppUtils.getDateAfterDays(AppUtils.getCurrentTimestamp(), 90);
            }
        }
        return AppUtils.getDateAfterDays(AppUtils.getCurrentTimestamp(), 90);
    }

    private AdvancePaymentData createAdvancePaymentData(VendorAdvancePayment vendorAdvanceRequest, PaymentRequestData paymentRequestData) {
        AdvancePaymentData advancePaymentData = new AdvancePaymentData();
        advancePaymentData.setPaymentRequestId(paymentRequestData);
        advancePaymentData.setAdvanceStatus(AppConstants.INITIATED);
        advancePaymentData.setVendorId(vendorAdvanceRequest.getVendorId());
        advancePaymentData.setPrAmount(vendorAdvanceRequest.getPrAmount());
        advancePaymentData.setAvailableAmount(vendorAdvanceRequest.getPrAmount());
        advancePaymentData.setBlockedAmount(BigDecimal.ZERO);
        advancePaymentData.setCreatedBy(SCMUtil.getCreatedBy(masterDataCache.getEmployee(vendorAdvanceRequest.getCreatedById()), vendorAdvanceRequest.getCreatedById()));
        advancePaymentData.setCreatedAt(AppUtils.getCurrentTimestamp());
        advancePaymentData.setAdvanceType(vendorAdvanceRequest.getAdvanceType());
        return advancePaymentData;
    }

    private List<AdvancePaymentData> getAdvanceList(VendorAdvancePayment vendorAdvanceRequest, boolean checkAdjusted) throws SumoException {
        List<AdvancePaymentData> advancePaymentDataList = new ArrayList<>();
        StringBuilder msg = new StringBuilder("");
        if (vendorAdvanceRequest.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
            PurchaseOrderData purchaseOrderData = paymentRequestManagementDao.find(PurchaseOrderData.class, checkAdjusted ? vendorAdvanceRequest.getSelectedSoPo() : vendorAdvanceRequest.getPoId());
            if (Objects.nonNull(purchaseOrderData.getAdvancePaymentDatas()) && !purchaseOrderData.getAdvancePaymentDatas().isEmpty()) {
                for (AdvancePaymentData advancePaymentData : purchaseOrderData.getAdvancePaymentDatas()) {
                    if (advancePaymentData.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.INITIATED.value()) ||
                            advancePaymentData.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.CREATED.value())) {
                        advancePaymentDataList.add(advancePaymentData);
                    } else {
                        String err = "Advance Payment Id : " + advancePaymentData.getAdvancePaymentId() + " [ " + advancePaymentData.getAdvanceStatus() + " ] <br>";
                        msg.append(err);
                    }
                }
            }
        }
        if (vendorAdvanceRequest.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
            ServiceOrderData serviceOrderData = paymentRequestManagementDao.find(ServiceOrderData.class, checkAdjusted ? vendorAdvanceRequest.getSelectedSoPo() : vendorAdvanceRequest.getSoId());
            if (Objects.nonNull(serviceOrderData.getAdvancePaymentDatas()) && !serviceOrderData.getAdvancePaymentDatas().isEmpty()) {
                for (AdvancePaymentData advancePaymentData : serviceOrderData.getAdvancePaymentDatas()) {
                    if (advancePaymentData.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.INITIATED.value()) ||
                            advancePaymentData.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.CREATED.value())) {
                        advancePaymentDataList.add(advancePaymentData);
                    } else {
                        String err = "Advance Payment Id : " + advancePaymentData.getAdvancePaymentId() + " [ " + advancePaymentData.getAdvanceStatus() + " ] <br>";
                        msg.append(err);
                    }
                }
            }
        }
        if (!msg.toString().equalsIgnoreCase("")) {
            throw new SumoException(checkAdjusted ? "Can not Adjust Advance" : "Can not Create Advance", msg.toString());
        }
        return advancePaymentDataList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VendorAdvancePayment> getAllVendorAdvances(Date startDate, Date endDate, Integer vendorId, String status, Integer advancePaymentId) {
        List<VendorAdvancePayment> result = new ArrayList<>();
        try {
            List<String> statusList = Objects.nonNull(status) ? Collections.singletonList(status) : AdvancePaymentStatus.getAllStatus();
            List<AdvancePaymentData> advancePaymentDataList = paymentRequestManagementDao.getAllVendorAdvances(startDate, endDate, vendorId, statusList, Objects.nonNull(advancePaymentId) ? Collections.singletonList(advancePaymentId) : null);
            for (AdvancePaymentData advancePaymentData : advancePaymentDataList) {
                VendorAdvancePayment vendorAdvancePayment = VendorAdvancePaymentMapper.INSTANCE.toVendorAdvancePayment(advancePaymentData);
                if (Objects.nonNull(advancePaymentData.getRefundDate())) {
                    vendorAdvancePayment.setRefundDate(AppUtils.getDateString(advancePaymentData.getRefundDate(), "yyyy-MM-dd"));
                    vendorAdvancePayment.setRefundInitiatedByName(SCMUtil.getCreatedBy(masterDataCache.getEmployee(advancePaymentData.getRefundInitiatedBy()), advancePaymentData.getRefundInitiatedBy()));
                }
                if (Objects.nonNull(advancePaymentData.getChildAdvance())) {
                    vendorAdvancePayment.setAdjustedWith(VendorAdvancePaymentMapper.INSTANCE.toVendorAdvancePayment(advancePaymentData.getChildAdvance()));
                }
                List<VendorAdvancePaymentAuditLog> vendorAdvancePaymentAuditLogs = new ArrayList<>();
                List<VendorAdvancePaymentStatusLog> vendorAdvancePaymentStatusLogs = new ArrayList<>();
                for (AdvancePaymentAuditLogData advancePaymentAuditLogData : advancePaymentData.getAdvancePaymentAuditLogDataList()) {
                    vendorAdvancePaymentAuditLogs.add(VendorAdvancePaymentMapper.INSTANCE.toVendorAdvancePaymentLog(advancePaymentAuditLogData));
                }
                for (AdvancePaymentStatusLog advancePaymentStatusLog : advancePaymentData.getAdvancePaymentStatusLogs()) {
                    VendorAdvancePaymentStatusLog vendorAdvancePaymentStatusLog = VendorAdvancePaymentMapper.INSTANCE.toVendorAdvanceStatusLog(advancePaymentStatusLog);
                    vendorAdvancePaymentStatusLog.setLoggedByName(SCMUtil.getCreatedBy(masterDataCache.getEmployee(vendorAdvancePaymentStatusLog.getLoggedBy()), vendorAdvancePaymentStatusLog.getLoggedBy()));
                    vendorAdvancePaymentStatusLogs.add(vendorAdvancePaymentStatusLog);
                }
                vendorAdvancePayment.setVendorAdvancePaymentAuditLogs(vendorAdvancePaymentAuditLogs);
                vendorAdvancePayment.setVendorAdvancePaymentStatusLogs(vendorAdvancePaymentStatusLogs);
                if (Objects.nonNull(advancePaymentData.getAdvanceRefundDocument())) {
                    vendorAdvancePayment.setAdvanceRefundDocId(advancePaymentData.getAdvanceRefundDocument());
                }
                if (Objects.nonNull(advancePaymentData.getLastUpdatedBy())) {
                    vendorAdvancePayment.setLastUpdatedByName(SCMUtil.getCreatedBy(masterDataCache.getEmployee(advancePaymentData.getLastUpdatedBy()), advancePaymentData.getLastUpdatedBy()));
                }
                result.add(vendorAdvancePayment);
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While getting All Vendor Advances ::: ", e);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean submitAdvanceRefundedDate(Integer advancePaymentId, String refundedDate, Integer receivedBy, Integer refundDocId) throws SumoException {
        AdvancePaymentData advancePaymentData = paymentRequestManagementDao.find(AdvancePaymentData.class, advancePaymentId);
        advancePaymentData.setAdvanceRefundDocument(refundDocId);
        String prevStatus = advancePaymentData.getAdvanceStatus();
        advancePaymentData.setRefundReceivedDate(AppUtils.getDate(refundedDate, "yyyy-MM-dd"));
        advancePaymentData.setAdvanceStatus(AdvancePaymentStatus.REFUNDED.value());
        advancePaymentData.setLastUpdatedDate(AppUtils.getCurrentTimestamp());
        advancePaymentData.setLastUpdatedBy(receivedBy);
        logAdvancePaymentStatus(prevStatus, advancePaymentData.getAdvanceStatus(), advancePaymentData, receivedBy);
        paymentRequestManagementDao.update(advancePaymentData, true);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void refreshVendorAdvancePaymentsCache(Integer vendor) {
        LOG.info("Started Updating the cache of Vendor for Advance Payments :::::: at : {}", AppUtils.getCurrentTimestamp());
        Date currentDate = AppUtils.getCurrentDate();
        List<String> nonRefundStatusList = Arrays.asList(AdvancePaymentStatus.INITIATED.value(), AdvancePaymentStatus.CREATED.value());
        Map<Integer, VendorDetailData> vendorsData = new HashMap<>();
        if (Objects.isNull(vendor)) {
            vendorsData = scmMetadataDao.findAllVendorDetailData().stream().collect(Collectors.toMap(VendorDetailData::getVendorId, Function.identity()));
        }
        List<AdvancePaymentData> advancePaymentDataList = paymentRequestManagementDao.getNonRefundAdvancesForBlocking(currentDate, nonRefundStatusList, vendor);
        if (!advancePaymentDataList.isEmpty()) {
            Map<Integer, List<AdvancePaymentData>> advancesByVendor = advancePaymentDataList.stream().collect(Collectors.groupingBy(AdvancePaymentData::getVendorId));
            for (Map.Entry<Integer, List<AdvancePaymentData>> entry : advancesByVendor.entrySet()) {
                Integer key = entry.getKey();
                List<AdvancePaymentData> advances = entry.getValue();
                String blockMessage = "" + Arrays.asList(advances.stream().map(AdvancePaymentData::getAdvancePaymentId).toArray());
                VendorDetail vendorDetail = scmCache.getVendorDetail(key);
                if (Objects.nonNull(vendorDetail)) {
                    VendorDetailData vendorDetailData = null;
                    if (Objects.isNull(vendor)) {
                        vendorDetailData = vendorsData.get(key);
                    } else {
                        vendorDetailData = paymentRequestManagementDao.find(VendorDetailData.class, key);
                    }
                    if (Objects.isNull(vendorDetailData.getVendorBlockedReason()) ||
                            (Objects.nonNull(vendorDetailData.getVendorBlockedReason()) && !vendorDetailData.getVendorBlockedReason().equalsIgnoreCase("MANUAL"))) {
                        if (Objects.nonNull(vendorDetailData.getUnblockedTillDate())) {
                            if (vendorDetailData.getUnblockedTillDate().before(currentDate)) {
                                vendorDetailData.setVendorBlocked(AppConstants.YES);
                                vendorDetailData.setVendorBlockedReason(blockMessage);
                                vendorDetailData.setUnblockedTillDate(null);
                                paymentRequestManagementDao.update(vendorDetailData, true);
                                vendorDetail.setBlockedReason(blockMessage);
                                vendorDetail.setVendorBlocked(AppConstants.YES);
                                scmCache.getVendorDetails().put(key, vendorDetail);
                            } else {
                                vendorDetail.setVendorBlocked(null);
                                vendorDetail.setBlockedReason(vendorDetailData.getVendorBlockedReason());
                                vendorDetail.setUnblockedTillDate(vendorDetailData.getUnblockedTillDate());
                                scmCache.getVendorDetails().put(key, vendorDetail);
                            }
                        } else {
                            vendorDetailData.setVendorBlocked(AppConstants.YES);
                            vendorDetailData.setVendorBlockedReason(blockMessage);
                            paymentRequestManagementDao.update(vendorDetailData, true);
                            vendorDetail.setBlockedReason(blockMessage);
                            vendorDetail.setVendorBlocked(AppConstants.YES);
                            scmCache.getVendorDetails().put(key, vendorDetail);
                        }
                    }
                }
            }
            Collection<VendorDetail> vendorDetails = Objects.nonNull(vendor) ? Collections.singletonList(scmCache.getVendorDetail(vendor)) : scmCache.getVendorDetails().values();
            vendorDetails.forEach(vendorDetail -> {
                if (!advancesByVendor.containsKey(vendorDetail.getVendorId())) {
                    updateVendorBlockStatus(vendorDetail);
                }
            });
        } else {
            Collection<VendorDetail> vendorDetails = Objects.nonNull(vendor) ? Collections.singletonList(scmCache.getVendorDetail(vendor)) : scmCache.getVendorDetails().values();
            vendorDetails.forEach(this::updateVendorBlockStatus);
        }
        LOG.info("Completed Updating the cache of Vendor for Advance Payment :::::: at : {}", AppUtils.getCurrentTimestamp());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void sendVendorAdvancesReminder() {
        try {
            LOG.info("Sending vendorAdvancesReminder");
            List<AdvancePaymentData> refundList = paymentRequestManagementDao.getRefundProcessedVendorAdvance();
            List<AdvancePaymentData> blockedVendorsAdvancesList = new ArrayList<>();
            List<VendorDetailData> blockedVendors = paymentRequestManagementDao.getBlockedVendorsDueToAdvance();
            if (Objects.nonNull(blockedVendors) && !blockedVendors.isEmpty()) {
                blockedVendors.forEach(vendorDetailData -> {
                    if (Objects.nonNull(vendorDetailData.getVendorBlockedReason()) && !vendorDetailData.getVendorBlockedReason().equalsIgnoreCase("MANUAL")) {
                        String[] advPaymentIdsString = vendorDetailData.getVendorBlockedReason().replaceAll("[\\[\\]]", "").split(", ");
                        List<String> strList = Arrays.asList(advPaymentIdsString);
                        List<Integer> advancePaymentIds = strList.stream().map(Integer::parseInt).collect(Collectors.toList());
                        blockedVendorsAdvancesList.addAll(paymentRequestManagementDao.getAllVendorAdvances(null, null, null, null, advancePaymentIds));
                    }
                });
            }
            List<AdvancePaymentData> allRunningAdvances = paymentRequestManagementDao.getAllRunningAdvances();
            List<VendorAdvancePayment> refundListPayments = refundList.stream().map(e -> {
                VendorAdvancePayment vendorAdvancePayment = VendorAdvancePayment.builder().vendorId(e.getVendorId())
                        .advanceStatus("ALL").advanceType(e.getAdvanceType()).build();
                VendorAdvancePayment advance = null;
                try {
                    advance = getVendorAdvancePayment(vendorAdvancePayment, true, e);
                } catch (Exception ex) {
                    LOG.info("Exception during Vendor Advance ..!", ex);
                }
                return advance;
            }).sorted(Comparator.comparing(VendorAdvancePayment::getDiffDays)).collect(Collectors.toList());
            List<VendorAdvancePayment> blockedPaymentsList = blockedVendorsAdvancesList.stream().map(e -> {
                VendorAdvancePayment vendorAdvancePayment = VendorAdvancePayment.builder().vendorId(e.getVendorId())
                        .advanceStatus("ALL").advanceType(e.getAdvanceType()).build();
                VendorAdvancePayment advance = null;
                try {
                    advance = getVendorAdvancePayment(vendorAdvancePayment, true, e);
                } catch (Exception ex) {
                    LOG.info("Exception during Vendor Advance ..!", ex);
                }
                return advance;
            }).sorted(Comparator.comparing(VendorAdvancePayment::getDiffDays)).collect(Collectors.toList());
            List<VendorAdvancePayment> allRunningAdvancesList = allRunningAdvances.stream().map(e -> {
                VendorAdvancePayment vendorAdvancePayment = VendorAdvancePayment.builder().vendorId(e.getVendorId())
                        .advanceStatus("ALL").advanceType(e.getAdvanceType()).build();
                VendorAdvancePayment advance = null;
                try {
                    advance = getVendorAdvancePayment(vendorAdvancePayment, true, e);
                } catch (Exception ex) {
                    LOG.info("Exception during Vendor Advance ..!", ex);
                }
                return advance;
            }).sorted(Comparator.comparing(VendorAdvancePayment::getDiffDays)).collect(Collectors.toList());
            Map<String, List<VendorAdvancePayment>> vendorAdvances = new HashMap<>();
            Map<String, BigDecimal> advanceData = new HashMap<>();
            vendorAdvances.put("PENDING_REFUND", refundListPayments);
            Optional<BigDecimal> refundAmount = refundListPayments.stream().map(VendorAdvancePayment::getAvailableAmount).reduce(SCMUtil::add);
            advanceData.put("PENDING_REFUND", refundAmount.isPresent() ? refundAmount.get() : BigDecimal.ZERO);
            vendorAdvances.put("BLOCKED_VENDORS", blockedPaymentsList);
            Optional<BigDecimal> blockedAmount = blockedPaymentsList.stream().map(VendorAdvancePayment::getAvailableAmount).reduce(SCMUtil::add);
            advanceData.put("BLOCKED_VENDORS", blockedAmount.isPresent() ? blockedAmount.get() : BigDecimal.ZERO);
            vendorAdvances.put("RUNNING_VENDOR_ADVANCES", allRunningAdvancesList);
            Optional<BigDecimal> runningAmount = allRunningAdvancesList.stream().map(VendorAdvancePayment::getAvailableAmount).reduce(SCMUtil::add);
            advanceData.put("RUNNING_VENDOR_ADVANCES", runningAmount.isPresent() ? runningAmount.get() : BigDecimal.ZERO);
            VendorAdvancesEmailNotificationTemplate notificationTemplate = new VendorAdvancesEmailNotificationTemplate(vendorAdvances, advanceData, scmCache.getVendorDetails(), props.getBasePath());
            VendorAdvancesEmailNotification notification = new VendorAdvancesEmailNotification(notificationTemplate, props.getEnvType(), new ArrayList<>());
            generateVendorAdvancesExcel(vendorAdvances, notification);
        } catch (Exception e) {
            LOG.error("Exception Occurred while sending VendorAdvancesReminder ::: ", e);
        }
    }

    private void generateVendorAdvancesExcel(Map<String, List<VendorAdvancePayment>> vendorAdvances, VendorAdvancesEmailNotification notification) {
        Workbook workbook = new XSSFWorkbook();
        String fileName = "Vendor_Advances" + AppUtils.getCurrentTimeISTStringWithNoColons();
        try {
            int sheetIndex = 0;
            List<String> fieldNames = Arrays.asList("ADVANCE_PAYMENT_ID", "PR_ID", "ADVANCE_TYPE", "VENDOR_ID", "VENDOR_NAME", "AVAILABLE_AMOUNT", "PO_ID", "PENDING_GRS",
                    "SO_ID", "PENDING_SRS", "PENDING_PRS", "CREATED_BY", "CREATED_AT", "REFUND_INITIATED_BY", "REFUND_DATE", "MAX_SETTLEMENT_TIME", "DIFFERENCE_DAYS");
            for (Map.Entry<String, List<VendorAdvancePayment>> entry : vendorAdvances.entrySet()) {
                Sheet sheet = workbook.createSheet();
                workbook.setSheetName(sheetIndex++, entry.getKey());
                int rowCount = 0;
                int columnCount = 0;
                Row row = sheet.createRow(rowCount++);
                for (String fieldName : fieldNames) {
                    Cell cell = row.createCell(columnCount++);
                    cell.setCellValue(fieldName);
                    cell.setCellStyle(requestOrderManagementService.getHeaderStyle((XSSFWorkbook) workbook));
                }
                for (VendorAdvancePayment vendorAdvancePayment : entry.getValue()) {
                    Row rows = sheet.createRow(rowCount++);
                    for (int i = 0; i < columnCount; i++) {
                        Cell cell = rows.createCell(i);
                        if (i == 0) {
                            cell.setCellValue(vendorAdvancePayment.getAdvancePaymentId());
                        } else if (i == 1) {
                            cell.setCellValue(vendorAdvancePayment.getAdvancePaymentId());
                        } else if (i == 2) {
                            cell.setCellValue(vendorAdvancePayment.getAdvanceType());
                        } else if (i == 3) {
                            cell.setCellValue(vendorAdvancePayment.getVendorId());
                        } else if (i == 4) {
                            cell.setCellValue(scmCache.getVendorDetail(vendorAdvancePayment.getVendorId()).getEntityName());
                        } else if (i == 5) {
                            cell.setCellValue(vendorAdvancePayment.getAvailableAmount().doubleValue());
                        } else if (i == 6) {
                            cell.setCellValue(Objects.nonNull(vendorAdvancePayment.getPoId()) ? vendorAdvancePayment.getPoId().toString() : "-");
                        } else if (i == 7) {
                            cell.setCellValue(Objects.nonNull(vendorAdvancePayment.getPendingGrs()) && !vendorAdvancePayment.getPendingGrs().isEmpty()
                                    ? Arrays.toString(vendorAdvancePayment.getPendingGrs().toArray()) : "-");
                        } else if (i == 8) {
                            cell.setCellValue(Objects.nonNull(vendorAdvancePayment.getSoId()) ? vendorAdvancePayment.getSoId().toString() : "-");
                        } else if (i == 9) {
                            cell.setCellValue(Objects.nonNull(vendorAdvancePayment.getPendingSrs()) && !vendorAdvancePayment.getPendingSrs().isEmpty() ? Arrays.toString(vendorAdvancePayment.getPendingSrs().toArray()) : "-");
                        } else if (i == 10) {
                            cell.setCellValue(Objects.nonNull(vendorAdvancePayment.getPendingPrs()) && !vendorAdvancePayment.getPendingPrs().isEmpty() ? Arrays.toString(vendorAdvancePayment.getPendingPrs().toArray()) : "-");
                        } else if (i == 11) {
                            cell.setCellValue(vendorAdvancePayment.getCreatedBy());
                        } else if (i == 12) {
                            cell.setCellValue(AppUtils.getDateString(vendorAdvancePayment.getCreatedAt(), "yyyy-MM-dd:HH:mm:ss"));
                        } else if (i == 13) {
                            cell.setCellValue(Objects.nonNull(vendorAdvancePayment.getRefundInitiatedByName()) ? vendorAdvancePayment.getRefundInitiatedByName() : "-");
                        } else if (i == 14) {
                            cell.setCellValue(Objects.nonNull(vendorAdvancePayment.getRefundDate()) ? vendorAdvancePayment.getRefundDate() : null);
                        } else if (i == 15) {
                            cell.setCellValue(AppUtils.getDateString(vendorAdvancePayment.getMaxSettlementTime(), "yyyy-MM-dd:HH:mm:ss"));
                        } else if (i == 16) {
                            cell.setCellValue(vendorAdvancePayment.getDiffDays());
                        }
                    }
                }
            }

            try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
                workbook.write(bos);
                File fileToUpload = new File(props.getBasePath() + fileName);
                byte[] barray = bos.toByteArray();
                try {
                    LOG.info("Trying to send Email of Vendor Advances");
                    List<AttachmentData> attachments = new ArrayList<>();
                    AttachmentData advances = new AttachmentData(barray, fileName,
                            AppConstants.EXCEL_MIME_TYPE);
                    attachments.add(advances);
                    notification.sendRawMail(attachments);
                    fileToUpload.delete();
                } catch (Exception e) {
                    LOG.info("error sending email ::: ", e);
                }
            } catch (IOException e1) {
                LOG.error("Error Occurred While writing into the Workbook... ::: ", e1);
            }
        } catch (Exception e) {
            LOG.error("error While Generating Excel ::: ", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean validatePrForQuery(Integer prId) throws SumoException {
        try {
            PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class, prId);
            if (Objects.nonNull(paymentRequestData.getVendorPaymentDate())) {
                int daysDiff = AppUtils.getActualDayDifference(AppUtils.getCurrentDate(), paymentRequestData.getVendorPaymentDate());
                if (daysDiff < 0) {
                    throw new SumoException("Can not Query On this PR..!", "Query On this Pr is not allowed as vendor Payment Date is already Exceeded..!");
                }
                return true;
            } else {
                throw new SumoException("Can not Query On this PR..!", "Query On this Pr is not allowed as vendor Payment Date is not available..!");
            }
        } catch (SumoException e) {
            throw e;
        } catch (Exception e) {
            LOG.error("Error Occurred While Sending Pr for Query ::: ", e);
        }
        return false;
    }


    private void updateVendorBlockStatus(VendorDetail vendorDetail) {
        if (Objects.nonNull(vendorDetail.getBlockedReason()) && vendorDetail.getBlockedReason().equalsIgnoreCase("MANUAL")) {
            return;
        }
        if (Objects.nonNull(vendorDetail.getBlockedReason()) || Objects.nonNull(vendorDetail.getVendorBlocked()) || Objects.nonNull(vendorDetail.getUnblockedTillDate())) {
            vendorDetail.setVendorBlocked(null);
            vendorDetail.setBlockedReason(null);
            vendorDetail.setUnblockedTillDate(null);
            VendorDetailData vendorDetailData = paymentRequestManagementDao.find(VendorDetailData.class, vendorDetail.getVendorId());
            vendorDetailData.setVendorBlocked(null);
            vendorDetailData.setVendorBlockedReason(null);
            vendorDetailData.setUnblockedTillDate(null);
            paymentRequestManagementDao.update(vendorDetailData, true);
            scmCache.getVendorDetails().put(vendorDetail.getVendorId(), vendorDetail);
        }
    }

    private PaymentRequest createPrForVendorAdvancePayment(Integer vendorId, BigDecimal prAmount, Integer createdBy, Integer advanceDocId, Integer companyId) throws SumoException {
        PaymentRequest paymentRequest = new PaymentRequest();
        paymentRequest.setType(PaymentRequestType.ADVANCE_PAYMENT);
        paymentRequest.setVendorId(new IdCodeName(vendorId, "", scmCache.getVendorDetail(vendorId).getEntityName()));
        paymentRequest.setCreatedBy(new IdCodeName(createdBy, "", ""));
        paymentRequest.setCompanyId(companyId);
        paymentRequest.setAmountsMatch(true);
        paymentRequest.setDeviationCount(0);
        paymentRequest.setPaidAmount(prAmount);
        paymentRequest.setProposedAmount(prAmount);
        paymentRequest.setPaidAdhoc(false);
        PaymentInvoice paymentInvoice = new PaymentInvoice();
        paymentInvoice.setPaymentAmount(prAmount);
        paymentInvoice.setCalculatedInvoiceAmount(prAmount);
        paymentInvoice.setInvoiceAmount(prAmount);
        paymentInvoice.setInvoiceDocumentHandle(advanceDocId);
        paymentInvoice.setInvoiceDate(AppUtils.getCurrentTimestamp());
        paymentRequest.setPaymentInvoice(paymentInvoice);
        paymentRequest = createPaymentRequest(paymentRequest, null);
        return paymentRequest;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail uploadDocument(FileType type, MimeType mimeType, DocUploadType docType, Integer userId,
                                         MultipartFile file) throws IOException, DocumentException, SumoException {
        String fileName = "INVOICE_" + SCMUtil.getCurrentTimeISTStringWithNoColons() + "."
                + mimeType.name().toLowerCase();
        String baseDir = "PR_INVOICE" + File.separator + SCMUtil.getCurrentYear() + File.separator
                + SCMUtil.getCurrentMonthName() + File.separator + SCMUtil.getCurrentDayofMonth();
        return uploadDocument(type, mimeType, docType, userId, file, fileName, baseDir);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail uploadQueryDocument(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file, String docName) throws DocumentException, IOException {
        String fileName = docName + "_" + SCMUtil.getCurrentTimeISTStringWithNoColons() + "."
                + mimeType.name().toLowerCase();
        String baseDir = "PR_QUERY" + File.separator + SCMUtil.getCurrentYear() + File.separator
                + SCMUtil.getCurrentMonthName() + File.separator + SCMUtil.getCurrentDayofMonth();
        return uploadDocument(type, mimeType, docType, userId, file, fileName, baseDir);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void rejectQueriedPrs() {
        try {
            List<PaymentRequestData> paymentRequestDataList = paymentRequestManagementDao.getQueriedPrsToReject();
            if (Objects.nonNull(paymentRequestDataList) && !paymentRequestDataList.isEmpty()) {
                for (PaymentRequestData paymentRequestData : paymentRequestDataList) {
                    PaymentRequest paymentRequest = viewPaymentRequest(paymentRequestData.getId());
                    paymentRequest.setUpdatedBy(new IdCodeName(AppConstants.SYSTEM_EMPLOYEE_ID, "", AppConstants.SYSTEM_EMPLOYEE_NAME));
                    rejectPaymentRequest(paymentRequest);
                }
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred while trying to reject Queried prs ::: ", e);
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void autoSettleZeroAmountPr(List<PaymentRequest> paymentRequestsAutoSettle, int companyId, Integer updatedBy, String bankName, String bankAccountNumber) throws EmailGenerationException, SumoException {
        List<PaymentRequest> prsToSettle = new ArrayList<>();
        String companyName = null;
        Company company = masterDataCache.getCompany(companyId);
        if (Objects.nonNull(company)) {
            companyName = company.getName();
        }
        for (PaymentRequest request : paymentRequestsAutoSettle) {
            PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class, request.getPaymentRequestId());
            PaymentRequest paymentRequest = new PaymentRequest();
            paymentRequest.setPaymentRequestId(paymentRequestData.getId());
            paymentRequest.setInvoiceNumber(paymentRequestData.getInvoiceNumber());
            paymentRequest.setVendorPaymentDate(paymentRequestData.getVendorPaymentDate());
            PRPaymentDetail prPaymentDetail = new PRPaymentDetail();
            prPaymentDetail.setActualDate(AppUtils.getBusinessDate());
            AdvancePaymentData advancePaymentData = paymentRequestData.getAdvancePaymentData();
            PaymentRequestData advancePr = advancePaymentData.getPaymentRequestId();
            prPaymentDetail.setUtrNumber(advancePr.getPrPaymentDetailData().getUtrNumber());
            prPaymentDetail.setCreatedBy(new IdCodeName(updatedBy, "", masterDataCache.getEmployee(updatedBy)));
            // refreshing vendor Cache
            VendorDetailData vendorDetailData = paymentRequestManagementDao.find(VendorDetailData.class, paymentRequestData.getVendorId());
            List<VendorDetail> vendorDetails = scmMetadataService.convertToVendorDetail(Collections.singletonList(vendorDetailData));
            for (VendorDetail vendorDetail : vendorDetails) {
                scmCache.getVendorDetails().put(vendorDetail.getVendorId(), vendorDetail);
            }
            VendorDetail vendorDetail = scmCache.getVendorDetail(paymentRequestData.getVendorId());
            prPaymentDetail.setBeneficiaryIfscCode(vendorDetail.getAccountDetails().getIfscCode());
            prPaymentDetail.setBeneficiaryAccountNumber(vendorDetail.getAccountDetails().getAccountNumber());
            prPaymentDetail.setVendorName(SCMUtil.filterSpecialCharacters(vendorDetail.getEntityName()));
            prPaymentDetail.setPaidAmount(paymentRequestData.getPaidAmount());
            prPaymentDetail.setProposedAmount(paymentRequestData.getProposedAmount());
            prPaymentDetail.setDebitBank(bankName);
            prPaymentDetail.setDebitAccount(bankAccountNumber);
            if ((PaymentBanks.KOTAK.name()).equals(bankName) || (PaymentBanks.KOTAK_V2.name()).equals(bankName) || PaymentBanks.HDFC.name().equals(bankName) || PaymentBanks.ICICI.name().equals(bankName)) {
                if (PaymentBanks.HDFC.name().equals(bankName)) {
                    String type = vendorDetail.getAccountDetails().getIfscCode().startsWith("HDFC") ? "I" : "N";
                    prPaymentDetail.setPaymentType(PaymentType.fromValue(type.equalsIgnoreCase("N") ?
                            "NEFT" : "I"));
                } else if (PaymentBanks.ICICI.name().equals(bankName)) {
                    String type = vendorDetail.getAccountDetails().getIfscCode().startsWith("ICIC") ?
                            "I" : AppUtils.roundToInteger(paymentRequestData.getPaidAmount()) <= 200000 ? "N" : "R";
                    prPaymentDetail.setPaymentType(PaymentType.fromValue(type.equalsIgnoreCase("N") ?
                            "NEFT" : type.equalsIgnoreCase("R") ? "RTGS" : "I"));
                } else {
                    prPaymentDetail.setPaymentType(
                            vendorDetail.getAccountDetails().getIfscCode().startsWith("KKBK") ?
                                    PaymentType.IFT : PaymentType.NEFT);
                }
            }
            paymentRequest.setPaymentDetail(prPaymentDetail);
            prsToSettle.add(paymentRequest);
        }
        settlePaymentRequestBulk(prsToSettle, companyName, true, SCMUtil.getCreatedBy(masterDataCache.getEmployee(updatedBy), updatedBy));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail uploadAdvanceRefund(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file, String docName, Integer advancePaymentId) throws DocumentException, IOException {
        String fileName = docName + "_" + advancePaymentId + "_" + SCMUtil.getCurrentTimeISTStringWithNoColons() + "."
                + mimeType.name().toLowerCase();
        String baseDir = "ADVANCE_REFUND" + File.separator + SCMUtil.getCurrentYear() + File.separator
                + SCMUtil.getCurrentMonthName() + File.separator + SCMUtil.getCurrentDayofMonth();
        return uploadDocument(type, mimeType, docType, userId, file, fileName, baseDir);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<String> getEmployeePaymentCards(Integer userId) {
        List<String> result = new ArrayList<>();
        try {
            result = paymentRequestManagementDao.getEmployeePaymentCards(userId);
        } catch (Exception e) {
            LOG.error("Exception Occurred While getting the Employee Payment Card ::: ", e);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail uploadCardPaymentProof(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file, String docName) throws DocumentException, IOException {
        String fileName = docName + "_" + SCMUtil.getCurrentTimeISTStringWithNoColons() + "."
                + mimeType.name().toLowerCase();
        String baseDir = "CARD_PAYMENT_PROOF" + File.separator + SCMUtil.getCurrentYear() + File.separator
                + SCMUtil.getCurrentMonthName() + File.separator + SCMUtil.getCurrentDayofMonth();
        return uploadDocument(type, mimeType, docType, userId, file, fileName, baseDir);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail uploadDocument(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file, String fileName, String baseDir) throws IOException, DocumentException {
        String dest = props.getBasePath() + file.getOriginalFilename();
        File destFile = new File(dest);
        MultipartFile compressedFile = null;
        if (mimeType.equals(MimeType.PDF)) {
            LOG.info("#########Uploaded FIle is PDF , Trying To Compress ###### ");
            //Compression of PDF
            try {
                compressedFile = compressPdf(destFile, file, compressedFile);
                LOG.info("###### size before compression : {}", file.getSize());
                LOG.info("###### Size After Compression : {}", compressedFile.getSize());
            } catch (SumoException e) {
                LOG.info("######Error While Compressing File , Uploading Without Compression");
                compressedFile = null;
            }
        } else if (mimeType.equals(MimeType.PNG)) {
            LOG.info("#######Uploaded File Is Of PNG Format. Uploading Without Compression .");
        } else {
            LOG.info("#######Uploaded File is Of {} Format, Trying To Compress", mimeType.extension());
            try {
                byte[] imageByte = compressImage(file, mimeType.extension());
                compressedFile = new MockMultipartFile(file.getName(),
                        file.getOriginalFilename(), file.getContentType(), imageByte);
                LOG.info("###### size before compression : {}", file.getSize());
                LOG.info("###### Size After Compression : {}", compressedFile.getSize());
            } catch (Exception e) {
                LOG.info("#######Error While Compressing Image , Uploading Without Compression");
                compressedFile = null;
            }
        }


        try {
            FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), baseDir, fileName, Objects.isNull(compressedFile) ? file : compressedFile);
            DocumentDetail documentDetail = new DocumentDetail();
            documentDetail.setMimeType(mimeType);
            documentDetail.setUploadType(docType);
            documentDetail.setFileType(type);
            documentDetail.setDocumentLink(fileName);
            documentDetail.setS3Key(s3File.getKey());
            documentDetail.setFileUrl(s3File.getUrl());
            documentDetail.setS3Bucket(s3File.getBucket());
            documentDetail.setUpdatedBy(SCMUtil.generateIdCodeName(userId, "", masterDataCache.getEmployee(userId)));
            documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
            DocumentDetailData data = paymentRequestManagementDao.add(SCMDataConverter.convert(documentDetail), true);

            if (data.getDocumentId() != null) {
                return SCMDataConverter.convert(data);
            }

        } catch (Exception e) {
            LOG.error("Encountered error while uploading document", e);
        } finally {
            Boolean res = destFile.delete();
            LOG.info("dest pdf file deleted : {}", res);
        }
        return null;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail uploadDebitNoteDocument(FileType type, MimeType mimeType, DocUploadType docType, Integer userId,
                                                  MultipartFile file) throws IOException, DocumentException, SumoException {
        String fileName = "DEBIT_NOTE_" + SCMUtil.getCurrentTimeISTStringWithNoColons() + "."
                + mimeType.name().toLowerCase();
        String baseDir = "DEBIT_NOTE_" + File.separator + SCMUtil.getCurrentYear() + File.separator
                + SCMUtil.getCurrentMonthName() + File.separator + SCMUtil.getCurrentDayofMonth();
        File destFile = null;
        MultipartFile compressedFile = null;
        if (mimeType.equals(MimeType.PDF)) {
            LOG.info("#########Uploaded FIle is PDF , Trying To Compress ###### ");
            //Compression of PDF
            try {
                compressedFile = compressPdf(destFile, file, compressedFile);
                LOG.info("###### size before compression : {}", file.getSize());
                LOG.info("###### Size After Compression : {}", compressedFile.getSize());
                if (compressedFile.getSize() > 2048000) {
                    throw new SumoException("Compression Error", "We are unable to compress the file uploaded. Try Compressing externally and re-upload. Ex: <a>https://smallpdf.com/compress-pdf </a>");
                }
            } catch (SumoException e) {
                throw new SumoException(e.getCode().getErrorTitle(), e.getCode().getErrorMsg());
            }
        } else if (mimeType.equals(MimeType.PNG)) {
            if (file.getSize() > 1024000) {
                throw new SumoException("Size Error", "PNG Image Can't be greater Than 1 MB . for Larger Images Upload JPG/Jpeg.");
            }
            LOG.info("#######Uploaded File Is Of PNG Format. Uploading Without Compression .");
        } else {
            LOG.info("#######Uploaded File is Of {} Format, Trying To Compress", mimeType.extension());
            byte[] imageByte = compressImage(file, mimeType.extension());
            compressedFile = new MockMultipartFile(file.getName(),
                    file.getOriginalFilename(), file.getContentType(), imageByte);
            LOG.info("###### size before compression : {}", file.getSize());
            LOG.info("###### Size After Compression : {}", compressedFile.getSize());
        }


        try {
            FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), baseDir, fileName, Objects.isNull(compressedFile) ? file : compressedFile);
            DocumentDetail documentDetail = new DocumentDetail();
            documentDetail.setMimeType(mimeType);
            documentDetail.setUploadType(docType);
            documentDetail.setFileType(type);
            documentDetail.setDocumentLink(fileName);
            documentDetail.setS3Key(s3File.getKey());
            documentDetail.setFileUrl(s3File.getUrl());
            documentDetail.setS3Bucket(s3File.getBucket());
            documentDetail.setUpdatedBy(SCMUtil.generateIdCodeName(userId, "", masterDataCache.getEmployee(userId)));
            documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
            DocumentDetailData data = paymentRequestManagementDao.add(SCMDataConverter.convert(documentDetail), true);
            if (Objects.nonNull(destFile)) {
                destFile.delete();
            }
            if (data.getDocumentId() != null) {
                return SCMDataConverter.convert(data);
            }

        } catch (Exception e) {
            LOG.error("Encountered error while uploading document", e);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public DocumentDetail getDocument(Integer docId) throws SumoException {
        if (docId != null) {
            DocumentDetailData documentDetailData = paymentRequestManagementDao.find(DocumentDetailData.class, docId);
            if (documentDetailData != null) {
                return SCMDataConverter.convert(documentDetailData);
            } else {
                throw new SumoException("Document Id is not valid.");
            }
        } else {
            throw new SumoException("Document id not provided.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Pair<String, String>> getMandatoryReqDoc(Integer prId) throws SumoException {
        return paymentRequestManagementDao.getMandatoryReqDoc(prId);

    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PaymentRequest> getPaymentRequests(Integer vendorId, Integer unitId, Integer prId, Integer grId, Integer srId,
                                                   String invoiceNumber, Date startDate, Date endDate, String status, Date paymentDate, Integer companyId, String requestType)
            throws SumoException {
        if (grId != null) {
            VendorGoodsReceivedData vendorGoodsReceivedData = goodsReceiveManagementDao.find(VendorGoodsReceivedData.class, grId);
            if (vendorGoodsReceivedData != null) {
                return convertToPaymentRequestList(Arrays.asList(paymentRequestManagementDao.find(PaymentRequestData.class, vendorGoodsReceivedData.getPaymentRequestData().getId())));
            } else {
                LOG.info("goods receive not exist for id: " + grId);
                return new ArrayList<>();
            }
        } else if (srId != null) {
            ServiceReceivedData serviceReceivedData = serviceReceiveManagementDao.find(ServiceReceivedData.class, srId);
            if (serviceReceivedData != null) {
                return convertToPaymentRequestList((paymentRequestManagementDao.getSRPaymentRequests(vendorId,
                        unitId, prId, grId, srId, invoiceNumber, startDate, endDate, status, companyId, paymentDate, requestType)));
            } else {
                LOG.info("service receive does not exist for id: " + srId);
                return new ArrayList<>();
            }
        } else {
            status = (status != null && !status.trim().isEmpty()) ? status : null;
            invoiceNumber = (invoiceNumber != null && !invoiceNumber.trim().isEmpty()) ? invoiceNumber : null;
            List<PaymentRequestData> paymentRequestDataList = paymentRequestManagementDao.getPaymentRequests(vendorId,
                    unitId, prId, grId, invoiceNumber, startDate, endDate, status, companyId, paymentDate, requestType);
            return convertToPaymentRequestList(paymentRequestDataList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PaymentRequest> getCompanyPaymentRequests(Integer companyId, Integer vendorId, Integer unitId,
                                                          Integer prId, Integer grId, String invoiceNumber, Date startDate, Date endDate, String status,
                                                          Date paymentDate, String type) throws SumoException {
        status = (status != null && !status.trim().isEmpty()) ? status : null;
        invoiceNumber = (invoiceNumber != null && !invoiceNumber.trim().isEmpty()) ? invoiceNumber : null;
        List<PaymentRequestData> paymentRequestDataList = paymentRequestManagementDao.getPaymentRequests(vendorId,
                unitId, prId, grId, invoiceNumber, startDate, endDate, status, companyId, paymentDate, type);
        return convertToPaymentRequestList(paymentRequestDataList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PaymentRequest> getCompanyPaymentRequestsForProcess(PaymentRequestType type, Integer companyId, Integer vendorId, Integer unitId,
                                                                    Integer prId, String invoiceNumber, Date startDate, Date endDate, String status, String dateType)
            throws SumoException {
        invoiceNumber = (invoiceNumber != null && !invoiceNumber.trim().isEmpty()) ? invoiceNumber : null;
        List<String> statusList = new ArrayList<>();
        Arrays.stream(PaymentRequestStatus.values()).forEach(paymentRequestStatus -> {
            if (paymentRequestStatus == PaymentRequestStatus.CREATED
                    || paymentRequestStatus == PaymentRequestStatus.ACKNOWLEDGED
                    || paymentRequestStatus == PaymentRequestStatus.APPROVED
                    || paymentRequestStatus == PaymentRequestStatus.SENT_FOR_PAYMENT
                    || paymentRequestStatus == PaymentRequestStatus.PAID) {
                statusList.add(paymentRequestStatus.value());
            }
        });
        List<PaymentRequestData> paymentRequestDataList = new ArrayList<>();
        if (dateType.equals("GR Creation Date")) {
            paymentRequestDataList = paymentRequestManagementDao.getGRPaymentRequestsForProcess(type,
                    vendorId, unitId, prId, invoiceNumber, startDate, endDate, companyId, dateType, statusList);
        } else {
            paymentRequestDataList = paymentRequestManagementDao.getPaymentRequestsForProcess(type,
                    vendorId, unitId, prId, invoiceNumber, startDate, endDate, companyId, dateType, statusList);
        }
        List<PaymentRequest> paymentRequests = convertToPaymentRequestList(paymentRequestDataList);
        if (status != null && status.trim().length() > 0) {
            paymentRequests = paymentRequests.stream()
                    .filter(paymentRequest -> paymentRequest.getCurrentStatus().value().equals(status))
                    .collect(Collectors.toList());
        }
        for (PaymentRequest paymentRequest : paymentRequests) {
            if (type.equals(PaymentRequestType.SERVICE_RECEIVED)) {
                List<String> serviceReceivedItemDataList = serviceReceiveManagementDao.findSrByPrIds(paymentRequest);
                paymentRequest.setBusinessCostDetailData(new ArrayList<>(new HashSet<>(serviceReceivedItemDataList)).stream().map(Object::toString)
                        .collect(Collectors.joining(", ")));
            } else {
                if (Objects.nonNull(paymentRequest.getRequestingUnit())) {
                    paymentRequest.setBusinessCostDetailData(paymentRequest.getRequestingUnit().getName());
                }
            }
        }

        return paymentRequests;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public PaymentRequest viewPaymentRequest(Integer paymentRequestId) throws SumoException {
        if (paymentRequestId != null) {
            PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class, paymentRequestId);
            PaymentInvoiceData paymentInvoiceData = paymentRequestManagementDao
                    .findInvoiceByPaymentRequestId(paymentRequestId);
            String vendor = scmCache.getVendorDetail(paymentRequestData.getVendorId()).getEntityName();
            String blockedBy = "";
            if (paymentRequestData.getBlockedBy() != null) {
                blockedBy = masterDataCache.getEmployee(paymentRequestData.getBlockedBy());
            }
            String createdBy = masterDataCache.getEmployee(paymentRequestData.getCreatedBy());
            String requestingUnit = null;
            if (paymentRequestData.getRequestingUnit() != null) {
                requestingUnit = scmCache.getUnitDetail(paymentRequestData.getRequestingUnit()).getUnitName();
            }
            VendorDetailData vdd = paymentRequestManagementDao.find(VendorDetailData.class,
                    paymentRequestData.getVendorId());
            BigDecimal debitBalance = getDebitBalance(vdd);
            List<VendorDebitBalanceVO> vendorDebitBalanceVOS = getDebitBalanceVOs(vdd);
            String detailBy = null;
            if (paymentRequestData.getPrPaymentDetailData() != null) {
                detailBy = masterDataCache.getEmployee(paymentRequestData.getPrPaymentDetailData().getCreatedBy());
            }
            List<CostElementData> costElementData = new ArrayList<CostElementData>();
            if (paymentRequestData.getType().equalsIgnoreCase("SERVICE_RECEIVED")) {
                costElementData = paymentRequestManagementDao.findAll(CostElementData.class);
            }
            PaymentRequest paymentRequest = SCMDataConverter.convert(paymentRequestData, paymentInvoiceData, blockedBy,
                    createdBy, requestingUnit, vendor, null, detailBy, debitBalance,
                    vdd.getCompanyDetails().getCreditDays(), vendorDebitBalanceVOS, scmCache, costElementData);

            BigDecimal tdsAmount = BigDecimal.ZERO;
            if (paymentRequest.getType().equals(PaymentRequestType.SERVICE_RECEIVED) && paymentRequest.getPaymentDetail() != null) {
                tdsAmount = getTdsAmount(paymentInvoiceData);
                paymentRequest.getPaymentDetail().setTdsAmount(tdsAmount);
            }
            if (vdd.getTds() != null) {
                paymentRequest.setTds(vdd.getTds().equals(SCMServiceConstants.SCM_CONSTANT_YES));
            }
            if (vdd.getCompanyDetails() != null) {
                paymentRequest.setPan(vdd.getCompanyDetails().getPAN());
                paymentRequest.setPanStatus(vdd.getCompanyDetails().getPanStatus());
                paymentRequest.setPanStatusUpdatedAt(vdd.getCompanyDetails().getPanStatusUpdatedAt());

            }
            paymentRequest.setPaymentState(paymentRequestData.getStateName());
            if (vdd.getVendorAddress() != null) {
                paymentRequest.setVendorState(vdd.getVendorAddress().getState());
            }
            if (paymentRequestData.getType().equalsIgnoreCase("SERVICE_RECEIVED")) {
                List<PaymentInvoiceItem> paymentInvoiceItems = paymentRequest.getPaymentInvoice()
                        .getPaymentInvoiceItems();
                for (PaymentInvoiceItem item : paymentInvoiceItems) {
                    if (item.getServiceReceivedItemId() != null) {
                        ServiceReceivedItemData srItem = paymentRequestManagementDao.find(ServiceReceivedItemData.class,
                                item.getServiceReceivedItemId());
                        if (srItem != null) {
                            item.setBusinessCostCenterId(srItem.getBusinessCostCenterId());
                            item.setBusinessCostCenterName(srItem.getBusinessCostCenterName());
                            if (Objects.nonNull(srItem.getBusinessCostCenterId())) {
                                BusinessCostCenterData businessCostCenterData = paymentRequestManagementDao.find(BusinessCostCenterData.class, srItem.getBusinessCostCenterId());
                                item.setBccCode(businessCostCenterData.getCode());
                            }
                        }
                    }
                }
            }
            paymentRequest.setVendorCreditPeriod(vdd.getCompanyDetails().getCreditDays());
            List<PaymentRequestStatusLogData> paymentRequestStatusLogDataList = paymentRequestManagementDao
                    .findStatusChangeLogsByPaymentRequestId(paymentRequestId);
            paymentRequestStatusLogDataList.stream().forEach(paymentRequestStatusLogData -> {
                IdCodeName updatedBy = SCMUtil.generateIdCodeName(paymentRequestStatusLogData.getUpdatedBy(), "",
                        masterDataCache.getEmployee(paymentRequestStatusLogData.getUpdatedBy()));
                paymentRequest.getStatusLogs().add(SCMDataConverter.convert(paymentRequestStatusLogData, updatedBy));
            });
            List<PaymentRequestLogData> paymentRequestLogDataList = paymentRequestManagementDao
                    .findPRLogsByPaymentRequest(paymentRequestId);
            paymentRequestLogDataList.stream().forEach(paymentRequestLogData -> {
                paymentRequest.getRequestLogs().add(SCMDataConverter.convert(paymentRequestLogData));
            });
            List<PaymentRequestItemMappingData> paymentRequestItemMappingDataList = paymentRequestManagementDao
                    .findMappedItemsByPaymenrRequestId(paymentRequestId);
            paymentRequestItemMappingDataList.stream().forEach(paymentRequestItemMappingData -> {
                String grStatus = getGrStatus(paymentRequestData, paymentRequestItemMappingData);
                paymentRequest.getRequestItemMappings()
                        .add(SCMDataConverter.convert(paymentRequestItemMappingData, grStatus));
            });
            List<InvoiceDeviationMappingData> invoiceDeviationMappingDataList = paymentRequestManagementDao
                    .findDeviations(paymentInvoiceData.getId(), PaymentDeviationLevel.INVOICE.value());
            paymentInvoiceData.getPaymentInvoiceItemData().stream().forEach(paymentInvoiceItemData -> {
                invoiceDeviationMappingDataList.addAll(paymentRequestManagementDao
                        .findDeviations(paymentInvoiceItemData.getId(), PaymentDeviationLevel.INVOICE_ITEM.value()));
            });
            invoiceDeviationMappingDataList.stream().forEach(invoiceDeviationMappingData -> {
                String createdByx = masterDataCache.getEmployee(invoiceDeviationMappingData.getCreatedBy());
                String acceptedBy = invoiceDeviationMappingData.getAcceptedBy() != null
                        ? masterDataCache.getEmployee(invoiceDeviationMappingData.getAcceptedBy())
                        : "";
                String rejectedBy = invoiceDeviationMappingData.getRejectedBy() != null
                        ? masterDataCache.getEmployee(invoiceDeviationMappingData.getCreatedBy())
                        : "";
                String removedBy = invoiceDeviationMappingData.getRemovedBy() != null
                        ? masterDataCache.getEmployee(invoiceDeviationMappingData.getCreatedBy())
                        : "";
                if (PaymentDeviationType.REJECTION.value()
                        .equals(invoiceDeviationMappingData.getPaymentDeviationData().getDeviationType())) {
                    paymentRequest.getPaymentInvoice().getRejections().add(SCMDataConverter
                            .convert(invoiceDeviationMappingData, createdByx, acceptedBy, rejectedBy, removedBy));
                } else {
                    if (PaymentDeviationLevel.INVOICE.value()
                            .equals(invoiceDeviationMappingData.getDeviationItemType())) {
                        paymentRequest.getPaymentInvoice().getDeviations().add(SCMDataConverter
                                .convert(invoiceDeviationMappingData, createdByx, acceptedBy, rejectedBy, removedBy));
                    }
                    if (PaymentDeviationLevel.INVOICE_ITEM.value()
                            .equals(invoiceDeviationMappingData.getDeviationItemType())) {
                        paymentRequest.getPaymentInvoice().getPaymentInvoiceItems().stream()
                                .forEach(paymentInvoiceItem -> {
                                    if (paymentInvoiceItem.getPaymentInvoiceItemId()
                                            .equals(invoiceDeviationMappingData.getDeviationItemId())) {
                                        paymentInvoiceItem.getDeviations()
                                                .add(SCMDataConverter.convert(invoiceDeviationMappingData, createdByx,
                                                        acceptedBy, rejectedBy, removedBy));
                                    }
                                });
                    }
                }
            });
            DebitNoteDetailData debitNoteDetailData = paymentRequestManagementDao
                    .findDebitNoteByPaymentRequestId(paymentRequestId);
            if (debitNoteDetailData != null) {
                paymentRequest.setDebitNote(SCMDataConverter.convert(debitNoteDetailData,
                        masterDataCache.getEmployee(debitNoteDetailData.getGeneratedBy()),
                        masterDataCache.getEmployee(debitNoteDetailData.getLastUpdatedBy())));
                if (debitNoteDetailData.getDebitNoteDocId() != null) {
                    DocumentDetailData documentDetailData = paymentRequestManagementDao.find(DocumentDetailData.class, debitNoteDetailData.getDebitNoteDocId());
                    if (documentDetailData != null) {
                        paymentRequest.setDebitNoteDocumentDetail(SCMDataConverter.convert(documentDetailData));
                    }
                }
            }
            if (Objects.nonNull(paymentRequestData.getExtraChargesType())) {
                paymentRequest.setExtraChargesType(paymentRequestData.getExtraChargesType());
            }
            if (Objects.nonNull(paymentRequestData.getAdvanceAmount()) && !paymentRequestData.getType().equalsIgnoreCase("ADVANCE_PAYMENT")) {
                List<VendorAdvancePayment> vendorAdvancePayments = new ArrayList<>();
                if (paymentRequestData.getType().equalsIgnoreCase("SERVICE_RECEIVED")) {
                    ServiceOrderData serviceOrderData = paymentRequestData.getAdvancePaymentData().getServiceOrderData();
                    if (Objects.nonNull(serviceOrderData.getAdvancePaymentDatas()) && !serviceOrderData.getAdvancePaymentDatas().isEmpty()) {
                        for (AdvancePaymentData advancePaymentData : serviceOrderData.getAdvancePaymentDatas()) {
                            if (!advancePaymentData.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.CANCELLED.value())) {
                                VendorAdvancePayment vendorAdvancePayment = VendorAdvancePayment.builder().vendorId(serviceOrderData.getVendorId())
                                        .advanceStatus("ALL").advanceType(SCMServiceConstants.SO_ADVANCE).poId(serviceOrderData.getId()).build();
                                VendorAdvancePayment advance = null;
                                try {
                                    advance = getVendorAdvancePayment(vendorAdvancePayment, true, advancePaymentData);
                                    vendorAdvancePayments.add(advance);
                                } catch (Exception e) {
                                    LOG.info("Exception during Vendor Advance ..!", e);
                                }
                            }
                        }
                    }
                }
                if (paymentRequestData.getType().equalsIgnoreCase("GOODS_RECEIVED")) {
                    PurchaseOrderData purchaseOrderData = paymentRequestData.getAdvancePaymentData().getPurchaseOrderData();
                    if (Objects.nonNull(purchaseOrderData.getAdvancePaymentDatas()) && !purchaseOrderData.getAdvancePaymentDatas().isEmpty()) {
                        for (AdvancePaymentData advancePaymentData : purchaseOrderData.getAdvancePaymentDatas()) {
                            if (!advancePaymentData.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.CANCELLED.value())) {
                                VendorAdvancePayment vendorAdvancePayment = VendorAdvancePayment.builder().vendorId(purchaseOrderData.getGeneratedForVendor())
                                        .advanceStatus("ALL").advanceType(SCMServiceConstants.PO_ADVANCE).poId(purchaseOrderData.getId()).build();
                                VendorAdvancePayment advance = null;
                                try {
                                    advance = getVendorAdvancePayment(vendorAdvancePayment, true, advancePaymentData);
                                    vendorAdvancePayments.add(advance);
                                } catch (Exception e) {
                                    LOG.info("Exception during Vendor Advance ..!", e);
                                }
                            }
                        }
                    }
                }
                if (!vendorAdvancePayments.isEmpty()) {
                    paymentRequest.setVendorAdvancePayments(vendorAdvancePayments);
                }
                paymentRequest.setAdvanceAmount(paymentRequestData.getAdvanceAmount());
            }
            if (Objects.nonNull(paymentRequestData.getPaymentRequestQueryData()) && !paymentRequestData.getPaymentRequestQueryData().isEmpty()) {
                List<PaymentRequestQuery> paymentRequestQueries = new ArrayList<>();
                paymentRequestData.getPaymentRequestQueryData().forEach(e -> {
                    PaymentRequestQuery paymentRequestQuery = DomainDataMapper.INSTANCE.toPaymentRequestQuery(e);
                    paymentRequestQuery.setQueryRaisedByName(SCMUtil.getCreatedBy(masterDataCache.getEmployee(e.getQueryRaisedBy()), e.getQueryRaisedBy()));
                    if (Objects.nonNull(paymentRequestQuery.getUploadedDocumentId())) {
                        try {
                            DocumentDetail document = getDocument(paymentRequestQuery.getUploadedDocumentId());
                            paymentRequestQuery.setDocumentDetail(document);
                        } catch (Exception ex) {
                            LOG.error("Exception Occurred While setting Document Detail Data :: ", ex);
                        }
                    }
                    if (Objects.nonNull(paymentRequestQuery.getQueryResolvedBy())) {
                        paymentRequestQuery.setQueryResolvedByName(SCMUtil.getCreatedBy(masterDataCache.getEmployee(e.getQueryResolvedBy()), e.getQueryResolvedBy()));
                    }
                    paymentRequestQueries.add(paymentRequestQuery);
                });
                paymentRequest.setPaymentRequestQueries(paymentRequestQueries);
                if (Objects.nonNull(paymentRequestData.getLastQueriedDate())) {
                    paymentRequest.setLastQueriedDate(paymentRequestData.getLastQueriedDate());
                }
                if (Objects.nonNull(paymentRequestData.getLastQueryResolvedDate())) {
                    paymentRequest.setLastQueryResolvedDate(paymentRequestData.getLastQueryResolvedDate());
                }
            }
            if (Objects.nonNull(paymentRequest.getVendorId()) && Objects.nonNull(paymentRequest.getVendorId().getId())) {
                VendorDetail vendorDetail = scmCache.getVendorDetail(paymentRequest.getVendorId().getId());
                if (Objects.nonNull(vendorDetail)) {
                    paymentRequest.setIsPrCCVendor(vendorDetail.getIsCCVendor());
                    paymentRequest.setIsPrEcomParty(vendorDetail.getIsEcomParty());
                }
                if (Objects.nonNull(vendorDetail) && Objects.nonNull(vendorDetail.getAccountDetails()) && Objects.nonNull(vendorDetail.getAccountDetails().getSection206())) {
                    paymentRequest.setSection206(vendorDetail.getAccountDetails().getSection206());
                    paymentRequest.setSection206UpdatedAt(vendorDetail.getAccountDetails().getSection206UpdatedAt());
                }
            }
            paymentRequest.setAllPrProcessMetaData(getPrProcessMetaData());
            paymentRequest.setPaymentRequestMetaData(SCMDataConverter.convert(
                            paymentRequestManagementDao.getPaymentRequestMetaDataForPr(paymentRequestData)
                    ));


            List<LdcVendorDomain> applicableLdc = ldcVendorServiceDecorator.getValidLdcData(
                    getBasicInvoiceAmount(paymentRequest.getPaymentInvoice()).doubleValue(),
                    SCMUtil.getDate(SCMUtil.getCurrentTimestamp()), paymentRequest.getVendorId().getId());
            paymentRequest.setApplicableLdc(applicableLdc);

            if(paymentRequest.getType().equals(PaymentRequestType.SERVICE_RECEIVED)){
                String res =   serviceReceiveManagementService.getPrBudgetType(paymentRequest.getPaymentRequestId());
                if(res != null && res.equalsIgnoreCase("CAPEX")){
                  Map<Integer,CostElementData> costElementDataMap = costElementData.stream().collect(Collectors.toMap(CostElementData::getCostElementId,Function.identity()));
                   for(PaymentInvoiceItem pi : paymentRequest.getPaymentInvoice().getPaymentInvoiceItems()){
                       CostElementData cld = costElementDataMap.get(pi.getSkuId());
                       if(cld.getCapexSubCategory()!=null){
                           pi.setBudgetCategory(cld.getCapexSubCategory().getBudgetCategory());
                           pi.setSection(cld.getCapexSubCategory().getSection());
                       }
                       if(cld.getCapexCategory()!=null){
                           pi.setCategory(cld.getCapexCategory().getName());
                       }
                   }
                }
            }

          ServiceOrderData so =  serviceReceiveManagementDao.getSoFromPR(paymentRequest.getPaymentRequestId());
    if(so!=null){
         for(ServiceOrderItemData soi : so.getServiceOrderItemDataList()){
             if(!Objects.equals(soi.getRequestedQuantity(),soi.getReceivedQuantity())){
             paymentRequest.setPartialSr(true);
             break;
         }
     }
    }


            return paymentRequest;
        } else {
            throw new SumoException("Payment request id is required.");
        }
    }
    private BigDecimal getBasicInvoiceAmount(PaymentInvoice paymentInvoice){
        BigDecimal basicInvoiceAmount = BigDecimal.valueOf(0);
        for (PaymentInvoiceItem item : paymentInvoice.getPaymentInvoiceItems()) {
            BigDecimal sum = item.getUnitPrice().multiply(item.getQuantity());
            basicInvoiceAmount = basicInvoiceAmount.add(sum);
        }
        return basicInvoiceAmount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<PaymentRequestStatusChangeVO> changePaymentRequestStatusInBulk(List<PaymentRequestStatusChangeVO> changeRequestList)
            throws SumoException, EmailGenerationException {
        List<PaymentRequestStatusChangeVO> updatedList = new ArrayList<>();
        for (PaymentRequestStatusChangeVO paymentRequestStatusChangeVO : changeRequestList) {
            updatedList.add(changePaymentRequestStatus(paymentRequestStatusChangeVO));
        }
        return updatedList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public PaymentRequestStatusChangeVO changePaymentRequestStatus(PaymentRequestStatusChangeVO changeRequest)
            throws SumoException, EmailGenerationException {
        if (changeRequest.getPaymentRequestId() != null) {
            PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class,
                    changeRequest.getPaymentRequestId());
            if (paymentRequestData != null) {
                if (changeRequest.getPaymentType() != null) {
                    if (changeRequest.getPaymentType().equalsIgnoreCase(PaymentRequestType.SERVICE_RECEIVED.toString())
                            && (changeRequest.getNewStatus().equals(PaymentRequestStatus.CANCELLED)
                            || changeRequest.getNewStatus().equals(PaymentRequestStatus.HOD_REJECTED))) {
                        updatePaymentQuantity(paymentRequestData);
                    }
                }
                changeRequest.setCurrentStatus(PaymentRequestStatus.fromValue(paymentRequestData.getCurrentStatus()));
                if (validStatusChange(changeRequest)) {
                    paymentRequestData.setCurrentStatus(changeRequest.getNewStatus().value());
                    paymentRequestData.setLastUpdated(SCMUtil.getCurrentTimestamp());
                    paymentRequestData = paymentRequestManagementDao.update(paymentRequestData, true);
                    if (paymentRequestData != null) {
                        if (PaymentRequestStatus.CANCELLED.equals(changeRequest.getNewStatus()) || PaymentRequestStatus.HOD_REJECTED.equals(changeRequest.getNewStatus())) {
                            revertLinkedGRData(paymentRequestData);
                            revertLinkedSRData(paymentRequestData);
                            revertAdvancePayment(paymentRequestData, changeRequest.getUpdatedBy(), changeRequest.getNewStatus().value());
                            if (paymentRequestData.getType().equals(PaymentRequestType.MILK_BAKERY.value())) {
                                revertMilkInvoice(paymentRequestData.getId());
                            }
                        }
                        if (PaymentRequestStatus.QUERIED.equals(changeRequest.getNewStatus())) {
                            if (!paymentRequestData.getPaymentRequestQueryData().isEmpty()) {
                                for (PaymentRequestQueryData e : paymentRequestData.getPaymentRequestQueryData()) {
                                    e.setQueriedForPrId(paymentRequestData.getId());
                                    e.setPrId(null);
                                    paymentRequestManagementDao.update(e, true);
                                }
                                changePrToQueried(paymentRequestData, changeRequest);
                            } else {
                                changePrToQueried(paymentRequestData, changeRequest);
                            }
                            paymentRequestData.setLastQueriedDate(AppUtils.getCurrentTimestamp());
                            paymentRequestManagementDao.update(paymentRequestData, true);
                            sendQueriedEmailNotification(paymentRequestData, changeRequest, true);
                        }
                        if (changeRequest.getCurrentStatus().equals(PaymentRequestStatus.QUERIED) && paymentRequestData.getCurrentStatus().equalsIgnoreCase(PaymentRequestStatus.CREATED.value())) {
                            if (!paymentRequestData.getPaymentRequestQueryData().isEmpty()) {
                                for (PaymentRequestQueryData e : paymentRequestData.getPaymentRequestQueryData()) {
                                    for (PaymentRequestQuery paymentRequestQuery : changeRequest.getPaymentRequestQueries()) {
                                        if (e.getPrQueryId().equals(paymentRequestQuery.getPrQueryId())) {
                                            if (Objects.nonNull(paymentRequestQuery.getUploadedDocumentId())) {
                                                e.setUploadedDocumentId(paymentRequestQuery.getUploadedDocumentId());
                                            }
                                            if (Objects.nonNull(paymentRequestQuery.getResolvedByComment())) {
                                                e.setResolvedByComment(paymentRequestQuery.getResolvedByComment());
                                            }
                                            e.setQueryResolvedBy(changeRequest.getUpdatedBy());
                                            paymentRequestManagementDao.update(e, true);
                                            break;
                                        }
                                    }
                                }
                            }
                            paymentRequestData.setLastQueryResolvedDate(AppUtils.getCurrentTimestamp());
                            paymentRequestManagementDao.update(paymentRequestData, true);
                            sendQueriedEmailNotification(paymentRequestData, changeRequest, false);
                        }
                        String logData = "Payment request id: " + paymentRequestData.getId() + " status changed from: "
                                + changeRequest.getCurrentStatus().value() + " to: " + changeRequest.getNewStatus() + " by: "
                                + masterDataCache.getEmployee(changeRequest.getUpdatedBy()) + "["
                                + changeRequest.getUpdatedBy() + "].";
                        logPaymentRequestData(logData, paymentRequestData.getId());
                        if (paymentRequestData.getCurrentStatus().equals(PaymentRequestStatus.ACKNOWLEDGED.value())
                                && changeRequest.getPaymentRequest() != null && changeRequest.getPaymentRequestMetaDataDomain() != null) {
                            ackPaymentRequestProcessing(changeRequest.getPaymentRequest(), changeRequest.getPaymentRequestMetaDataDomain(),changeRequest.getLdcId(),paymentRequestData);
                        }


                        return logPaymentRequestStatusChange(changeRequest);
                    } else {
                        throw new SumoException("Failed to change payment request status from "
                                + changeRequest.getCurrentStatus() + " to " + changeRequest.getNewStatus() + ".");
                    }
                } else {
                    throw new SumoException("Payment request status change from " + changeRequest.getCurrentStatus()
                            + " to " + changeRequest.getNewStatus() + " not allowed.");
                }
            } else {
                throw new SumoException("Payment request id " + changeRequest.getPaymentRequestId() + " is not valid.");
            }

        } else {
            throw new SumoException("Payment request id is required.");
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    private void ackPaymentRequestProcessing(PaymentRequest pr, PaymentRequestMetaDataDomain paymentRequestMetaDataDomain,Long ldcId,PaymentRequestData prData) throws SumoException {
        try {
            LOG.info("########### Starting ack payment processing for pr id : {} ###################", pr.getPaymentRequestId());
            PaymentRequestMetaData.PaymentRequestMetaDataBuilder paymentRequestMetaDataBuilder = PaymentRequestMetaData.builder()
                    .paymentRequest(paymentRequestManagementDao.find(PaymentRequestData.class, pr.getPaymentRequestId()))
                    .gstRate(paymentRequestMetaDataDomain.getGstRate())
                    .isRCM(paymentRequestMetaDataDomain.getIsRcm() ? "Y" : "N")
                    .isGstAvailed(paymentRequestMetaDataDomain.getIsGstAvailed() ? "Y" : "N")
                    .tdsLedger(paymentRequestMetaDataDomain.getTdsLedger() != null ? PrMetaDataMapper.INSTANCE.toTdsLegerRateData(paymentRequestMetaDataDomain.getTdsLedger()) : null)
                    .supplierState(paymentRequestMetaDataDomain.getSupplierState() != null ? PrMetaDataMapper.INSTANCE.toGstStateMetaDataData(paymentRequestMetaDataDomain.getSupplierState()) : null)
                    .recipientState(paymentRequestMetaDataDomain.getRecipientState() != null ? PrMetaDataMapper.INSTANCE.toGstOfStplData(paymentRequestMetaDataDomain.getRecipientState()) : null)
                    .tdsPercentage(paymentRequestMetaDataDomain.getTdsPercentage()).loggedMessages(paymentRequestMetaDataDomain.getLoggedMessages());
        if(paymentRequestMetaDataDomain.getGstRate()!=null || paymentRequestMetaDataDomain.getTdsPercentage()!=null || ldcId!=null){
            prData.setProposedAmount(pr.getProposedAmount());
             prData.setPaidAmount(pr.getPaidAmount());
             paymentRequestManagementDao.update(prData,true);

            PaymentInvoiceData paymentInvoiceData = paymentRequestManagementDao.findPaymentInvoice(prData.getId());
            paymentInvoiceData.setCalculatedInvoiceAmount(pr.getPaymentInvoice().getCalculatedInvoiceAmount());
            paymentInvoiceData.setInvoiceAmount(pr.getPaymentInvoice().getInvoiceAmount());
            paymentInvoiceData.setPaymentAmount(pr.getPaymentInvoice().getPaymentAmount());
            paymentRequestManagementDao.update(paymentInvoiceData,true);

            Map<Integer,PaymentInvoiceItem> prInvoiceItems = pr.getPaymentInvoice().getPaymentInvoiceItems().stream().collect(Collectors.toMap(PaymentInvoiceItem::getPaymentInvoiceItemId,Function.identity()));

            for(PaymentInvoiceItemData pid : paymentInvoiceData.getPaymentInvoiceItemData()){
                pid.setTdsRate(prInvoiceItems.get(pid.getId()).getTdsRate());
                pid.setTotalAmount(prInvoiceItems.get(pid.getId()).getTotalAmount());
                pid.setTotalTax(prInvoiceItems.get(pid.getId()).getTotalTax());
                paymentRequestManagementDao.update(pid,true);
            }

            if(paymentRequestMetaDataDomain.getGstRate()!=null){
                for(PaymentInvoiceItem pi : pr.getPaymentInvoice().getPaymentInvoiceItems()){
                    PaymentInvoiceItemData pid = paymentRequestManagementDao.find(PaymentInvoiceItemData.class,pi.getPaymentInvoiceItemId());
                    paymentRequestManagementDao.removePaymentInvoiceTaxItem(pi.getPaymentInvoiceItemId());
                    for(PaymentInvoiceItemTax pit : pi.getTaxes()){
                        paymentRequestManagementDao.update(SCMDataConverter.convert(pit,pid),true);
                    }
                }
            }

         if(ldcId!=null){
             paymentRequestMetaDataBuilder.ldcVendorData(ldcVendorDao.getById(ldcId));
             ldcVendorServiceDecorator.updateLdcRemainingLimit(ldcId,
                     getBasicInvoiceAmount(pr.getPaymentInvoice()).doubleValue());
         }



            if(pr.getVendorAdvancePayments() !=null && pr.getVendorAdvancePayments().size() > 0 && pr.getAdvancePayment()!=null){
                advancePaymentRecalculation(pr,prData);
                 }
    }


            PaymentRequestMetaData  paymentRequestMetaData =  paymentRequestMetaDataBuilder.build();
            paymentRequestManagementDao.add(paymentRequestMetaData, true);
            LOG.info("########### Completed ack payment processing for pr id : {} ###################", pr.getPaymentRequestId());
        } catch (Exception e) {
            LOG.error("Error while processing ack payment request : " + e.getMessage());
            throw new SumoException(e);
        }
    }

    private void advancePaymentRecalculation(PaymentRequest pr,PaymentRequestData prData) throws SumoException {

        LOG.info("########### (1) Advance Payment Recalculation start prId : {} ###################", pr.getPaymentRequestId());
        if(pr.getCreatedBy() == null){
            pr.setCreatedBy(new IdCodeName(SCMServiceConstants.SYSTEM_USER,"System user",null));
        }

        LOG.info("########### (2) Validating availableAmount, prId : {} ###################", pr.getPaymentRequestId());
        // validating with data got from UI
        List<AdvancePaymentData> advancePaymentDataList = paymentRequestManagementDao.getAllAdvancePaymentByAdvanceIds(pr.getAdvancePaymentIds());
        validateAvailableAmount(advancePaymentDataList, pr.getAdvancePayment().getAvailableAmount());

        LOG.info("########### (3) Reverse advance data, prId : {} ###################", pr.getPaymentRequestId());
        // reverse advance data amount
        List<AdvancePaymentAuditLogData> advancePaymentAuditLogDataList = paymentRequestManagementDao.getAdvancePaymentAuditLog(prData);
        reverseAdvancePaymentLogsAndData(advancePaymentAuditLogDataList,null,prData, pr.getCreatedBy().getId());

        LOG.info("########### (4) Recalculating advance payment, prId : {} ###################", pr.getPaymentRequestId());
        // re-calculate advance
        BigDecimal remainingAmount = pr.getAdvancePayment().getUsedAmount();
        calculateAdvance(advancePaymentDataList,prData,remainingAmount,pr,false);

        LOG.info("########### (5) update child advance payment, prId : {} ###################", pr.getPaymentRequestId());
       // update child advance
        for(AdvancePaymentData ad : advancePaymentDataList){
            if(ad.getChildAdvance()!=null){
                if(!Objects.equals(pr.getAdvancePayment().getFinalAvailable(), BigDecimal.ZERO)){
                    ad.getChildAdvance().setPrAmount(pr.getAdvancePayment().getFinalAvailable());
                    ad.getChildAdvance().setAvailableAmount(pr.getAdvancePayment().getFinalAvailable());
                }
                if(Objects.equals(pr.getAdvancePayment().getFinalAvailable(),BigDecimal.ZERO)){
                    ad.setAdjustedPoSo(null);
                    ad.setChildAdvance(null);
                    ad.setRefundInitiatedBy(null);
                    ad.setRefundDate(null);
                    paymentRequestManagementDao.delete(ad.getChildAdvance());
                }
                paymentRequestManagementDao.update(ad,true);
            }
        }
    }

    private void sendQueriedEmailNotification(PaymentRequestData paymentRequestData, PaymentRequestStatusChangeVO changeRequest, Boolean isQueried) throws EmailGenerationException {
        Map<String, Object> emailData = new HashMap<>();
        emailData.put("vendorPaymentDate", paymentRequestData.getVendorPaymentDate());
        emailData.put("paymentDate", AppUtils.getFormattedTime(paymentRequestData.getPaymentDate(), "yyyy-MM-dd"));
        Integer queriedBy = null;
        Integer queryResolvedBy = null;
        Set<String> emailIds = new HashSet<>();
        String defaultEmailId = "<EMAIL>";
        emailData.put("generatedBy", SCMUtil.getCreatedBy(masterDataCache.getEmployee(paymentRequestData.getCreatedBy()), paymentRequestData.getCreatedBy()));
        EmployeeBasicDetail generatedByEmpDetail = masterDataCache.getEmployeeBasicDetail(paymentRequestData.getCreatedBy());
        emailData.put("generatedByEmail", defaultEmailId);
        emailIds.add(defaultEmailId);
        if (Objects.nonNull(generatedByEmpDetail) && Objects.nonNull(generatedByEmpDetail.getEmailId())) {
            emailData.put("generatedByEmail", generatedByEmpDetail.getEmailId());
            emailIds.add(generatedByEmpDetail.getEmailId());
        }
        if (isQueried) {
            queriedBy = changeRequest.getUpdatedBy();
            queryResolvedBy = paymentRequestData.getCreatedBy();
            EmployeeBasicDetail queriedByEmployee = masterDataCache.getEmployeeBasicDetail(queriedBy);
            EmployeeBasicDetail queryResolvedByEmployee = masterDataCache.getEmployeeBasicDetail(queryResolvedBy);
            emailData.put("queriedBy", SCMUtil.getCreatedBy(queriedByEmployee.getName(), queriedBy));
            emailData.put("resolvedBy", SCMUtil.getCreatedBy(queryResolvedByEmployee.getName(), queryResolvedBy));
            emailData.put("queriedByEmailId", defaultEmailId);
            if (Objects.nonNull(queriedByEmployee.getEmailId())) {
                emailData.put("queriedByEmailId", queriedByEmployee.getEmailId());
                emailIds.add(queriedByEmployee.getEmailId());
            }
        } else {
            queriedBy = changeRequest.getPaymentRequestQueries().get(0).getQueryRaisedBy();
            queryResolvedBy = paymentRequestData.getCreatedBy();
            EmployeeBasicDetail queriedByEmployee = masterDataCache.getEmployeeBasicDetail(queriedBy);
            EmployeeBasicDetail queryResolvedByEmployee = masterDataCache.getEmployeeBasicDetail(queryResolvedBy);
            emailData.put("queriedBy", SCMUtil.getCreatedBy(queriedByEmployee.getName(), queriedBy));
            emailData.put("resolvedBy", SCMUtil.getCreatedBy(queryResolvedByEmployee.getName(), queryResolvedBy));
            emailData.put("queriedByEmailId", defaultEmailId);
            emailData.put("queryResolvedByEmailId", defaultEmailId);
            if (Objects.nonNull(queriedByEmployee.getEmailId())) {
                emailData.put("queriedByEmailId", queriedByEmployee.getEmailId());
                emailIds.add(queriedByEmployee.getEmailId());
            }
            if (Objects.nonNull(queryResolvedByEmployee.getEmailId())) {
                emailData.put("queryResolvedByEmailId", queryResolvedByEmployee.getEmailId());
                emailIds.add(queryResolvedByEmployee.getEmailId());
            }

        }
        emailData.put("prId", paymentRequestData.getId());
        emailData.put("isQueried", isQueried);
        PRQueryEmailNotificationTemplate prQueryEmailNotificationTemplate = new PRQueryEmailNotificationTemplate(changeRequest.getPaymentRequestQueries(), emailData, props.getBasePath());
        PrQueryEmailNotification prQueryEmailNotification = new PrQueryEmailNotification(prQueryEmailNotificationTemplate, props.getEnvType(), emailIds);
        prQueryEmailNotification.sendEmail();
    }

    private void changePrToQueried(PaymentRequestData paymentRequestData, PaymentRequestStatusChangeVO changeRequest) throws SumoException {
        if (Objects.nonNull(changeRequest.getPaymentRequestQueries()) && !changeRequest.getPaymentRequestQueries().isEmpty()) {
            List<PaymentRequestQueryData> paymentRequestQueryDataList = new ArrayList<>();
            for (PaymentRequestQuery e : changeRequest.getPaymentRequestQueries()) {
                PaymentRequestQueryData paymentRequestQueryData = new PaymentRequestQueryData();
                paymentRequestQueryData.setPrId(paymentRequestData);
                paymentRequestQueryData.setPaymentDeviationData(paymentRequestManagementDao.find(PaymentDeviationData.class, e.getPaymentDeviationId()));
                paymentRequestQueryData.setQueryRaisedBy(changeRequest.getUpdatedBy());
                paymentRequestQueryData.setRaisedByComment(e.getRaisedByComment());
                paymentRequestQueryDataList.add(paymentRequestManagementDao.add(paymentRequestQueryData, true));
            }
            paymentRequestData.setPaymentRequestQueryData(paymentRequestQueryDataList);
            paymentRequestManagementDao.update(paymentRequestData, true);
            sendQueryEmailNotification(paymentRequestData, changeRequest);
        }
    }

    private void sendQueryEmailNotification(PaymentRequestData paymentRequestData, PaymentRequestStatusChangeVO changeRequest) {
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updatePaymentQuantity(PaymentRequestData paymentRequestData) {
        PaymentInvoiceData invoiceData = paymentRequestManagementDao.findPaymentInvoice(paymentRequestData.getId());
        for (PaymentInvoiceItemData paymentInvoiceItem : invoiceData.getPaymentInvoiceItemData()) {
            if (paymentInvoiceItem.getServiceReceivedItemId() != null) {
                ServiceReceivedItemData receiveItemData = paymentRequestManagementDao
                        .find(ServiceReceivedItemData.class, paymentInvoiceItem.getServiceReceivedItemId());
                receiveItemData.setPendingInvoiceQuantity(receiveItemData.getReceivedQuantity());
                receiveItemData.setInvoiceQuantity(BigDecimal.ZERO);
                paymentRequestManagementDao.update(receiveItemData, true);
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean approvePaymentRequest(PaymentRequest paymentRequest) throws SumoException, InventoryUpdateException, ParseException, DataNotFoundException, TransferOrderCreationException, EmailGenerationException {
        if (paymentRequest.getPaymentRequestId() != null) {
            Date currentTime = SCMUtil.getCurrentTimestamp();
            PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class,
                    paymentRequest.getPaymentRequestId());
            if (paymentRequestData != null) {
				/*if (paymentRequest.getPaymentCycle() == null) {
					throw new SumoException("Payment cycle not selected.");
				}
				PaymentCalendarData paymentCalendarData = paymentRequestManagementDao.find(PaymentCalendarData.class,
						paymentRequest.getPaymentCycle().getId());
				if (!validPaymentDate(paymentCalendarData)) {
					throw new SumoException("Invalid payment date selected.");
				} else {
					paymentRequestData.setPaymentCalendarData(paymentCalendarData);
				}*/
                if (paymentRequestData.getType().equals(PaymentRequestType.MILK_BAKERY.value())) {
                    SpecializedOrderInvoiceData specializedOrderInvoiceData = paymentRequestManagementDao.
                            findSpecializedOrderInvoiceByPrId(paymentRequestData.getId());
                    Boolean isGrCreated = goodsReceiveManagementService.createGrForExcessPrQty(specializedOrderInvoiceData.getSpecializedOrderInvoiceId(),
                            specializedOrderInvoiceData.getUnitId(), specializedOrderInvoiceData.getVendorId());
                    if (isGrCreated.equals(Boolean.TRUE)) {
                        LOG.info("Successfully  Created Gr For Excess Invoice Qty For Pr Id : {}", paymentRequestData.getId());
                    } else {
                        LOG.info(" Not Eligible  For Excess Invoice Qty Gr  For Pr Id : {}", paymentRequestData.getId());
                    }
                }

                if (paymentRequestData.getType().equals(PaymentRequestType.SERVICE_RECEIVED.name())) {
                    paymentRequestData.setPaidAmount(paymentRequest.getPaidAmount());
                    paymentRequestData.setProposedAmount(paymentRequest.getProposedAmount());
                }
                //paymentRequestData.setPaidAmount(paymentRequest.getPaidAmount());
                paymentRequestData.setLastUpdated(currentTime);
                String currentStatus = paymentRequestData.getCurrentStatus();
                paymentRequestData.setCurrentStatus(PaymentRequestStatus.APPROVED.value());
                if (paymentRequestData.getPaidAmount().compareTo(paymentRequestData.getProposedAmount()) > 0) {
                    throw new SumoException("Paid amount cannot be greater than proposed amount.");
                }
                List<DebitNoteDetailData> debitNoteDetailDataList = paymentRequestManagementDao
                        .findDebitNoteByPaymentRequest(paymentRequest.getPaymentRequestId());
				/*if (paymentRequestData.getPaidAmount().compareTo(paymentRequestData.getProposedAmount()) < 0) {
					if (debitNoteDetailDataList == null || debitNoteDetailDataList.size() == 0) {
						throw new SumoException("Paid amount is less and debit note not provided.");
					}
				}*/
                BigDecimal totalDebitNoteAmount = BigDecimal.ZERO;
                for (DebitNoteDetailData debitNoteDetailData : debitNoteDetailDataList) {
                    totalDebitNoteAmount = SCMUtil.add(totalDebitNoteAmount, debitNoteDetailData.getTotalAmount());
                }
                paymentRequestData.setPaidAmount(paymentRequestData.getProposedAmount().subtract(totalDebitNoteAmount));
				/*if (paymentRequestData.getPaidAmount().compareTo(paymentRequestData.getProposedAmount()) == 0
						&& debitNoteDetailDataList != null && debitNoteDetailDataList.size() > 0) {
					throw new SumoException(
							"Debit note is available but still paid amount is equal to proposed amount.");
				}*/
                VendorDetailData vendorDetailData = paymentRequestManagementDao.find(VendorDetailData.class,
                        paymentRequest.getVendorId().getId());
                paymentRequestData.setIsBlocked(SCMUtil.setStatus(blockedPayment(vendorDetailData)));
                if (Objects.nonNull(paymentRequest.getExtraChargesType())) {
                    paymentRequestData.setExtraChargesType(paymentRequest.getExtraChargesType());
                }
                if (Objects.nonNull(paymentRequest.getExtraChargesSgst())) {
                    paymentRequestData.setExtraChargesSgst(paymentRequest.getExtraChargesSgst());
                }
                if (Objects.nonNull(paymentRequest.getExtraChargesCgst())) {
                    paymentRequestData.setExtraChargesCgst(paymentRequest.getExtraChargesCgst());
                }
                if (Objects.nonNull(paymentRequest.getExtraChargesIgst())) {
                    paymentRequestData.setExtraChargesIgst(paymentRequest.getExtraChargesIgst());
                }
                if (Objects.nonNull(paymentRequest.getExtraChargesWithOutTax())) {
                    paymentRequestData.setExtraChargesWithOutTax(paymentRequest.getExtraChargesWithOutTax());
                }
                paymentRequestData = paymentRequestManagementDao.update(paymentRequestData, false);
                if (paymentRequestData != null) {
                    if (paymentRequestData.getType().equals(PaymentRequestType.SERVICE_RECEIVED.name())) {
                        updateTdsRate(paymentRequest);
                        PaymentRequestMetaData prMetaData = paymentRequestManagementDao.getPaymentRequestMetaDataForPr(paymentRequestData);
                        prMetaData.setTdsLedger(PrMetaDataMapper.INSTANCE.toTdsLegerRateData(paymentRequest.getPaymentRequestMetaData().getTdsLedger()));
                        prMetaData.setTdsPercentage(paymentRequest.getPaymentRequestMetaData().getTdsPercentage());
                        paymentRequestManagementDao.update(prMetaData,true);
                    }
                    setInvoiceDeviationMappings(paymentRequest, currentTime);
                    String logData = "Payment request id: " + paymentRequestData.getId() + " approved by: "
                            + masterDataCache.getEmployee(paymentRequest.getUpdatedBy().getId()) + "["
                            + paymentRequest.getUpdatedBy().getId() + "].";
                    logPaymentRequestData(logData, paymentRequestData.getId());
                    PaymentRequestStatusChangeVO changeRequest = new PaymentRequestStatusChangeVO();
                    changeRequest.setCurrentStatus(PaymentRequestStatus.valueOf(currentStatus));
                    changeRequest.setNewStatus(PaymentRequestStatus.APPROVED);
                    changeRequest.setPaymentRequestId(paymentRequest.getPaymentRequestId());
                    changeRequest.setUpdatedBy(paymentRequest.getUpdatedBy().getId());
                    logPaymentRequestStatusChange(changeRequest);
                    PaymentRequest request = viewPaymentRequest(paymentRequestData.getId());
                    if (Objects.nonNull(request) && Objects.nonNull(request.getVendorAdvancePayments()) && !request.getVendorAdvancePayments().isEmpty() && Objects.nonNull(request.getPaidAmount()) && request.getPaidAmount().compareTo(BigDecimal.ZERO) == 0) {
                        PaymentRequestData advancePaymentPrData = paymentRequestData.getAdvancePaymentData().getPaymentRequestId();
                        if (Objects.nonNull(advancePaymentPrData) && Objects.nonNull(advancePaymentPrData.getPrPaymentDetailData())) {
                            PRPaymentDetailData prPaymentDetailData = advancePaymentPrData.getPrPaymentDetailData();
                            LOG.info("Auto Settling Payment Request for PR ID : {}", paymentRequestData.getId());
                            autoSettleZeroAmountPr(Collections.singletonList(request), paymentRequestData.getCompanyId(), changeRequest.getUpdatedBy(), prPaymentDetailData.getDebitBank(), prPaymentDetailData.getDebitAccountNumber());
                        }
                    }
                    if (Objects.nonNull(request.getPaymentCard())) {
                        PaymentSheetVO paymentSheetVO = new PaymentSheetVO();
                        paymentSheetVO.setPaymentRequestIds(Collections.singletonList(request.getPaymentRequestId()));
                        paymentSheetVO.setReason("FORCE_CLOSE");
                        paymentSheetVO.setUpdatedBy(paymentRequest.getUpdatedBy().getId());
                        forceSettlePaymentRequests(paymentSheetVO);
                    }
                } else {
                    throw new SumoException("Error updating payment request.");
                }
            } else {
                throw new SumoException("Invalid payment request.");
            }
        }
        return true;
    }

    private void updateTdsRate(PaymentRequest pr) {
        PaymentInvoice prInvoice = pr.getPaymentInvoice();
        PaymentInvoiceData invoiceData = paymentRequestManagementDao
                .findInvoiceByPaymentRequestId(pr.getPaymentRequestId());
        Map<Integer, PaymentInvoiceItem> invoiceItemMap = prInvoice.getPaymentInvoiceItems().stream()
                .collect(Collectors.toMap(PaymentInvoiceItem::getPaymentInvoiceItemId, Function.identity()));
        if (invoiceData != null) {
            invoiceData.setCalculatedInvoiceAmount(prInvoice.getCalculatedInvoiceAmount());
            invoiceData.setInvoiceAmount(prInvoice.getInvoiceAmount());
            invoiceData.setPaymentAmount(prInvoice.getPaymentAmount());
            for (PaymentInvoiceItemData item : invoiceData.getPaymentInvoiceItemData()) {
                PaymentInvoiceItem itemFromMap = invoiceItemMap.get(item.getId());
                if (itemFromMap != null && itemFromMap.getTdsRate() != null
                        && itemFromMap.getTdsRate().compareTo(BigDecimal.ZERO) > 0) {
                    item.setTdsRate(itemFromMap.getTdsRate());
                    item.setTotalAmount(itemFromMap.getTotalAmount());
                    item.setTotalTax(itemFromMap.getTotalTax());
                    Map<Integer, PaymentInvoiceItemTax> taxMap = itemFromMap.getTaxes().stream()
                            .collect(Collectors.toMap(PaymentInvoiceItemTax::getTaxDetailId, Function.identity()));
                    item.getTaxes().forEach(taxData -> {
                        taxData.setTaxValue(taxMap.get(taxData.getTaxDetailId()).getTaxValue());
                        taxData.setTaxPercentage(taxMap.get(taxData.getTaxDetailId()).getTaxPercentage());
                        paymentRequestManagementDao.update(taxData, false);
                    });
                    paymentRequestManagementDao.update(item, false);
                }
            }
            paymentRequestManagementDao.update(invoiceData, false);
        }
    }

    private Set<String> getCostCenterMailData(PaymentRequest paymentRequest) {
        Set<String> emailSet = new HashSet<>();
        if (paymentRequest.getType().equals(PaymentRequestType.SERVICE_RECEIVED)) {
            List<ServiceOrderData> serviceOrderData = serviceReceiveManagementDao.findSoForPr(paymentRequest.getPaymentRequestId());
            if (serviceOrderData != null) {
                for (ServiceOrderData srd : serviceOrderData) {
                    CostCenterData costCenterData = serviceOrderManagementDao.find(CostCenterData.class, srd.getCostCenterId());
                    if (costCenterData != null) {
                        emailSet.add(costCenterData.getCostCenterEmail());
                    }
                }
            }
        }
        return emailSet;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean rejectPaymentRequest(PaymentRequest paymentRequest) throws SumoException, EmailGenerationException {
        if (paymentRequest.getPaymentRequestId() != null) {

            Date currentTime = SCMUtil.getCurrentTimestamp();
            PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class,
                    paymentRequest.getPaymentRequestId());
            paymentRequestData.setLastUpdated(currentTime);
            String currentStatus = paymentRequestData.getCurrentStatus();
            paymentRequestData.setCurrentStatus(PaymentRequestStatus.REJECTED.value());
            paymentRequestData = paymentRequestManagementDao.update(paymentRequestData, false);
            if (paymentRequestData != null) {
                setInvoiceRejections(paymentRequest, currentTime);
                String logData = "Payment request id: " + paymentRequestData.getId() + " rejected by: "
                        + masterDataCache.getEmployee(paymentRequest.getUpdatedBy().getId()) + "["
                        + paymentRequest.getUpdatedBy().getId() + "].";
                logPaymentRequestData(logData, paymentRequestData.getId());
                PaymentRequestStatusChangeVO changeRequest = new PaymentRequestStatusChangeVO();
                changeRequest.setCurrentStatus(PaymentRequestStatus.valueOf(currentStatus));
                changeRequest.setNewStatus(PaymentRequestStatus.REJECTED);
                changeRequest.setPaymentRequestId(paymentRequest.getPaymentRequestId());
                changeRequest.setUpdatedBy(paymentRequest.getUpdatedBy().getId());
                logPaymentRequestStatusChange(changeRequest);
                Set<String> emailSet = getCostCenterMailData(paymentRequest);
                List<Integer> grIds = getGrIdsForPr(paymentRequestData);
                List<Integer> srIds = new ArrayList<>();
                List<String> businessCostCenters = new ArrayList<>();
                try {
                    List<ServiceReceivedData> serviceReceivedDataList = serviceReceiveManagementDao
                            .findServiceReceivingForPaymentRequest(paymentRequestData.getId());
                    for (ServiceReceivedData data : serviceReceivedDataList) {
                        srIds.add(data.getServiceReceivedId());
                        for (ServiceReceivedItemData itemData : data.getServiceItemList()) {
                            String bcc = itemData.getBusinessCostCenterName() + "(" + itemData.getBusinessCostCenterId() + ")";
                            businessCostCenters.add(bcc);
                        }
                    }
                } catch (Exception e) {
                    LOG.error("Exception Occurred while fetching GR Id's :: ", e);
                }
                LOG.info("size of gr is {} and sr is {}", grIds.size(), srIds.size());
                revertLinkedGRData(paymentRequestData);
                revertLinkedSRData(paymentRequestData);
                revertAdvancePayment(paymentRequestData, paymentRequest.getUpdatedBy().getId(), PaymentRequestStatus.REJECTED.value());
                if (paymentRequest.getType().equals(PaymentRequestType.MILK_BAKERY)) {
                    revertMilkInvoice(paymentRequestData.getId());
                }
                UnitBasicDetail unitBasicDetail = null;
                if (Objects.nonNull(paymentRequestData.getRequestingUnit())) {
                    unitBasicDetail = masterDataCache.getUnitBasicDetail(paymentRequestData.getRequestingUnit());
                }
                // TODO send rejection mail
                VendorPRRejectEmailNotificationTemplate vendorPRRejectEmailNotificationTemplate = new VendorPRRejectEmailNotificationTemplate(
                        scmCache.getVendorDetail(paymentRequestData.getVendorId()), paymentRequest,
                        props.getBasePath(), grIds, srIds, unitBasicDetail, masterDataCache.getEmployee(paymentRequest.getUpdatedBy().getId()),
                        masterDataCache.getEmployee(paymentRequest.getCreatedBy().getId()), businessCostCenters);

                if (emailSet != null && emailSet.size() > 0) {
                    String[] emails = new String[emailSet.size()];
                    emailSet.toArray(emails);
                    VendorPRRejectEmailNotification vendorPRRejectEmailNotification = new VendorPRRejectEmailNotification(
                            vendorPRRejectEmailNotificationTemplate, props.getEnvType(), emails);
                    vendorPRRejectEmailNotification.sendEmail();
                } else {
                    String[] emails = {"<EMAIL>", "<EMAIL>"};
                    VendorPRRejectEmailNotification vendorPRRejectEmailNotification = new VendorPRRejectEmailNotification(
                            vendorPRRejectEmailNotificationTemplate, props.getEnvType(), emails);
                    vendorPRRejectEmailNotification.sendEmail();
                }
            } else {
                throw new SumoException("Error updating payment request.");
            }
        }
        return true;
    }

    private void revertAdvancePayment(PaymentRequestData paymentRequestData, Integer userId, String status) throws SumoException {
        LOG.info("Reverting the Advance Payment ");
        if (status.equalsIgnoreCase(PaymentRequestStatus.REJECTED.value())) {
            if (paymentRequestData.getType().equalsIgnoreCase(AppConstants.ADVANCE_PAYMENT)) {
                AdvancePaymentData advancePaymentData = paymentRequestData.getAdvancePaymentData();
                String prevStatus = advancePaymentData.getAdvanceStatus();
                String msg = null;
                advancePaymentData.setAdvanceStatus(AdvancePaymentStatus.REJECTED.value());
                if (advancePaymentData.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                    PurchaseOrderData purchaseOrderData = paymentRequestManagementDao.find(PurchaseOrderData.class, advancePaymentData.getPurchaseOrderData().getId());
                    msg = "REJECTED_FOR_PO_ID_" + purchaseOrderData.getId();
                    advancePaymentData.setPurchaseOrderData(null);
                    paymentRequestManagementDao.update(purchaseOrderData, true);
                }
                if (advancePaymentData.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
                    ServiceOrderData serviceOrderData = paymentRequestManagementDao.find(ServiceOrderData.class, advancePaymentData.getServiceOrderData().getId());
                    msg = "REJECTED_FOR_SO_ID_" + serviceOrderData.getId();
                    advancePaymentData.setServiceOrderData(null);
                    paymentRequestManagementDao.update(serviceOrderData, true);
                }
                advancePaymentData.setRejectedFor(msg);
                advancePaymentData.setLastUpdatedDate(AppUtils.getCurrentTimestamp());
                advancePaymentData.setLastUpdatedBy(userId);
                logAdvancePaymentStatus(prevStatus, advancePaymentData.getAdvanceStatus(), advancePaymentData, userId);
                paymentRequestManagementDao.update(advancePaymentData, true);
            } else {
                DebitNoteDetailData debitNoteDetailData = paymentRequestManagementDao.findDebitNoteByPaymentRequestId(paymentRequestData.getId());
                if (Objects.nonNull(debitNoteDetailData)) {
                    debitNoteDetailData.setDebitNoteStatus(PaymentRequestStatus.REJECTED.value());
                    paymentRequestManagementDao.update(debitNoteDetailData, true);
                    if (Objects.nonNull(debitNoteDetailData.getAdvanceAmount()) && Objects.nonNull(debitNoteDetailData.getAdvancePaymentId())) {
                        AdvancePaymentData vendorAdvancePayment = paymentRequestManagementDao.find(AdvancePaymentData.class, debitNoteDetailData.getAdvancePaymentId());
                        logAdvancePaymentAudit(debitNoteDetailData.getAdvanceAmount(), paymentRequestData, vendorAdvancePayment, userId, AdvancePaymentStatus.UN_BLOCKED.value());
                    }
                }
                reverseAdvancePayment(paymentRequestData, userId);
            }
        }
        if (status.equalsIgnoreCase(PaymentRequestStatus.CANCELLED.value())) {
            if (paymentRequestData.getType().equalsIgnoreCase(AppConstants.ADVANCE_PAYMENT)) {
                AdvancePaymentData advancePaymentData = paymentRequestData.getAdvancePaymentData();
                if (Objects.nonNull(advancePaymentData)) {
                    String msg = null;
                    String prevStatus = advancePaymentData.getAdvanceStatus();
                    advancePaymentData.setAdvanceStatus(AdvancePaymentStatus.CANCELLED.value());
                    if (advancePaymentData.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                        PurchaseOrderData purchaseOrderData = paymentRequestManagementDao.find(PurchaseOrderData.class, advancePaymentData.getPurchaseOrderData().getId());
                        msg = "CANCELLED_FOR_PO_ID_" + purchaseOrderData.getId();
                        advancePaymentData.setPurchaseOrderData(null);
                        paymentRequestManagementDao.update(purchaseOrderData, true);
                    }
                    if (advancePaymentData.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
                        ServiceOrderData serviceOrderData = paymentRequestManagementDao.find(ServiceOrderData.class, advancePaymentData.getServiceOrderData().getId());
                        msg = "CANCELLED_FOR_SO_ID_" + serviceOrderData.getId();
                        advancePaymentData.setServiceOrderData(null);
                        paymentRequestManagementDao.update(serviceOrderData, true);
                    }
                    advancePaymentData.setRejectedFor(msg);
                    advancePaymentData.setLastUpdatedDate(AppUtils.getCurrentTimestamp());
                    advancePaymentData.setLastUpdatedBy(userId);
                    logAdvancePaymentStatus(prevStatus, advancePaymentData.getAdvanceStatus(), advancePaymentData, userId);
                    paymentRequestManagementDao.update(advancePaymentData, true);
                }
            } else {
                reverseAdvancePayment(paymentRequestData, userId);
            }
        }

        if (status.equalsIgnoreCase(PaymentRequestStatus.HOD_REJECTED.value())) {
            if (Objects.nonNull(paymentRequestData.getAdvanceAmount())) {
                List<AdvancePaymentAuditLogData> advancePaymentAuditLogDataList = paymentRequestManagementDao.getAdvancePaymentAuditLog(paymentRequestData);
                List<AdvancePaymentData> advancePaymentDataList = new ArrayList<>();
                if (paymentRequestData.getAdvancePaymentData().getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                    PurchaseOrderData purchaseOrderData = paymentRequestData.getAdvancePaymentData().getPurchaseOrderData();
                    advancePaymentDataList = purchaseOrderData.getAdvancePaymentDatas();
                } else {
                    ServiceOrderData serviceOrderData = paymentRequestData.getAdvancePaymentData().getServiceOrderData();
                    advancePaymentDataList = serviceOrderData.getAdvancePaymentDatas();
                }
                Map<Integer, String> prevStatusMap = new HashMap<>();
                List<AdvancePaymentData> notCompletedList = new ArrayList<>();
                for (AdvancePaymentData advancePaymentData : advancePaymentDataList) {
                    prevStatusMap.put(advancePaymentData.getAdvancePaymentId(), advancePaymentData.getAdvanceStatus());
                    if (!advancePaymentData.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.COMPLETED.value())) {
                        notCompletedList.add(advancePaymentData);
                    }
                }
                AdvancePaymentData advanceRejectedData = null;
                for (AdvancePaymentAuditLogData logData : advancePaymentAuditLogDataList) {
                    AdvancePaymentData vendorAdvancePayment = logData.getAdvancePaymentId();
                    prevStatusMap.put(vendorAdvancePayment.getAdvancePaymentId(), vendorAdvancePayment.getAdvanceStatus());
                    logAdvancePaymentAudit(logData.getAmount(), paymentRequestData, vendorAdvancePayment, userId, AdvancePaymentStatus.UN_BLOCKED.value());
                }

                for (AdvancePaymentData vendorAdvancePayment : notCompletedList) {
                    vendorAdvancePayment.setLastUpdatedDate(AppUtils.getCurrentTimestamp());
                    vendorAdvancePayment.setLastUpdatedBy(userId);
                    if (Objects.nonNull(vendorAdvancePayment.getAdjustedPoSo()) || Objects.nonNull(vendorAdvancePayment.getRefundDate())) {
                        if (Objects.nonNull(vendorAdvancePayment.getAdjustedPoSo())) {
                            advanceRejectedData = vendorAdvancePayment.getChildAdvance();
                            vendorAdvancePayment.setAdjustedPoSo(null);
                            vendorAdvancePayment.setChildAdvance(null);
                        }
                        if (Objects.nonNull(vendorAdvancePayment.getRefundDate())) {
                            vendorAdvancePayment.setAdvanceStatus(AdvancePaymentStatus.CREATED.value());
                            vendorAdvancePayment.setRefundDate(null);
                            vendorAdvancePayment.setRefundInitiatedBy(null);
                            logAdvancePaymentStatus(prevStatusMap.get(vendorAdvancePayment.getAdvancePaymentId()), vendorAdvancePayment.getAdvanceStatus(), vendorAdvancePayment, userId);
                        }
                    }
                    paymentRequestManagementDao.update(vendorAdvancePayment, true);
                }
                if (Objects.nonNull(advanceRejectedData)) {
                    markAdjustedSoPoRejected(null, userId, PaymentRequestStatus.REJECTED.value(), advanceRejectedData);
                }
            }
        }
    }

    private void reverseAdvancePayment(PaymentRequestData paymentRequestData, Integer userId) throws SumoException {
        if (Objects.nonNull(paymentRequestData.getAdvanceAmount())) {
            List<AdvancePaymentAuditLogData> advancePaymentAuditLogDataList = paymentRequestManagementDao.getAdvancePaymentAuditLog(paymentRequestData);
            List<AdvancePaymentData> advancePaymentDataList = new ArrayList<>();
            if (paymentRequestData.getAdvancePaymentData().getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                PurchaseOrderData purchaseOrderData = paymentRequestData.getAdvancePaymentData().getPurchaseOrderData();
                advancePaymentDataList = purchaseOrderData.getAdvancePaymentDatas();
            } else {
                ServiceOrderData serviceOrderData = paymentRequestData.getAdvancePaymentData().getServiceOrderData();
                advancePaymentDataList = serviceOrderData.getAdvancePaymentDatas();
            }
            Map<Integer, String> prevStatusMap = new HashMap<>();
            List<AdvancePaymentData> notCompletedList = new ArrayList<>();
            for (AdvancePaymentData advancePaymentData : advancePaymentDataList) {
                prevStatusMap.put(advancePaymentData.getAdvancePaymentId(), advancePaymentData.getAdvanceStatus());
                if (!advancePaymentData.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.COMPLETED.value())) {
                    notCompletedList.add(advancePaymentData);
                }
            }
            AdvancePaymentData advanceRejectedData = null;
            reverseAdvancePaymentLogsAndData(advancePaymentAuditLogDataList,prevStatusMap,paymentRequestData,userId);

            for (AdvancePaymentData vendorAdvancePayment : notCompletedList) {
                vendorAdvancePayment.setLastUpdatedDate(AppUtils.getCurrentTimestamp());
                vendorAdvancePayment.setLastUpdatedBy(userId);
                if (Objects.nonNull(vendorAdvancePayment.getAdjustedPoSo()) || Objects.nonNull(vendorAdvancePayment.getRefundDate())) {
                    if (Objects.nonNull(vendorAdvancePayment.getAdjustedPoSo())) {
                        advanceRejectedData = vendorAdvancePayment.getChildAdvance();
                        vendorAdvancePayment.setAdjustedPoSo(null);
                        vendorAdvancePayment.setChildAdvance(null);
                    }
                    if (Objects.nonNull(vendorAdvancePayment.getRefundDate())) {
                        vendorAdvancePayment.setAdvanceStatus(AdvancePaymentStatus.CREATED.value());
                        vendorAdvancePayment.setRefundDate(null);
                        vendorAdvancePayment.setRefundInitiatedBy(null);
                        logAdvancePaymentStatus(prevStatusMap.get(vendorAdvancePayment.getAdvancePaymentId()), vendorAdvancePayment.getAdvanceStatus(), vendorAdvancePayment, userId);
                    }
                }
                paymentRequestManagementDao.update(vendorAdvancePayment, true);
            }
            if (Objects.nonNull(advanceRejectedData)) {
                markAdjustedSoPoRejected(null, userId, PaymentRequestStatus.CANCELLED.value(), advanceRejectedData);
            }
        }
    }

    private void reverseAdvancePaymentLogsAndData(List<AdvancePaymentAuditLogData> advancePaymentAuditLogDataList , Map<Integer, String> prevStatusMap,
                                                  PaymentRequestData paymentRequestData, Integer userId
                                                  ) throws SumoException {
        for (AdvancePaymentAuditLogData logData : advancePaymentAuditLogDataList) {
            AdvancePaymentData vendorAdvancePayment = logData.getAdvancePaymentId();
            if(prevStatusMap!=null) prevStatusMap.put(vendorAdvancePayment.getAdvancePaymentId(), vendorAdvancePayment.getAdvanceStatus());
            logAdvancePaymentAudit(logData.getAmount(), paymentRequestData, vendorAdvancePayment, userId, AdvancePaymentStatus.UN_BLOCKED.value());
        }
    }

    private List<Integer> getGrIdsForPr(PaymentRequestData paymentRequestData) {
        List<Integer> result = new ArrayList<>();
        try {
            List<VendorGoodsReceivedData> vendorGoodsReceivedDataList = paymentRequestManagementDao
                    .findVendorGRFromPaymentRequest(paymentRequestData.getId());
            LOG.info("size is : {}", vendorGoodsReceivedDataList.size());
            result = vendorGoodsReceivedDataList.stream().map(VendorGoodsReceivedData::getGoodsReceivedId).collect(Collectors.toList());
        } catch (Exception e) {
            LOG.error("Exception Occurred while fetching GR Id's :: ", e);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DebitNoteDetail addDebitNote(DebitNoteDetail debitNoteDetail) throws SumoException {
        if (debitNoteDetail.getPaymentRequestId() > 0) {
            DebitNoteDetailData debitNoteDetailDatax = paymentRequestManagementDao
                    .findDebitNoteByPaymentRequestId(debitNoteDetail.getPaymentRequestId());
            if (debitNoteDetailDatax == null) {
                PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class,
                        debitNoteDetail.getPaymentRequestId());
                if (paymentRequestData != null) {
                    if (debitNoteDetail.getPaidAmount() != null) {
                        paymentRequestData.setPaidAmount(debitNoteDetail.getPaidAmount());
                        paymentRequestData = paymentRequestManagementDao.update(paymentRequestData, true);
                    }
                    if (paymentRequestData != null) {
                        Date currentTime = SCMUtil.getCurrentTimestamp();
                        DebitNoteDetailData debitNoteDetailData = new DebitNoteDetailData();
                        debitNoteDetailData.setDebitNoteStatus(PaymentRequestStatus.INITIATED.value());
                        debitNoteDetailData.setAmount(debitNoteDetail.getAmount());
                        debitNoteDetailData.setCreditNoteReceived(SCMUtil.setStatus(false));
                        debitNoteDetailData.setInvoiceNumber(debitNoteDetail.getInvoiceNumber());
                        debitNoteDetailData.setPaymentRequest(paymentRequestData);
                        debitNoteDetailData.setTotalTaxes(debitNoteDetail.getTotalTaxes());
                        debitNoteDetailData.setGenerationTime(currentTime);
                        debitNoteDetailData.setGeneratedBy(debitNoteDetail.getGeneratedBy().getId());
                        debitNoteDetailData.setDebitNoteDocId(debitNoteDetail.getDebitNoteDocId());
                        debitNoteDetailData.setTotalAmount(
                                SCMUtil.add(debitNoteDetailData.getAmount(), debitNoteDetailData.getTotalTaxes()));
                        debitNoteDetailData.setLastUpdatedBy(debitNoteDetail.getLastUpdatedBy().getId());
                        debitNoteDetailData.setBusyReferenceNumber(debitNoteDetail.getBusyReferenceNumber());
                        if (Objects.nonNull(debitNoteDetail.getAdvanceAmount())) {
                            debitNoteDetailData.setAdvanceAmount(debitNoteDetail.getAdvanceAmount());
                        }
                        if (Objects.nonNull(debitNoteDetail.getAdvancePaymentId())) {
                            debitNoteDetailData.setAdvancePaymentId(debitNoteDetail.getAdvancePaymentId());
                        }
                        debitNoteDetailData = paymentRequestManagementDao.add(debitNoteDetailData, true);
                        if (debitNoteDetailData == null) {
                            throw new SumoException("Error adding debit note.");
                        }
                        String logData = "Payment request id: " + paymentRequestData.getId() + " debit note generated by "
                                + masterDataCache.getEmployee(debitNoteDetailData.getGeneratedBy()) + " ["
                                + debitNoteDetailData.getGeneratedBy() + "] amount: "
                                + debitNoteDetailData.getTotalAmount();
                        logPaymentRequestData(logData, paymentRequestData.getId());
                        if (Objects.nonNull(debitNoteDetail.getAdvanceAmount())) {
                            AdvancePaymentData vendorAdvancePayment = paymentRequestManagementDao.getVendorAdvancePayment(paymentRequestData.getVendorId(), Collections.singletonList(AdvancePaymentStatus.CREATED.value()), SCMServiceConstants.STAND_ALONE_ADVANCE, null, null);
                            if (Objects.isNull(vendorAdvancePayment)) {
                                throw new SumoException("No vendor Advance Available ..!", "No vendor Advance Available for this Vendor : Entered Advance Use amount : " + debitNoteDetail.getAdvanceAmount());
                            }
                            if (vendorAdvancePayment.getAvailableAmount().compareTo(debitNoteDetail.getAdvanceAmount()) >= 0) {
                                logAdvancePaymentAudit(debitNoteDetail.getAdvanceAmount(), paymentRequestData, vendorAdvancePayment, debitNoteDetail.getGeneratedBy().getId(), AdvancePaymentStatus.BLOCKED.value());
                            } else {
                                throw new SumoException("Exceeding the available Advance ..!", "Available Vendor Advance is : <b>" + vendorAdvancePayment.getAvailableAmount() + "</b>" + "\n" + "<b>Entered Advance Use amount : <b>" + debitNoteDetail.getAdvanceAmount() + "</b>");
                            }
                        }
                        if (Objects.nonNull(debitNoteDetail.getCategorySubCategoryDebitNotes()) && !debitNoteDetail.getCategorySubCategoryDebitNotes().isEmpty()) {
                            for (CategorySubCategoryDebitNote note : debitNoteDetail.getCategorySubCategoryDebitNotes()) {
                                CategorySubCategoryDebitNoteData categorySubCategoryDebitNoteData = new CategorySubCategoryDebitNoteData();
                                categorySubCategoryDebitNoteData.setCategoryId(note.getCategoryId());
                                categorySubCategoryDebitNoteData.setSubCategoryId(note.getSubCategoryId());
                                categorySubCategoryDebitNoteData.setAmount(note.getAmount());
                                categorySubCategoryDebitNoteData.setPaymentRequestId(paymentRequestData.getId());
                                categorySubCategoryDebitNoteData.setDebitNoteId(debitNoteDetailData.getDebitNoteDetailId());
                                paymentRequestManagementDao.add(categorySubCategoryDebitNoteData, true);
                            }
                        }
                        return SCMDataConverter.convert(debitNoteDetailData,
                                masterDataCache.getEmployee(debitNoteDetailData.getGeneratedBy()),
                                masterDataCache.getEmployee(debitNoteDetailData.getLastUpdatedBy()));
                    } else {
                        throw new SumoException("Error updating paid amount.");
                    }
                } else {
                    throw new SumoException("Payment request id not valid.");
                }
            } else {
                throw new SumoException("Debit note already set for this payment request.");
            }
        } else {
            throw new SumoException("Payment request id not set.");
        }
    }

    private void logAdvancePaymentAudit(BigDecimal advanceAmount, PaymentRequestData paymentRequestData, AdvancePaymentData vendorAdvancePayment, Integer userId, String status) throws SumoException {
        AdvancePaymentAuditLogData advancePaymentAuditLogData = new AdvancePaymentAuditLogData();
        advancePaymentAuditLogData.setAmount(advanceAmount);
        advancePaymentAuditLogData.setFromAmount(vendorAdvancePayment.getAvailableAmount());
        advancePaymentAuditLogData.setFromAmount(vendorAdvancePayment.getAvailableAmount());
        if (status.equalsIgnoreCase(AdvancePaymentStatus.BLOCKED.value())) {
            advancePaymentAuditLogData.setToAmount(vendorAdvancePayment.getAvailableAmount().subtract(advanceAmount));
        } else {
            advancePaymentAuditLogData.setToAmount(vendorAdvancePayment.getAvailableAmount().add(advanceAmount));
        }
        advancePaymentAuditLogData.setPrId(paymentRequestData.getId());
        advancePaymentAuditLogData.setAdvancePaymentId(vendorAdvancePayment);
        advancePaymentAuditLogData.setStatus(status);
        advancePaymentAuditLogData.setLoggedBy(SCMUtil.getCreatedBy(masterDataCache.getEmployee(userId), userId));
        advancePaymentAuditLogData.setLoggedAt(AppUtils.getCurrentTimestamp());
        paymentRequestManagementDao.add(advancePaymentAuditLogData, true);
        if (status.equalsIgnoreCase(AdvancePaymentStatus.BLOCKED.value())) {
            vendorAdvancePayment.setAvailableAmount(vendorAdvancePayment.getAvailableAmount().subtract(advanceAmount));
            vendorAdvancePayment.setBlockedAmount(vendorAdvancePayment.getBlockedAmount().add(advanceAmount));
        } else {
            vendorAdvancePayment.setAvailableAmount(vendorAdvancePayment.getAvailableAmount().add(advanceAmount));
            vendorAdvancePayment.setBlockedAmount(vendorAdvancePayment.getBlockedAmount().subtract(advanceAmount));
        }
        if (vendorAdvancePayment.getAvailableAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new SumoException("Available Advance Amount is going into Negative..!", "Used Amount is : " + advanceAmount + " and available amount is : " + vendorAdvancePayment.getAvailableAmount());
        }
        if (vendorAdvancePayment.getBlockedAmount().compareTo(vendorAdvancePayment.getPrAmount()) > 0) {
            throw new SumoException("Blocked Amount is Exceeding Pr Amount!", "Used Amount is : " + advanceAmount + " and available amount is : " + vendorAdvancePayment.getAvailableAmount());
        }
        vendorAdvancePayment.setLastUpdatedDate(AppUtils.getCurrentTimestamp());
        vendorAdvancePayment.setLastUpdatedBy(userId);
        paymentRequestManagementDao.update(vendorAdvancePayment, true);
    }

    @Override
    public void markAdjustedSoPoRejected(AdvancePaymentData vendorAdvancePayment, Integer userId, String statusValue, AdvancePaymentData childAdvance) throws SumoException {
        AdvancePaymentData advancePaymentData = Objects.nonNull(childAdvance) ? childAdvance : paymentRequestManagementDao.getVendorAdjustedAdvance(vendorAdvancePayment);
        String prevStatus = advancePaymentData.getAdvanceStatus();
        if (statusValue.equalsIgnoreCase(AdvancePaymentStatus.CANCELLED.value())) {
            advancePaymentData.setAdvanceStatus(AdvancePaymentStatus.ADJUST_CANCELLED.value());
        } else {
            advancePaymentData.setAdvanceStatus(AdvancePaymentStatus.ADJUST_REJECTED.value());
        }
        String msg = null;
        if (advancePaymentData.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
            PurchaseOrderData purchaseOrderData = advancePaymentData.getPurchaseOrderData();
            msg = "ADJUST_CANCELLED_FOR_PO_ID_" + purchaseOrderData.getId();
            advancePaymentData.setPurchaseOrderData(null);
            paymentRequestManagementDao.update(purchaseOrderData, true);
        }
        if (advancePaymentData.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
            ServiceOrderData serviceOrderData = advancePaymentData.getServiceOrderData();
            msg = "ADJUST_CANCELLED_FOR_SO_ID_" + serviceOrderData.getId();
            advancePaymentData.setServiceOrderData(null);
            paymentRequestManagementDao.update(serviceOrderData, true);
        }
        advancePaymentData.setRejectedFor(msg);
        advancePaymentData.setLastUpdatedBy(userId);
        advancePaymentData.setLastUpdatedDate(AppUtils.getCurrentTimestamp());
        logAdvancePaymentStatus(prevStatus, advancePaymentData.getAdvanceStatus(), advancePaymentData, userId);
        paymentRequestManagementDao.update(advancePaymentData, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<DebitNoteDetail> getDebitNotes(Integer vendorId, Integer unitId, Integer prId, Integer dnId,
                                               String invoiceNumber, Date startDate, Date endDate, Integer companyId, String status) throws SumoException {
        invoiceNumber = (invoiceNumber != null && !invoiceNumber.trim().isEmpty()) ? invoiceNumber : null;
        List<DebitNoteDetailData> debitNoteDetailDataList = paymentRequestManagementDao.getDebitNotes(vendorId, unitId,
                prId, dnId, invoiceNumber, startDate, endDate, companyId, status);
        List<DebitNoteDetail> debitNoteDetails = new ArrayList<>();
        debitNoteDetailDataList.stream().forEach(debitNoteDetailData -> {
            debitNoteDetails.add(SCMDataConverter.convert(debitNoteDetailData,
                    masterDataCache.getEmployee(debitNoteDetailData.getGeneratedBy()),
                    masterDataCache.getEmployee(debitNoteDetailData.getLastUpdatedBy())));
        });
        return debitNoteDetails;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DebitNoteDetail settleDebitNote(DebitNoteDetail detail) throws SumoException {
        if (detail != null && detail.getDebitNoteId() != null) {
            DebitNoteDetailData debitNoteDetailData = paymentRequestManagementDao.find(DebitNoteDetailData.class,
                    detail.getDebitNoteId());
            if (debitNoteDetailData != null) {
                Date currentTime = SCMUtil.getCurrentTimestamp();
                debitNoteDetailData.setCreditNoteReceived(SCMUtil.setStatus(true));
                debitNoteDetailData.setCreditNoteReceivingTime(currentTime);
                debitNoteDetailData.setLastUpdatedBy(detail.getLastUpdatedBy().getId());
                debitNoteDetailData.setUpdateTime(currentTime);
                debitNoteDetailData = paymentRequestManagementDao.update(debitNoteDetailData, true);
                if (debitNoteDetailData != null) {
                    return SCMDataConverter.convert(debitNoteDetailData,
                            masterDataCache.getEmployee(debitNoteDetailData.getGeneratedBy()),
                            masterDataCache.getEmployee(debitNoteDetailData.getLastUpdatedBy()));
                } else {
                    throw new SumoException("Error updating debit note.");
                }
            } else {
                throw new SumoException("Debit note id is not valid.");
            }
        } else {
            throw new SumoException("Provide correct debit note id.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PaymentCalendar> getPaymentDatesForPaymentRequest(Integer paymentRequestId) throws SumoException {
        if (paymentRequestId != null) {
            PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class,
                    paymentRequestId);
            if (paymentRequestData != null) {
                VendorDetailData vendorDetailData = paymentRequestManagementDao.find(VendorDetailData.class,
                        paymentRequestData.getVendorId());
                if (vendorDetailData != null && vendorDetailData.getCompanyDetails() != null
                        && vendorDetailData.getCompanyDetails().getCreditDays() != null) {
                    Integer cycleTag = vendorDetailData.getCompanyDetails().getCreditDays() == 30 ? 1 : 0;
                    Date prDate = paymentRequestData.getCreationTime();
                    PaymentInvoiceData paymentInvoiceData = paymentRequestManagementDao
                            .findInvoiceByPaymentRequestId(paymentRequestId);
                    if (paymentInvoiceData != null) {
                        Date invoiceDate = paymentInvoiceData.getInvoiceDate();
                        Date financialCalendarStart = props.getFinancialCalendarStart();
                        if (invoiceDate.compareTo(financialCalendarStart) < 0) {
                            throw new SumoException("Invoice date is not valid.");
                        }
                        List<PaymentCalendarData> paymentCalendarDataList = paymentRequestManagementDao
                                .getPaymentDatesFromCalendar(cycleTag, prDate, invoiceDate,
                                        paymentRequestData.getCompanyId());
                        return paymentCalendarDataList.stream().map(paymentCalendarData -> {
                            return SCMDataConverter.convert(paymentCalendarData, false);
                        }).collect(Collectors.toList());
                    } else {
                        throw new SumoException("Payment request is not valid");
                    }
                } else {
                    throw new SumoException("Vendor credit days not found.");
                }
            } else {
                throw new SumoException("Payment request id is not valid.");
            }
        } else {
            throw new SumoException("Provide payment request id.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public PaymentSheetReturnVO getPaymentSheet(PaymentSheetVO paymentSheetVO) throws SumoException {
        List<PaymentRequest> paymentRequests = new ArrayList<>();
        Map<Integer, VendorDetail> vendors = new TreeMap<>();
        paymentSheetVO.getPaymentRequestIds().stream().forEach(prId -> {
            PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class, prId);
            if (!vendors.containsKey(paymentRequestData.getVendorId())) {
                VendorDetailData vendorDetailData = paymentRequestManagementDao.find(VendorDetailData.class,
                        paymentRequestData.getVendorId());
                VendorDetail vendorDetail = SCMDataConverter.convertVendor(vendorDetailData,
                        masterDataCache.getEmployee(vendorDetailData.getUpdatedBy()),
                        masterDataCache.getEmployee(vendorDetailData.getRequestedBy()));
                vendors.put(vendorDetail.getVendorId(), vendorDetail);
            }
            VendorDetail vendor = vendors.get(paymentRequestData.getVendorId());
            BigDecimal debitBalance = getDebitBalance(vendor);
            if (!vendor.isPaymentBlocked()) {
                String createdBy = masterDataCache.getEmployee(paymentRequestData.getCreatedBy());
                String requestingUnit = null;
                if (paymentRequestData.getRequestingUnit() != null) {
                    requestingUnit = scmCache.getUnitDetails().get(paymentRequestData.getRequestingUnit())
                            .getUnitName();
                }
                List<VendorDebitBalanceVO> vendorDebitBalanceVOS = vendor.getVos();
                PaymentRequestMetaData prMetaData = paymentRequestManagementDao.getPaymentRequestMetaDataForPr(paymentRequestData);
                PaymentInvoiceData paymentInvoiceData = null;
                BigDecimal rcmPaidAmount = null;
                if(prMetaData!=null && Objects.equals(prMetaData.getIsRCM(),"Y")){
                   paymentInvoiceData = paymentRequestManagementDao.findInvoiceByPaymentRequestId(prId);
                   BigDecimal totalAmount  = BigDecimal.ZERO;
                   for(PaymentInvoiceItemData invoice : paymentInvoiceData.getPaymentInvoiceItemData()){
                            totalAmount = totalAmount.add(invoice.getTotalPrice());
                   }

                   BigDecimal rate = BigDecimal.valueOf(prMetaData.getTdsPercentage()/100);
                   BigDecimal tax = totalAmount.multiply(rate);
                   rcmPaidAmount = totalAmount.subtract(tax);
                }

                PaymentRequest paymentRequest = SCMDataConverter.convert(paymentRequestData, paymentInvoiceData, null, createdBy, requestingUnit,
                        "", null, null, debitBalance, vendor.getCompanyDetails().getCreditDays(), vendorDebitBalanceVOS,
                        scmCache, new ArrayList<>());
                if (Objects.nonNull(paymentRequestData.getAdvancePaymentData())) {
                    paymentRequest.setAdvancePaymentId(paymentRequestData.getAdvancePaymentData().getAdvancePaymentId());
                }
                if(rcmPaidAmount!=null){
                    paymentRequest.setPaidAmount(rcmPaidAmount);
                }
                paymentRequests.add(paymentRequest);

            }
        });
        PaymentSheetReturnVO paymentSheetReturnVO = new PaymentSheetReturnVO();
        paymentSheetReturnVO.setPaymentRequests(paymentRequests);
        paymentSheetReturnVO.setVendors(vendors);
        return paymentSheetReturnVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean unblockPaymentRequests(PaymentSheetVO paymentSheetVO) throws SumoException {
        if (paymentSheetVO != null) {
            Date currentTimestamp = SCMUtil.getCurrentTimestamp();
            for (Integer paymentRequestId : paymentSheetVO.getPaymentRequestIds()) {
                PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class,
                        paymentRequestId);
                if (paymentRequestData != null && paymentRequestData.getIsBlocked().equals(SCMUtil.setStatus(true))) {
                    paymentRequestData.setIsBlocked(SCMUtil.setStatus(false));
                    paymentRequestData.setLastUpdated(currentTimestamp);
                    paymentRequestData = paymentRequestManagementDao.update(paymentRequestData, true);
                    if (paymentRequestData != null) {
                        String log = "Payment request priority unblock by "
                                + masterDataCache.getEmployee(paymentSheetVO.getUpdatedBy()) + "["
                                + paymentSheetVO.getUpdatedBy() + "] reason: " + paymentSheetVO.getReason();
                        logPaymentRequestData(log, paymentRequestId);
                    } else {
                        throw new SumoException("Payment request id " + paymentRequestId + " could not be updated.");
                    }
                } else {
                    throw new SumoException("Payment request id " + paymentRequestId + " is invalid");
                }
            }
        } else {
            throw new SumoException("Request body is null.");
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<BankVO> getBanksOfComapny(int companyId) throws SumoException {
        List<BankVO> codeNames = new ArrayList<>();
        List<CompanyBankMapping> bankMappings = paymentRequestManagementDao.getBanksOfComapny(companyId, null);
        if (bankMappings == null) {
            throw new SumoException("Account details not available of any bank for this company: "
                    + masterDataCache.getCompany(companyId).getName());
        }
        for (CompanyBankMapping mapping : bankMappings) {
            BankVO name = new BankVO();
            name.setName(mapping.getBankName());
            name.setCode(mapping.getBankCode());
            name.setAccountNumber(mapping.getBankAccountNo());
            name.setId(companyId);
            codeNames.add(name);
        }
        return codeNames;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CompanyBankMapping getBanksOfComapny(int companyId, String bankCode) throws SumoException {
        List<CompanyBankMapping> bankMappings = paymentRequestManagementDao.getBanksOfComapny(companyId, bankCode);
        if (bankMappings == null) {
            throw new SumoException("Account details not available of any bank for this company: "
                    + masterDataCache.getCompany(companyId).getName());
        }

        return bankMappings.get(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean blockPaymentRequests(PaymentSheetVO paymentSheetVO) throws SumoException {
        if (paymentSheetVO != null) {
            Date currentTimestamp = SCMUtil.getCurrentTimestamp();
            for (Integer paymentRequestId : paymentSheetVO.getPaymentRequestIds()) {
                PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class,
                        paymentRequestId);
                if (paymentRequestData != null) {
                    if (paymentRequestData.getIsBlocked().equals(SCMUtil.setStatus(false))
                            && !PaymentRequestStatus.PAID.equals(paymentRequestData.getCurrentStatus())
                            && !PaymentRequestStatus.SENT_FOR_PAYMENT.equals(paymentRequestData.getCurrentStatus())) {
                        paymentRequestData.setIsBlocked(SCMUtil.setStatus(true));
                        paymentRequestData.setLastUpdated(currentTimestamp);
                        paymentRequestData = paymentRequestManagementDao.update(paymentRequestData, true);
                        if (paymentRequestData != null) {
                            String log = "Payment request priority block by "
                                    + masterDataCache.getEmployee(paymentSheetVO.getUpdatedBy()) + "["
                                    + paymentSheetVO.getUpdatedBy() + "] reason: " + paymentSheetVO.getReason();
                            logPaymentRequestData(log, paymentRequestId);
                        } else {
                            throw new SumoException(
                                    "Payment request id " + paymentRequestId + " could not be updated.");
                        }
                    } else {
                        throw new SumoException("Payment request id " + paymentRequestId + " cannot be blocked");
                    }
                } else {
                    throw new SumoException("Payment request id " + paymentRequestId + " is invalid");
                }
            }
        } else {
            throw new SumoException("Request body is null.");
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean blockAllPRsForVendor(VendorEditVO request) throws SumoException {
        if (request != null && request.getVendorId() != 0) {
            Date currentTimestamp = SCMUtil.getCurrentTimestamp();
            List<PaymentRequestData> paymentRequestDataList = paymentRequestManagementDao
                    .findUnfinishedPaymentsForVendor(request.getVendorId());
            for (PaymentRequestData paymentRequestData : paymentRequestDataList) {
                if (!SCMUtil.getStatus(paymentRequestData.getIsBlocked())) {
                    paymentRequestData.setIsBlocked(SCMUtil.setStatus(true));
                    paymentRequestData.setLastUpdated(currentTimestamp);
                    paymentRequestData = paymentRequestManagementDao.update(paymentRequestData, true);
                    if (paymentRequestData != null) {
                        String log = "Payment request blocked by " + masterDataCache.getEmployee(request.getUserId())
                                + "[" + request.getUserId() + "] reason: " + (Objects.nonNull(request.getBlockedReason()) ? request.getBlockedReason() : "blocked vendor.");
                        logPaymentRequestData(log, paymentRequestData.getId());
                    } else {
                        throw new SumoException(
                                "Payment request id " + paymentRequestData.getId() + " could not be blocked.");
                    }
                }
            }
        } else {
            throw new SumoException("Vendor should not be null.");
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean unBlockAllPRsForVendor(VendorEditVO request) throws SumoException {
        if (request != null && request.getVendorId() != 0) {
            Date currentTimestamp = SCMUtil.getCurrentTimestamp();
            List<PaymentRequestData> paymentRequestDataList = paymentRequestManagementDao
                    .findUnfinishedPaymentsForVendor(request.getVendorId());
            for (PaymentRequestData paymentRequestData : paymentRequestDataList) {
                if (SCMUtil.getStatus(paymentRequestData.getIsBlocked())) {
                    paymentRequestData.setIsBlocked(SCMUtil.setStatus(false));
                    paymentRequestData.setLastUpdated(currentTimestamp);
                    paymentRequestData = paymentRequestManagementDao.update(paymentRequestData, true);
                    String log = "Payment request un-blocked by " + masterDataCache.getEmployee(request.getUserId())
                            + "[" + request.getUserId() + "] reason: un-blocked vendor.";
                    logPaymentRequestData(log, paymentRequestData.getId());
                }
            }
        } else {
            throw new SumoException("Vendor should not be null.");
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean settlePaymentRequestSingle(PRPaymentDetail detail) throws SumoException, EmailGenerationException {
        if (detail != null) {
            if (detail.getPaymentRequests().size() > 0) {
                Date currentTimestamp = SCMUtil.getCurrentTimestamp();
                BigDecimal proposedAmount = BigDecimal.ZERO;
                PRPaymentDetailData prPaymentDetailData = new PRPaymentDetailData();
                prPaymentDetailData.setBeneficiaryAccountNumber(detail.getBeneficiaryAccountNumber());
                prPaymentDetailData.setBeneficiaryIFSCode(detail.getBeneficiaryIfscCode());
                prPaymentDetailData.setDebitAccountNumber(detail.getDebitAccount());
                prPaymentDetailData.setDebitBank(detail.getDebitBank());
                prPaymentDetailData.setPaidAmount(detail.getPaidAmount());
                prPaymentDetailData.setPaymentDate(detail.getPaymentDate());
                prPaymentDetailData.setPaymentType(detail.getPaymentType().value());
                prPaymentDetailData.setRemarks(detail.getRemarks());
                prPaymentDetailData.setVendorId(detail.getVendorId());
                prPaymentDetailData.setVendorName(detail.getVendorName());
                prPaymentDetailData.setCreatedBy(detail.getCreatedBy().getId());
                prPaymentDetailData.setProposedAmount(detail.getProposedAmount());
                prPaymentDetailData.setUtrNumber(detail.getUtrNumber());
                prPaymentDetailData.setVendorPaymentDate(detail.getVendorPaymentDate());
                prPaymentDetailData.setUtrUploadedBy(detail.getCreatedBy().getName() + "[" + detail.getCreatedBy().getId() + "]");
                prPaymentDetailData.setUtrUploadedTime(currentTimestamp);
                prPaymentDetailData.setIsForcedUtrUpload(AppConstants.NO);

                prPaymentDetailData = paymentRequestManagementDao.add(prPaymentDetailData, true);
                if (prPaymentDetailData != null) {
                    for (PaymentRequest paymentRequest : detail.getPaymentRequests()) {
                        PaymentRequestData paymentRequestData = paymentRequestManagementDao
                                .find(PaymentRequestData.class, paymentRequest.getPaymentRequestId());
                        if (paymentRequestData != null && PaymentRequestStatus.SENT_FOR_PAYMENT.value()
                                .equals(paymentRequestData.getCurrentStatus())) {
                            proposedAmount = SCMUtil.add(proposedAmount, paymentRequestData.getPaidAmount());
                            PaymentRequestStatusChangeVO paymentRequestStatusChangeVO = new PaymentRequestStatusChangeVO();
                            paymentRequestStatusChangeVO.setPaymentRequestId(paymentRequestData.getId());
                            paymentRequestStatusChangeVO.setUpdatedBy(detail.getCreatedBy().getId());
                            paymentRequestStatusChangeVO.setNewStatus(PaymentRequestStatus.PAID);
                            paymentRequestStatusChangeVO.setCurrentStatus(PaymentRequestStatus.SENT_FOR_PAYMENT);
                            changePaymentRequestStatus(paymentRequestStatusChangeVO);
                            paymentRequestData.setPrPaymentDetailData(prPaymentDetailData);
                            paymentRequestData.setLastUpdated(currentTimestamp);
                            paymentRequestData = paymentRequestManagementDao.update(paymentRequestData, true);
                            if (paymentRequestData == null) {
                                throw new SumoException("Error updating payment id in payment request.");
                            }
                        }
                    }
                    if (!SCMUtil.isEqual(proposedAmount, prPaymentDetailData.getProposedAmount())) {
                        throw new SumoException("Proposed amount is not valid");
                    }
                } else {
                    throw new SumoException("Error creating payment detail.");
                }
            }
        } else {
            throw new SumoException("Request body is null.");
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean startPaymentProcessAdhoc(PaymentRequestStatusChangeVO changeRequest) throws SumoException {
        if (changeRequest.getPaymentRequestId() != null) {
            PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class,
                    changeRequest.getPaymentRequestId());
            if (paymentRequestData != null
                    && PaymentRequestStatus.APPROVED.value().equals(paymentRequestData.getCurrentStatus())) {
                changeRequest.setCurrentStatus(PaymentRequestStatus.fromValue(paymentRequestData.getCurrentStatus()));
                if (validStatusChange(changeRequest)) {
                    paymentRequestData.setCurrentStatus(PaymentRequestStatus.SENT_FOR_PAYMENT.value());
                    paymentRequestData.setPaidAdhoc(SCMUtil.setStatus(true));
                    paymentRequestData.setLastUpdated(SCMUtil.getCurrentTimestamp());
                    paymentRequestData = paymentRequestManagementDao.update(paymentRequestData, true);
                    if (paymentRequestData != null) {
                        String logData = "Payment request id: " + paymentRequestData.getId()
                                + " adhoc sent for payment by: "
                                + masterDataCache.getEmployee(changeRequest.getUpdatedBy()) + "["
                                + changeRequest.getUpdatedBy() + "] reason: " + changeRequest.getReason();
                        logPaymentRequestData(logData, paymentRequestData.getId());
                        changeRequest = logPaymentRequestStatusChange(changeRequest);
                        return changeRequest.isUpdated();
                    } else {
                        throw new SumoException("Failed to change payment request status from "
                                + changeRequest.getCurrentStatus() + " to " + changeRequest.getNewStatus() + ".");
                    }
                } else {
                    throw new SumoException("Payment request status change from " + changeRequest.getCurrentStatus()
                            + " to " + changeRequest.getNewStatus() + " not allowed.");
                }
            } else {
                throw new SumoException("Payment request id " + changeRequest.getPaymentRequestId() + " is not valid.");
            }
        } else {
            throw new SumoException("Payment request id is required.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean forceSettlePaymentRequests(PaymentSheetVO request) throws SumoException {
        if (request != null) {
            if (request.getPaymentRequestIds().size() > 0) {
                List<PaymentRequestData> paymentRequestDataList = paymentRequestManagementDao.getPaymentRequestsWithPrIds(request.getPaymentRequestIds());
                validateAdvancePaymentsForForceClose(paymentRequestDataList);
                for (PaymentRequestData paymentRequestData : paymentRequestDataList) {
                    if (paymentRequestData != null
                            && PaymentRequestStatus.APPROVED.name().equals(paymentRequestData.getCurrentStatus())) {
                        PaymentRequestStatusChangeVO changeRequest = new PaymentRequestStatusChangeVO();
                        changeRequest
                                .setCurrentStatus(PaymentRequestStatus.valueOf(paymentRequestData.getCurrentStatus()));
                        changeRequest.setNewStatus(PaymentRequestStatus.FORCE_CLOSED);
                        changeRequest.setUpdatedBy(request.getUpdatedBy());
                        changeRequest.setPaymentRequestId(paymentRequestData.getId());
                        changeRequest.setReason(request.getReason());

                        paymentRequestData.setCurrentStatus(changeRequest.getNewStatus().value());
                        paymentRequestData.setLastUpdated(SCMUtil.getCurrentTimestamp());
                        paymentRequestData = paymentRequestManagementDao.update(paymentRequestData, true);
                        if (paymentRequestData != null) {
                            String logData = "Payment request id: " + paymentRequestData.getId() + " force closed by: "
                                    + masterDataCache.getEmployee(changeRequest.getUpdatedBy()) + "["
                                    + changeRequest.getUpdatedBy() + "] reason: " + changeRequest.getReason();
                            logPaymentRequestData(logData, paymentRequestData.getId());
                            changeRequest = logPaymentRequestStatusChange(changeRequest);
                            if (!changeRequest.isUpdated()) {
                                throw new SumoException("Failed to change payment request id: " + paymentRequestData.getId() + " status from "
                                        + changeRequest.getCurrentStatus() + " to " + changeRequest.getNewStatus()
                                        + ".");
                            }
                        } else {
                            throw new SumoException("Failed to change payment request id: " + paymentRequestData.getId() + " status from "
                                    + changeRequest.getCurrentStatus() + " to " + changeRequest.getNewStatus() + ".");
                        }
                    } else {
                        throw new SumoException("Payment request id : " + paymentRequestData.getId() + " is not valid.");
                    }
                }
            }
        } else {
            throw new SumoException("Request body is null.");
        }
        return true;
    }

    private void validateAdvancePaymentsForForceClose(List<PaymentRequestData> paymentRequestDataList) throws SumoException {
        StringBuilder msg = new StringBuilder();
        for (PaymentRequestData paymentRequestData : paymentRequestDataList) {
            if (Objects.nonNull(paymentRequestData.getAdvanceAmount()) && paymentRequestData.getAdvanceAmount().compareTo(BigDecimal.ZERO) > 0) {
                msg.append("PR ID : ").append(paymentRequestData.getId()).append(" ( Rs. ").append(paymentRequestData.getAdvanceAmount()).append(" ) <br>");
            }
        }
        if (!msg.toString().equalsIgnoreCase("") && msg.toString().length() > 0) {
            msg.append("<b>PR's with Advance Payment Cannot be FORCE CLOSED..!</b>");
            throw new SumoException("Advance Prs Found...!", msg.toString());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PaymentRequest> uploadPaymentSheet(String bankName, MultipartFile file)
            throws IOException, SumoException {
        InputStream excelFile = file.getInputStream();
        Workbook workbook = new XSSFWorkbook(excelFile);
        Sheet dataTypeSheet = workbook.getSheetAt(0);
        Row headerRow = dataTypeSheet.getRow(0);
        Integer prIdCellIndex = 0;
        Integer utrCellIndex = 0;
        Integer actualDateIndex = 0;
        List<Integer> dateFormatExceptions = new ArrayList<>();
        if (bankName.equals(PaymentBanks.YES_BANK.name())) {
            prIdCellIndex = 5;
            utrCellIndex = 6;
            actualDateIndex = 7;
        }
        if (bankName.equals(PaymentBanks.KOTAK.name())) {
            prIdCellIndex = 17;
            utrCellIndex = 18;
            actualDateIndex = 19;
        }
        if (bankName.equals(PaymentBanks.KOTAK_V2.name())) {
            prIdCellIndex = 25;
            utrCellIndex = 26;
            actualDateIndex = 27;
        }
        if (bankName.equals(PaymentBanks.HDFC.name())) {
            prIdCellIndex = 13;
            utrCellIndex = 29;
            actualDateIndex = 30;
        }
        if (bankName.equals(PaymentBanks.ICICI.name())) {
            prIdCellIndex = 15;
            utrCellIndex = 21;
            actualDateIndex = 22;
        }
        if ((headerRow.getCell(prIdCellIndex).getStringCellValue().equals("PR Id") || headerRow.getCell(prIdCellIndex).getStringCellValue().equals("PR NUMBER") || headerRow.getCell(prIdCellIndex).getStringCellValue().equals("Customer Reference Number(PR Id)"))
                && (headerRow.getCell(utrCellIndex).getStringCellValue().equals("UTR Number") || headerRow.getCell(utrCellIndex).getStringCellValue().equals("Utr Number"))) {
            List<PaymentRequest> paymentRequests = new ArrayList<>();
            for (int i = 1; i <= dataTypeSheet.getLastRowNum(); i++) {
                Row row = dataTypeSheet.getRow(i);
                PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class,
                        (int) row.getCell(prIdCellIndex).getNumericCellValue());
                if (PaymentRequestStatus.SENT_FOR_PAYMENT.value().equals(paymentRequestData.getCurrentStatus())) {
                    PaymentRequest paymentRequest = new PaymentRequest();
                    paymentRequest.setPaymentRequestId(paymentRequestData.getId());
                    paymentRequest.setInvoiceNumber(paymentRequestData.getInvoiceNumber());
                    paymentRequest.setVendorPaymentDate(paymentRequestData.getVendorPaymentDate());
                    PRPaymentDetail prPaymentDetail = new PRPaymentDetail();
                    try {

                        if (row.getCell(actualDateIndex).getDateCellValue() != null) {
                            prPaymentDetail.setActualDate(row.getCell(actualDateIndex).getDateCellValue());
                        } else {
                            dateFormatExceptions.add(paymentRequest.getPaymentRequestId());
                        }

                    } catch (Exception e) {
                        LOG.error("Error occurred while reading Actual Date", e);
                        dateFormatExceptions.add(paymentRequest.getPaymentRequestId());
                    }
                    prPaymentDetail.setUtrNumber(row.getCell(utrCellIndex).getStringCellValue());
                    if (PaymentBanks.ICICI.name().equals(bankName)) {
                        prPaymentDetail.setBeneficiaryIfscCode(
                                row.getCell(getColumnIndexFromSheet(bankName, "IFSC")).getStringCellValue());
                        prPaymentDetail.setBeneficiaryAccountNumber(
                                row.getCell(getColumnIndexFromSheet(bankName, "Beneficiary Ac No")).getStringCellValue());
                        prPaymentDetail.setVendorName(
                                row.getCell(getColumnIndexFromSheet(bankName, "Beneficiary Name")).getStringCellValue());
                        prPaymentDetail.setPaidAmount(new BigDecimal(
                                row.getCell(getColumnIndexFromSheet(bankName, "Amt")).getNumericCellValue()));
                        prPaymentDetail.setProposedAmount(paymentRequestData.getProposedAmount());
                        prPaymentDetail.setDebitBank(bankName);
                    } else {
                        prPaymentDetail.setBeneficiaryIfscCode(
                                row.getCell(getColumnIndexFromSheet(bankName, "BENEFICIARY_IFSC")).getStringCellValue());
                        prPaymentDetail.setBeneficiaryAccountNumber(
                                row.getCell(getColumnIndexFromSheet(bankName, "BENEFICIARY_ACCOUNT")).getStringCellValue());
                        prPaymentDetail.setVendorName(
                                row.getCell(getColumnIndexFromSheet(bankName, "BENEFICIARY_NAME")).getStringCellValue());
                        prPaymentDetail.setPaidAmount(new BigDecimal(
                                row.getCell(getColumnIndexFromSheet(bankName, "AMOUNT")).getNumericCellValue()));
                        prPaymentDetail.setProposedAmount(paymentRequestData.getProposedAmount());
                        prPaymentDetail.setDebitBank(bankName);
                    }
                    if ((PaymentBanks.KOTAK.name()).equals(bankName) || (PaymentBanks.KOTAK_V2.name()).equals(bankName) || PaymentBanks.HDFC.name().equals(bankName) || PaymentBanks.ICICI.name().equals(bankName)) {
                        if (PaymentBanks.ICICI.name().equals(bankName)) {
                            prPaymentDetail.setDebitAccount(row
                                    .getCell(getColumnIndexFromSheet(bankName, "Debit Ac No")).getStringCellValue());
                        } else {
                            prPaymentDetail.setDebitAccount(row
                                    .getCell(getColumnIndexFromSheet(bankName, "DEBIT_ACC_NUMBER")).getStringCellValue());
                        }
                        if (PaymentBanks.HDFC.name().equals(bankName)) {
                            prPaymentDetail.setPaymentType(PaymentType.fromValue(
                                    row.getCell(getColumnIndexFromSheet(bankName, "PAYMENT_TYPE")).getStringCellValue().equalsIgnoreCase("N") ?
                                            "NEFT" : "I"));
                        } else if (PaymentBanks.ICICI.name().equals(bankName)) {
                            prPaymentDetail.setPaymentType(PaymentType.fromValue(
                                    row.getCell(getColumnIndexFromSheet(bankName, "Pay Mod")).getStringCellValue().equalsIgnoreCase("N") ?
                                            "NEFT" : row.getCell(getColumnIndexFromSheet(bankName, "Pay Mod")).getStringCellValue().equalsIgnoreCase("R") ? "RTGS" : "I"));
                        } else {
                            prPaymentDetail.setPaymentType(PaymentType.fromValue(
                                    row.getCell(getColumnIndexFromSheet(bankName, "PAYMENT_TYPE")).getStringCellValue()));
                        }
                    }
                    paymentRequest.setPaymentDetail(prPaymentDetail);
                    if (paymentRequest.getPaymentDetail().getUtrNumber() != null
                            && !paymentRequest.getPaymentDetail().getUtrNumber().isEmpty()) {
                        paymentRequests.add(paymentRequest);
                    }
                }
            }

            if (dateFormatExceptions.size() > 0) {
                String message = "Cannot Find Actual Date or Format of date in the payment sheet for PR Ids : " + "\n"
                        + Arrays.toString(dateFormatExceptions.toArray()) +
                        " . Please enter Actual Date in dd-mm-yyyy format eg: (" + SCMUtil.getFormattedTime(SCMUtil.getCurrentTimestamp(), "dd-MM-yyyy") + ")" +
                        ".(date-month-year)";
                throw new SumoException("Error in Actual Date or Format of Actual Date(dd-mm-yyyy)",
                        message);
            }
            return paymentRequests;
        } else {
            throw new SumoException("Sheet is not valid");
        }
    }

    private BigDecimal getTdsAmount(PaymentInvoiceData invoiceData) {

        BigDecimal tdsAmount = BigDecimal.ZERO;
        BigDecimal denominator = BigDecimal.valueOf(100);

        for (PaymentInvoiceItemData item : invoiceData.getPaymentInvoiceItemData()) {
            if (item.getTdsRate() != null) {
                BigDecimal amount = SCMUtil.multiplyWithScale10(item.getTotalPrice(), item.getTdsRate());
                BigDecimal res = SCMUtil.divideWithScale10(amount, denominator);
                tdsAmount = SCMUtil.add(tdsAmount, res);
            }
        }
        return tdsAmount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public AdvancePaymentStatusLog logAdvancePaymentStatus(String fromStatus, String toStatus, AdvancePaymentData advancePaymentData, Integer loggedBy) throws SumoException {
        AdvancePaymentStatusLog advancePaymentStatusLog = new AdvancePaymentStatusLog();
        advancePaymentStatusLog.setAdvancePaymentData(advancePaymentData);
        advancePaymentStatusLog.setFromStatus(fromStatus);
        advancePaymentStatusLog.setToStatus(toStatus);
        advancePaymentStatusLog.setLoggedBy(loggedBy);
        advancePaymentStatusLog.setLoggedAt(AppUtils.getCurrentTimestamp());
        advancePaymentStatusLog = serviceOrderManagementDao.add(advancePaymentStatusLog, true);
        return advancePaymentStatusLog;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean settlePaymentRequestBulk(List<PaymentRequest> paymentReqs, String selectedCompany, Boolean isForceUploaded, String utrUploadedBy)
            throws SumoException, EmailGenerationException {
        if (paymentReqs != null) {
            List<String> exceptionsList = new ArrayList<>();
            Map<String, PRPaymentDetail> paymentDetailMap = new HashMap<>();
            Map<String, PRPaymentDetailData> paymentDetailDataMap = new HashMap<>();
            ObjectMapper mapper = new ObjectMapper();
            List<PaymentRequest> paymentRequests = mapper.convertValue(paymentReqs, new TypeReference<List<PaymentRequest>>() {
            });
            Integer userId = AppConstants.SYSTEM_EMPLOYEE_ID;
            List<String> exceptionalPrs = findExceptionalPrs(paymentRequests);
            if (exceptionalPrs.size() > 0) {
                String message = "Can not Submit the payments " + "<br>" + Arrays.toString(exceptionalPrs.toArray());
                throw new SumoException("List of Errors for the uploaded Payment sheet .!",
                        message);
            }
            //List<PaymentRequest> pojos = mapper.convertValue(paymentRequests, new TypeReference<List<PaymentRequest>>() { });
            for (PaymentRequest paymentRequest : paymentRequests) {
                PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class,
                        paymentRequest.getPaymentRequestId());
                VendorDetailData vendorDetailData = paymentRequestManagementDao.find(VendorDetailData.class,
                        paymentRequestData.getVendorId());

                PaymentInvoiceData invoiceData = paymentRequestManagementDao.findInvoiceByPaymentRequestId(paymentRequest.getPaymentRequestId());
                BigDecimal tdsAmount = BigDecimal.ZERO;

                if (isForceUploaded == false) {
                    LOG.info("Inside check of is force upload");
                    Date currentDate = AppUtils.getCurrentDateIST();
                    LOG.info("current date is : {} ", currentDate);
                    Date vendorPaymentDate = paymentRequestData.getVendorPaymentDate();
                    LOG.info("Vendor payment date : {}", vendorPaymentDate);
                    int daysDifference = (int) AppUtils.getDayDifference(vendorPaymentDate, currentDate);
                    LOG.info("no of days difference is : {} ", daysDifference);
                    if (daysDifference > props.getUtrUploadThresholdDays()) {
                        String exception = " for PR ID : " + paymentRequest.getPaymentRequestId() + " last date of uploading the payment sheet is  : " + daysDifference + " days before i.e : " +
                                AppUtils.getSMSTemplateDate(SCMUtil.getPreviousBusinessDateIST(currentDate, daysDifference));
                        exceptionsList.add(exception);
                    }
                }

                if (paymentRequestData.getType().equals(PaymentRequestType.SERVICE_RECEIVED.name())) {
                    tdsAmount = getTdsAmount(invoiceData);
                }

                if (paymentRequestData != null && !SCMUtil.getStatus(paymentRequestData.getIsBlocked())) {
                    DebitNoteDetailData debitNoteDetailData = paymentRequestManagementDao
                            .findDebitNoteByPaymentRequestId(paymentRequest.getPaymentRequestId());
                    if (debitNoteDetailData != null) {
                        paymentRequest.setDebitNote(SCMDataConverter.convert(debitNoteDetailData, "", ""));
                        debitNoteDetailData.setDebitNoteStatus(PaymentRequestStatus.CREATED.value());
                        paymentRequestManagementDao.update(debitNoteDetailData, true);
                    }
                    paymentRequest.setType(PaymentRequestType.valueOf(paymentRequestData.getType()));
                    paymentRequest.setPaidAmount(paymentRequestData.getPaidAmount());
                    paymentRequest.setDuplicatePaidAmount(paymentRequestData.getDuplicatePaidAmount());
                    paymentRequest.setVendorPaymentDate(paymentRequestData.getVendorPaymentDate());
                    userId = paymentRequest.getPaymentDetail().getCreatedBy().getId();
                    PRPaymentDetail prPaymentDetail = paymentDetailMap
                            .get(paymentRequest.getPaymentDetail().getUtrNumber());
                    if (prPaymentDetail == null) {
                        PRPaymentDetail prPaymentDetail1 = paymentRequest.getPaymentDetail();
                        prPaymentDetail1.setVendorName(vendorDetailData.getEntityName());
                        prPaymentDetail1.setVendorId(vendorDetailData.getVendorId());
                        prPaymentDetail1.setProposedAmount(paymentRequestData.getPaidAmount());
                        prPaymentDetail1.setTdsAmount(tdsAmount);
                        prPaymentDetail1.setVendorPaymentDate(paymentRequestData.getVendorPaymentDate());
                        paymentRequest.setPaymentDetail(prPaymentDetail1);
                        paymentDetailMap.put(paymentRequest.getPaymentDetail().getUtrNumber(),
                                paymentRequest.getPaymentDetail());
                    } else {
                        BigDecimal proposedAmount = SCMUtil.add(prPaymentDetail.getProposedAmount(),
                                paymentRequestData.getProposedAmount());
                        BigDecimal totalTds = SCMUtil.add(prPaymentDetail.getTdsAmount(), tdsAmount);
                        prPaymentDetail.setVendorName(vendorDetailData.getEntityName());
                        prPaymentDetail.setVendorId(vendorDetailData.getVendorId());
                        prPaymentDetail.setProposedAmount(proposedAmount);
                        prPaymentDetail.setTdsAmount(totalTds);
                        prPaymentDetail.setVendorPaymentDate(paymentRequestData.getVendorPaymentDate());
                        paymentDetailMap.put(paymentRequest.getPaymentDetail().getUtrNumber(), prPaymentDetail);
                    }
                } else {
                    throw new SumoException(
                            "Payment request id: " + paymentRequest.getPaymentRequestId() + "is invalid because payment is at blocked status.");
                }
            }
            if (exceptionsList != null && !exceptionsList.isEmpty() && exceptionsList.size() > 0) {
                String message = "Error while uploading the payment sheet " + "\n"
                        + Arrays.toString(exceptionsList.toArray());
                throw new SumoException("Please Contact your manager to Upload Payment Sheet",
                        message);
            }
            savePaymentDetails(paymentDetailMap, paymentDetailDataMap, paymentRequests, isForceUploaded, utrUploadedBy, userId);
            sendPaymentNotification(paymentRequests, paymentDetailMap, selectedCompany);
            updateAdvancePayments(paymentRequests, userId);
        }
        return true;
    }

    private void updateAdvancePayments(List<PaymentRequest> paymentRequests, Integer userId) throws SumoException {
        for (PaymentRequest paymentRequest : paymentRequests) {
            PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class, paymentRequest.getPaymentRequestId());
            if (Objects.nonNull(paymentRequestData.getAdvancePaymentData())) {
                AdvancePaymentData advancePaymentMainData = paymentRequestData.getAdvancePaymentData();
                if (paymentRequestData.getType().equalsIgnoreCase(AppConstants.ADVANCE_PAYMENT)) {
                    String prevStatus = advancePaymentMainData.getAdvanceStatus();
                    advancePaymentMainData.setAdvanceStatus(AdvancePaymentStatus.CREATED.value());
                    advancePaymentMainData.setLastUpdatedBy(userId);
                    advancePaymentMainData.setLastUpdatedDate(AppUtils.getCurrentTimestamp());
                    logAdvancePaymentStatus(prevStatus, advancePaymentMainData.getAdvanceStatus(), advancePaymentMainData, userId);
                    paymentRequestManagementDao.update(advancePaymentMainData, true);
                } else {
                    List<AdvancePaymentData> advancePaymentDataList = new ArrayList<>();
                    if (paymentRequestData.getAdvancePaymentData().getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                        PurchaseOrderData purchaseOrderData = paymentRequestData.getAdvancePaymentData().getPurchaseOrderData();
                        advancePaymentDataList = purchaseOrderData.getAdvancePaymentDatas();
                    } else {
                        ServiceOrderData serviceOrderData = paymentRequestData.getAdvancePaymentData().getServiceOrderData();
                        advancePaymentDataList = serviceOrderData.getAdvancePaymentDatas();
                    }
                    List<AdvancePaymentData> notCompletedList = new ArrayList<>();
                    for (AdvancePaymentData advancePaymentData : advancePaymentDataList) {
                        if (!advancePaymentData.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.COMPLETED.value())) {
                            notCompletedList.add(advancePaymentData);
                        }
                    }
                    AdvancePaymentData advanceAdjustedData = null;
                    AdvancePaymentData refundedAdvanceData = null;

                    for (AdvancePaymentData advancePaymentData : notCompletedList) {
                        if (Objects.nonNull(advancePaymentData.getAdjustedPoSo()) || Objects.nonNull(advancePaymentData.getRefundDate())) {
                            String prevAdvStatus = advancePaymentData.getAdvanceStatus();
                            if (Objects.nonNull(advancePaymentData.getAdjustedPoSo())) {
                                advanceAdjustedData = advancePaymentData;
                            }
                            if (Objects.nonNull(advancePaymentData.getRefundDate())) {
                                advancePaymentData.setAdvanceStatus(AdvancePaymentStatus.REFUND_APPROVED.value());
                                refundedAdvanceData = advanceAdjustedData;
                            }
                            advancePaymentData.setLastUpdatedDate(AppUtils.getCurrentTimestamp());
                            advancePaymentData.setLastUpdatedBy(userId);
                            logAdvancePaymentStatus(prevAdvStatus, advancePaymentData.getAdvanceStatus(), advancePaymentData, userId);
                            if (Objects.nonNull(advanceAdjustedData)) {
                                advanceAdjustedData.setAdvanceStatus(AdvancePaymentStatus.ADJUSTED.value());
                            }
                            paymentRequestManagementDao.update(advancePaymentData, true);
                        }
                        if (Objects.isNull(advancePaymentData.getAdjustedPoSo()) && Objects.isNull(advancePaymentData.getRefundDate())) {
                            if (advancePaymentData.getAvailableAmount().compareTo(BigDecimal.ZERO) == 0 && advancePaymentData.getBlockedAmount().equals(advancePaymentData.getPrAmount()) && !advancePaymentData.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.COMPLETED.value())) {
                                String prevStatus = advancePaymentData.getAdvanceStatus();
                                advancePaymentData.setAdvanceStatus(AdvancePaymentStatus.COMPLETED.value());
                                advancePaymentData.setLastUpdatedBy(userId);
                                advancePaymentData.setLastUpdatedDate(AppUtils.getCurrentTimestamp());
                                logAdvancePaymentStatus(prevStatus, advancePaymentData.getAdvanceStatus(), advancePaymentData, userId);
                                paymentRequestManagementDao.update(advancePaymentData, true);
                            }
                        }
                        updateVendorBlockCache(advancePaymentData);
                    }

                    if (Objects.nonNull(advanceAdjustedData)) {
                        AdvancePaymentData vendorAdvancePayment = advanceAdjustedData.getChildAdvance();
                        String prevStatus = vendorAdvancePayment.getAdvanceStatus();
                        vendorAdvancePayment.setAdvanceStatus(AdvancePaymentStatus.CREATED.value());
                        vendorAdvancePayment.setPaymentRequestId(advanceAdjustedData.getPaymentRequestId());
                        logAdvancePaymentStatus(prevStatus, vendorAdvancePayment.getAdvanceStatus(), vendorAdvancePayment, userId);
                        paymentRequestManagementDao.update(vendorAdvancePayment, true);
                        if (advanceAdjustedData.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                            PurchaseOrderData purchaseOrderData = advanceAdjustedData.getPurchaseOrderData();
                            purchaseOrderService.createAutoLogsForPurchaseOrder(purchaseOrderData);
                            purchaseOrderData.setStatus(PurchaseOrderStatus.CLOSED.value());
                            purchaseOrderService.sendCLosedPoEmailNotification(purchaseOrderData, userId);
                            paymentRequestManagementDao.update(purchaseOrderData, false);
                        }
                        if (advanceAdjustedData.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
                            serviceOrderManagementService.closeServiceOrder(advanceAdjustedData.getServiceOrderData().getId(), userId);
                        }
                    }
                    if (Objects.nonNull(refundedAdvanceData)) {
                        if (refundedAdvanceData.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                            PurchaseOrderData purchaseOrderData = refundedAdvanceData.getPurchaseOrderData();
                            purchaseOrderService.createAutoLogsForPurchaseOrder(purchaseOrderData);
                            purchaseOrderData.setStatus(PurchaseOrderStatus.CLOSED.value());
                            purchaseOrderService.sendCLosedPoEmailNotification(purchaseOrderData, userId);
                            paymentRequestManagementDao.update(purchaseOrderData, false);
                        }
                        if (refundedAdvanceData.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
                            serviceOrderManagementService.closeServiceOrder(refundedAdvanceData.getServiceOrderData().getId(), userId);
                        }
                    }
                    closePendingSoPo(paymentRequestData, userId);
                }
            }
        }
    }

    private void closePendingSoPo(PaymentRequestData paymentRequestData, Integer userId) {
        try {
            if (paymentRequestData.getAdvancePaymentData().getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                PurchaseOrderData purchaseOrderData = paymentRequestData.getAdvancePaymentData().getPurchaseOrderData();
                checkCompleteGoodsReceive(purchaseOrderData, userId);
            } else {
                ServiceOrderData serviceOrderData = paymentRequestData.getAdvancePaymentData().getServiceOrderData();
                checkForCompleteServiceReceive(serviceOrderData, userId);
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While closePendingSoPo ::: ", e);
        }
    }

    private void checkCompleteGoodsReceive(PurchaseOrderData purchaseOrderData, Integer userId) throws SumoException {
        boolean isFulfilled = purchaseOrderService.checkIfFulfilled(purchaseOrderData);
        if (isFulfilled) {
            boolean updated = purchaseOrderService.generatePOStatusEvent(purchaseOrderData, PurchaseOrderStatus.CLOSED, userId);
            if (updated) {
                purchaseOrderData.setStatus(PurchaseOrderStatus.CLOSED.name());
            }
            purchaseOrderData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
            purchaseOrderData.setLastUpdatedBy(userId);
            goodsReceiveManagementDao.update(purchaseOrderData, true);
        } else {
            LOG.info("Complete Receiving is not done on the PO : {}", purchaseOrderData.getId());
        }
    }

    private void checkForCompleteServiceReceive(ServiceOrderData serviceOrderData, Integer userId) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (ServiceOrderItemData i : serviceOrderData.getServiceOrderItemDataList()) {
            BigDecimal itemRecievedCost = SCMUtil.multiplyWithScale10(i.getReceivedQuantity(), i.getUnitPrice());
            BigDecimal itemRecievedTax = SCMUtil.multiplyWithScale10(itemRecievedCost, SCMUtil.divideWithScale10(i.getTaxRate(), BigDecimal.valueOf(100)));
            BigDecimal itemTotalReceivedCost = SCMUtil.add(itemRecievedCost, itemRecievedTax);
            totalAmount = SCMUtil.add(totalAmount, itemTotalReceivedCost);
        }
        if (serviceOrderData.getTotalAmount().compareTo(totalAmount) <= 0) {
            serviceOrderManagementService.updateStatusEvent(serviceOrderData.getId(), ServiceOrderStatus.CLOSED.value(), userId);
            LOG.info("UPDATING the SO Status to CLOSED for SO id : {}", serviceOrderData.getId());
            serviceOrderData.setStatus(ServiceOrderStatus.CLOSED.value());
            serviceOrderData.setLastUpdatedBy(userId);
            serviceOrderData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
            paymentRequestManagementDao.update(serviceOrderData, true);
        } else {
            LOG.info("Complete Receiving is not done on the SO : {}", serviceOrderData.getId());
        }
    }

    @Override
    public void updateVendorBlockCache(AdvancePaymentData advancePaymentData) {
        VendorDetail vendorDetail = scmCache.getVendorDetail(advancePaymentData.getVendorId());
        if (Objects.nonNull(vendorDetail) && Objects.nonNull(vendorDetail.getBlockedReason())) {
            String[] advPaymentIdsString = vendorDetail.getBlockedReason().replaceAll("[\\[\\]]", "").split(", ");
            List<String> strList = Arrays.asList(advPaymentIdsString);
            List<Integer> advancePaymentIds = strList.stream().map(Integer::parseInt).collect(Collectors.toList());
            if (advancePaymentIds.contains(advancePaymentData.getAdvancePaymentId())) {
                refreshVendorAdvancePaymentsCache(advancePaymentData.getVendorId());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<RejectedPayments> getRejectedPrForGr(Integer unitId, Integer vendorId) throws SumoException {
        List<VendorGoodsReceivedData> vendorGoodsReceivedDataList = paymentRequestManagementDao.getRejectedPrForGr(unitId, vendorId);
        List<RejectedPayments> rejectedPaymentsList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(vendorGoodsReceivedDataList)) {
            vendorGoodsReceivedDataList.forEach(vendorGoodsReceivedData -> {
                GoodsRecievedToPaymentRequestMapping goodsRecievedToPaymentRequestMapping = paymentRequestManagementDao.getRejectedPaymentMapping(vendorGoodsReceivedData.getGoodsReceivedId());
                if (Objects.nonNull(goodsRecievedToPaymentRequestMapping)) {
                    RejectedPayments rejectedPayments = new RejectedPayments();
                    rejectedPayments.setGrId(goodsRecievedToPaymentRequestMapping.getGoodsRecievedId());
                    rejectedPayments.setPrId(goodsRecievedToPaymentRequestMapping.getPaymentRequestId());
                    rejectedPayments.setStatus(goodsRecievedToPaymentRequestMapping.getCurrentStatus());
                    rejectedPayments.setReceiptType(InvoiceDocType.valueOf(vendorGoodsReceivedData.getDocType()));
                    rejectedPayments.setCompanyId(vendorGoodsReceivedData.getCompanyId());
                    rejectedPayments.setUpdateTime(goodsRecievedToPaymentRequestMapping.getLastUpdated());
                    rejectedPaymentsList.add(rejectedPayments);
                }
            });
            return rejectedPaymentsList;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public VendorPRVO getVendorPRVO(Integer vendorId) throws SumoException {
        if (vendorId != null) {
            VendorDetailData vendorDetailData = paymentRequestManagementDao.find(VendorDetailData.class, vendorId);
            VendorPRVO vendorPRVO = new VendorPRVO();
            getDebitBalanceVOs(vendorDetailData).stream().forEach(vendorDebitBalanceVO -> {
                DebitBalanceVO vo = new DebitBalanceVO();
                vo.setCompanyId(vendorDebitBalanceVO.getCompanyId());
                vo.setCompanyName(vendorDebitBalanceVO.getCompanyName());
                vo.setDebitBalance(vendorDebitBalanceVO.getDebitBalance());
                vendorPRVO.getDebitBalances().add(vo);
            });
            vendorPRVO.setTotalDebitBalance(getDebitBalance(vendorDetailData));
            List<PaymentRequestData> paymentRequestDataList = paymentRequestManagementDao
                    .findAllSentPaymentsForVendor(vendorId);
            if (paymentRequestDataList != null) {
                paymentRequestDataList.stream().forEach(paymentRequestData -> {
                    vendorPRVO.setPaymentSentAmount(
                            SCMUtil.add(vendorPRVO.getPaymentSentAmount(), paymentRequestData.getPaidAmount()));
                });
                vendorPRVO.setPaymentSentCount(paymentRequestDataList.size());
            }
            return vendorPRVO;
        } else {
            throw new SumoException("Please provide vendor id!");
        }
    }

    private void setInvoiceDeviationMappings(PaymentRequest paymentRequest, Date currentTime) throws SumoException {
        if (paymentRequest.getPaymentInvoice().getPaymentInvoiceId() != null) {
            PaymentInvoiceData paymentInvoiceData = paymentRequestManagementDao.find(PaymentInvoiceData.class,
                    paymentRequest.getPaymentInvoice().getPaymentInvoiceId());
            if (paymentInvoiceData != null) {
                addUpdateDeviationMappings(paymentRequest.getPaymentInvoice().getDeviations(),
                        paymentInvoiceData.getId(), currentTime);
                for (PaymentInvoiceItem paymentInvoiceItem : paymentRequest.getPaymentInvoice()
                        .getPaymentInvoiceItems()) {
                    addUpdateDeviationMappings(paymentInvoiceItem.getDeviations(),
                            paymentInvoiceItem.getPaymentInvoiceItemId(), currentTime);
                }
            } else {
                throw new SumoException("Invoice details are not valid.");
            }
        }
    }

    private void addUpdateDeviationMappings(List<InvoiceDeviationMapping> invoiceDeviationMappings, Integer id,
                                            Date currentTime) throws SumoException {
        for (InvoiceDeviationMapping invoiceDeviationMapping : invoiceDeviationMappings) {
            if (invoiceDeviationMapping.getMappingId() != null) {
                updateDeviation(invoiceDeviationMapping, currentTime);
            } else {
                invoiceDeviationMapping.setDeviationItemType(PaymentDeviationLevel.INVOICE_ITEM);
                invoiceDeviationMapping.setDeviationItemId(id);
                addDeviation(invoiceDeviationMapping);
            }
        }
    }

    private void setInvoiceRejections(PaymentRequest paymentRequest, Date currentTime) throws SumoException {
        if (paymentRequest.getPaymentInvoice().getPaymentInvoiceId() != null) {
            PaymentInvoiceData paymentInvoiceData = paymentRequestManagementDao.find(PaymentInvoiceData.class,
                    paymentRequest.getPaymentInvoice().getPaymentInvoiceId());
            if (paymentInvoiceData != null) {
                for (InvoiceDeviationMapping invoiceDeviationMapping : paymentRequest.getPaymentInvoice()
                        .getRejections()) {
                    invoiceDeviationMapping.setDeviationItemType(PaymentDeviationLevel.INVOICE);
                    invoiceDeviationMapping.setDeviationItemId(paymentInvoiceData.getId());
                    addDeviation(invoiceDeviationMapping);
                }
            } else {
                throw new SumoException("Invoice details are not valid.");
            }
        }
    }

    private boolean validStatusChange(PaymentRequestStatusChangeVO changeRequest) {
        switch (changeRequest.getNewStatus()) {
            case CREATED:
                return PaymentRequestStatus.PENDING_HOD_APPROVAL.equals(changeRequest.getCurrentStatus()) || PaymentRequestStatus.QUERIED.equals(changeRequest.getCurrentStatus());
            case HOD_REJECTED:
                return PaymentRequestStatus.PENDING_HOD_APPROVAL.equals(changeRequest.getCurrentStatus());
            case ACKNOWLEDGED:
                return PaymentRequestStatus.INITIATED.equals(changeRequest.getCurrentStatus())
                        || PaymentRequestStatus.CREATED.equals(changeRequest.getCurrentStatus());
            case CANCELLED:
                return PaymentRequestStatus.INITIATED.equals(changeRequest.getCurrentStatus())
                        || PaymentRequestStatus.CREATED.equals(changeRequest.getCurrentStatus());
            case SENT_FOR_PAYMENT:
                return PaymentRequestStatus.APPROVED.equals(changeRequest.getCurrentStatus());
            case PAID:
                return PaymentRequestStatus.SENT_FOR_PAYMENT.equals(changeRequest.getCurrentStatus());
            case QUERIED:
                return PaymentRequestStatus.CREATED.equals(changeRequest.getCurrentStatus())
                        || PaymentRequestStatus.ACKNOWLEDGED.equals(changeRequest.getCurrentStatus());
            default:
                return false;
        }
    }

    private void revertLinkedGRData(PaymentRequestData paymentRequestData) throws SumoException {
        List<VendorGoodsReceivedData> vendorGoodsReceivedDataList = paymentRequestManagementDao
                .findVendorGRFromPaymentRequest(paymentRequestData.getId());
        if (CollectionUtils.isNotEmpty(vendorGoodsReceivedDataList)) {
            vendorGoodsReceivedDataList.forEach(vendorGoodsReceivedData -> {
                Boolean updateFlag = paymentRequestManagementDao.updatePrToGRMapping(paymentRequestData.getId(), vendorGoodsReceivedData, PaymentRequestStatus.REJECTED);
                if (!updateFlag) {
                    try {
                        saveGrTopaymentmapping(vendorGoodsReceivedData, PaymentRequestStatus.REJECTED);
                    } catch (SumoException e) {
                        LOG.error("Encountered error while adding Pr!", e);
                    }
                    ;
                }
            });
        }
//        paymentRequestManagementDao.updatePrToGRMapping(paymentRequestData.getId(), vendorGoodsReceivedDataList, PaymentRequestStatus.REJECTED);
        for (VendorGoodsReceivedData vendorGoodsReceivedData : vendorGoodsReceivedDataList) {
            Integer i = vendorGoodsReceivedData.getGoodsReceivedId();
            vendorGoodsReceivedData.setPaymentStatus(VendorGRPaymentStatus.ALREADY_CREATED.value());
            vendorGoodsReceivedData.setPaymentRequestData(null);
            vendorGoodsReceivedData = paymentRequestManagementDao.update(vendorGoodsReceivedData, true);
            if (vendorGoodsReceivedData == null) {
                throw new SumoException("Error removing payment request from GR id: " + i);
            }
        }
    }

    private void revertLinkedSRData(PaymentRequestData paymentRequestData) throws SumoException {
        List<ServiceReceivedData> serviceReceivedDataList = serviceReceiveManagementDao
                .findServiceReceivingForPaymentRequest(paymentRequestData.getId());
        for (ServiceReceivedData serviceReceivedData : serviceReceivedDataList) {
            Integer i = serviceReceivedData.getServiceReceivedId();
            serviceReceivedData.setPaymentStatus(VendorGRPaymentStatus.ALREADY_CREATED.value());
            serviceReceivedData.setPaymentRequestData(null);
            serviceReceivedData = paymentRequestManagementDao.update(serviceReceivedData, true);
            if (serviceReceivedData == null) {
                throw new SumoException("Error removing payment request from Service Received id: " + i);
            }
        }
    }

    private void revertMilkInvoice(Integer prId) {
        SpecializedOrderInvoiceData invoiceData = paymentRequestManagementDao.findSpecializedOrderInvoiceByPrId(prId);
        invoiceData.setIsPrRaised(AppUtils.setStatus(false));
        invoiceData.setPrId(null);
        paymentRequestManagementDao.update(invoiceData, true);

        List<InvoiceExcessQuantity> invoiceExcessQuantityList = goodsReceiveManagementDao.findPrExcessQuantity(invoiceData.getUnitId(),
                invoiceData.getSpecializedOrderInvoiceId());
        if (invoiceExcessQuantityList.size() > 0) {
            for (InvoiceExcessQuantity invoiceExcessQuantity : invoiceExcessQuantityList) {
                invoiceExcessQuantity.setExcessQty(BigDecimal.ZERO);
            }
        }
        paymentRequestManagementDao.update(invoiceExcessQuantityList, true);
    }

    private PaymentRequestStatusChangeVO logPaymentRequestStatusChange(PaymentRequestStatusChangeVO changeRequest)
            throws SumoException {
        PaymentRequestStatusLogData paymentRequestStatusLogData = new PaymentRequestStatusLogData();
        paymentRequestStatusLogData.setFromStatus(changeRequest.getCurrentStatus().value());
        paymentRequestStatusLogData.setToStatus(changeRequest.getNewStatus().value());
        paymentRequestStatusLogData.setPaymentRequestId(changeRequest.getPaymentRequestId());
        paymentRequestStatusLogData.setUpdatedBy(changeRequest.getUpdatedBy());
        paymentRequestStatusLogData.setUpdateTime(SCMUtil.getCurrentTimestamp());
        paymentRequestStatusLogData = paymentRequestManagementDao.add(paymentRequestStatusLogData, true);
        if (paymentRequestStatusLogData != null) {
            changeRequest.setUpdated(true);
        } else {
            throw new SumoException("Error updating pr id: " + changeRequest.getPaymentRequestId() + " status from "
                    + changeRequest.getCurrentStatus().value() + " to " + changeRequest.getNewStatus().value());
        }
        return changeRequest;
    }

    private void logPaymentRequestData(String logData, int paymentRequestId) throws SumoException {
        PaymentRequestLogData paymentRequestLogData = new PaymentRequestLogData();
        paymentRequestLogData.setPaymentRequestId(paymentRequestId);
        paymentRequestLogData.setLogData(logData);
        paymentRequestLogData.setUpdateTime(SCMUtil.getCurrentTimestamp());
        paymentRequestLogData = paymentRequestManagementDao.add(paymentRequestLogData, true);
        if (paymentRequestLogData == null) {
            throw new SumoException("Error adding log for payment request action.");
        }
    }

    private void updateDeviation(InvoiceDeviationMapping invoiceDeviationMapping, Date currentTime)
            throws SumoException {
        InvoiceDeviationMappingData invoiceDeviationMappingData = paymentRequestManagementDao
                .find(InvoiceDeviationMappingData.class, invoiceDeviationMapping.getMappingId());
        invoiceDeviationMappingData.setCurrentStatus(invoiceDeviationMapping.getCurrentStatus());
        if (invoiceDeviationMapping.getAcceptedBy() != null
                && invoiceDeviationMapping.getAcceptedBy().getId() != null) {
            invoiceDeviationMappingData.setAcceptedBy(invoiceDeviationMapping.getAcceptedBy().getId());
        }
        if (invoiceDeviationMapping.getRejectedBy() != null
                && invoiceDeviationMapping.getRejectedBy().getId() != null) {
            invoiceDeviationMappingData.setRejectedBy(invoiceDeviationMapping.getRejectedBy().getId());
        }
        if (invoiceDeviationMapping.getRemovedBy() != null && invoiceDeviationMapping.getRemovedBy().getId() != null) {
            invoiceDeviationMappingData.setRemovedBy(invoiceDeviationMapping.getRemovedBy().getId());
        }
        if (invoiceDeviationMappingData.getActionRemark() != null) {
            invoiceDeviationMappingData.setActionRemark(invoiceDeviationMapping.getActionRemark());
        }
        if (!invoiceDeviationMappingData.getCurrentStatus().equals("CREATED")) {
            invoiceDeviationMappingData.setActionTime(currentTime);
        }
        invoiceDeviationMappingData = paymentRequestManagementDao.update(invoiceDeviationMappingData, true);
        if (invoiceDeviationMappingData == null) {
            throw new SumoException("Failed to update deviations while request approval.");
        }
    }

    private void addDeviation(InvoiceDeviationMapping invoiceDeviationMapping) throws SumoException {
        InvoiceDeviationMappingData invoiceDeviationMappingData = new InvoiceDeviationMappingData();
        invoiceDeviationMappingData.setPaymentDeviationData(paymentRequestManagementDao.find(PaymentDeviationData.class,
                invoiceDeviationMapping.getPaymentDeviation().getPaymentDeviationId()));
        invoiceDeviationMappingData.setCreatedBy(invoiceDeviationMapping.getCreatedBy().getId());
        invoiceDeviationMappingData.setCurrentStatus(invoiceDeviationMapping.getCurrentStatus());
        invoiceDeviationMappingData.setDeviationItemId(invoiceDeviationMapping.getDeviationItemId());
        invoiceDeviationMappingData.setDeviationItemType(invoiceDeviationMapping.getDeviationItemType().value());
        if (invoiceDeviationMapping.getDeviationRemark() != null) {
            invoiceDeviationMappingData.setDeviationRemark(invoiceDeviationMapping.getDeviationRemark());
        }
        if (invoiceDeviationMapping.getActionRemark() != null) {
            invoiceDeviationMappingData.setActionRemark(invoiceDeviationMapping.getActionRemark());
        }
        if (invoiceDeviationMapping.getRemovedBy() != null) {
            invoiceDeviationMappingData.setRemovedBy(invoiceDeviationMapping.getRemovedBy().getId());
        }
        if (invoiceDeviationMapping.getRejectedBy() != null) {
            invoiceDeviationMappingData.setRejectedBy(invoiceDeviationMapping.getRejectedBy().getId());
        }
        if (invoiceDeviationMapping.getAcceptedBy() != null) {
            invoiceDeviationMappingData.setAcceptedBy(invoiceDeviationMapping.getAcceptedBy().getId());
        }
        if (invoiceDeviationMapping.getActionTime() != null) {
            invoiceDeviationMappingData.setActionTime(invoiceDeviationMapping.getActionTime());
        }
        invoiceDeviationMappingData.setCreationTime(SCMUtil.getCurrentTimestamp());
        invoiceDeviationMappingData = paymentRequestManagementDao.add(invoiceDeviationMappingData, true);
        if (invoiceDeviationMappingData == null) {
            throw new SumoException("Error adding deviation detail.");
        }
    }

    private boolean validPaymentDate(PaymentCalendarData paymentCalendarData) {
        // TODO write logic
        return true;
    }

    private boolean blockedPayment(VendorDetailData vendorDetailData) {
        return vendorDetailData == null || SCMUtil.getStatus(vendorDetailData.getAccountDetails().getPaymentBlocked());
    }

    private void sendVendorInvoiceRequestNotification(VendorDetailData vendorDetailData,
                                                      PaymentRequestData paymentRequestData, PaymentRequest paymentRequest) {
        VendorDetail vendorDetail = SCMDataConverter.convertVendor(vendorDetailData, "", "");
        String receivingUnit = masterDataCache.getUnit(paymentRequestData.getRequestingUnit()).getName();
        try {
            List<VendorGR> vendorGRs = new ArrayList<>();
            List<PaymentRequestItemMappingData> paymentRequestItemMappingDataList = paymentRequestManagementDao
                    .findMappedItemsByPaymenrRequestId(paymentRequestData.getId());
            for (PaymentRequestItemMappingData paymentRequestItemMappingData : paymentRequestItemMappingDataList) {
                VendorGoodsReceivedData vendorGoodsReceivedData = paymentRequestManagementDao
                        .find(VendorGoodsReceivedData.class, paymentRequestItemMappingData.getPaymentRequestItemId());
                vendorGRs.add(SCMDataConverter.convert(vendorGoodsReceivedData, scmCache, masterDataCache, false));
            }
            String[] emails = {"<EMAIL>"};
            VendorInvoiceRequestEmailNotificationTemplate template = new VendorInvoiceRequestEmailNotificationTemplate(
                    ctxFactory, vendorDetail, paymentRequest, props.getBasePath(), props.getEnvType(), receivingUnit,
                    vendorGRs);
            VendorInvoiceRequestEmailNotification notification = new VendorInvoiceRequestEmailNotification(template,
                    props.getEnvType(), emails);
            WorkbookContext workbook = template.getWorkbook();
            if (workbook != null) {
                template.renderInvoiceRequestSheet();
                template.generateReport(workbook.toNativeBytes());
                List<AttachmentData> attachmentDataList = new ArrayList<>();
                if (template.isGenerated()) {
                    File file = new File(template.getShetPath());
                    if (file.exists()) {
                        AttachmentData attachements = new AttachmentData(IOUtils.toByteArray(new FileInputStream(file)),
                                template.getFileName(), template.getMimeType());
                        attachmentDataList.add(attachements);
                        notification.sendRawMail(attachmentDataList);
                    } else {
                        StringBuilder message = new StringBuilder(
                                "Failed to create vendor invoice request worksheet \n");
                        message.append("Unit Details :: " + receivingUnit + "\n");
                        message.append("Vendor :: " + vendorDetail.getEntityName() + "\n");
                        message.append("PR ID :: " + paymentRequestData.getId() + "\n");
                        SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "Kettle",
                                SlackNotification.PAYMENT_REQUEST, message.toString());
                    }
                }
            }
        } catch (IOException e) {
            LOG.info(e.getMessage());
        } catch (EmailGenerationException e) {
            LOG.info(e.getMessage());
        } catch (Exception e) {
            StringBuilder message = new StringBuilder("Failed to send vendor invoice request mail \n");
            message.append("Unit Details :: " + receivingUnit + "\n");
            message.append("Vendor :: " + vendorDetail.getEntityName() + "\n");
            message.append("PR ID :: " + paymentRequestData.getId() + "\n");
            SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "Kettle",
                    SlackNotification.PAYMENT_REQUEST, message.toString());
        }
    }

    private Integer getColumnIndexFromSheet(String bankName, String column) {
        if (bankName.equals(PaymentBanks.YES_BANK.name())) {
            if (column.equals("PR_ID")) {
                return 5;
            } else if (column.equals("UTR")) {
                return 6;
            } else if (column.equals("BENEFICIARY_NAME")) {
                return 0;
            } else if (column.equals("BENEFICIARY_ACCOUNT")) {
                return 1;
            } else if (column.equals("BENEFICIARY_IFSC")) {
                return 2;
            } else if (column.equals("AMOUNT")) {
                return 3;
            }
        }
        if (bankName.equals(PaymentBanks.KOTAK.name())) {
            if (column.equals("PR_ID")) {
                return 17;
            } else if (column.equals("UTR")) {
                return 18;
            } else if (column.equals("BENEFICIARY_NAME")) {
                return 8;
            } else if (column.equals("BENEFICIARY_ACCOUNT")) {
                return 10;
            } else if (column.equals("BENEFICIARY_IFSC")) {
                return 9;
            } else if (column.equals("AMOUNT")) {
                return 6;
            } else if (column.equals("DEBIT_ACC_NUMBER")) {
                return 5;
            } else if (column.equals("PAYMENT_TYPE")) {
                return 2;
            }
        }
        if (bankName.equals(PaymentBanks.KOTAK_V2.name())) {
            if (column.equals("PR_ID")) {
                return 25;
            } else if (column.equals("UTR")) {
                return 26;
            } else if (column.equals("BENEFICIARY_NAME")) {
                return 10;
            } else if (column.equals("BENEFICIARY_ACCOUNT")) {
                return 13;
            } else if (column.equals("BENEFICIARY_IFSC")) {
                return 12;
            } else if (column.equals("AMOUNT")) {
                return 7;
            } else if (column.equals("DEBIT_ACC_NUMBER")) {
                return 6;
            } else if (column.equals("PAYMENT_TYPE")) {
                return 2;
            }
        }
        if (bankName.equals(PaymentBanks.HDFC.name())) {
            if (column.equals("PR_ID")) {
                return 13;
            } else if (column.equals("UTR")) {
                return 29;
            } else if (column.equals("BENEFICIARY_NAME")) {
                return 4;
            } else if (column.equals("BENEFICIARY_ACCOUNT")) {
                return 2;
            } else if (column.equals("BENEFICIARY_IFSC")) {
                return 24;
            } else if (column.equals("AMOUNT")) {
                return 3;
            } else if (column.equals("DEBIT_ACC_NUMBER")) {
                return 28;
            } else if (column.equals("PAYMENT_TYPE")) {
                return 0;
            }
        }
        if (bankName.equals(PaymentBanks.ICICI.name())) {
            if (column.equals("PR NUMBER")) {
                return 15;
            } else if (column.equals("Utr Number")) {
                return 21;
            } else if (column.equals("Beneficiary Name")) {
                return 2;
            } else if (column.equals("Beneficiary Ac No")) {
                return 1;
            } else if (column.equals("IFSC")) {
                return 6;
            } else if (column.equals("Amt")) {
                return 3;
            } else if (column.equals("Debit Ac No")) {
                return 0;
            } else if (column.equals("Pay Mod")) {
                return 4;
            }
        }
        return null;
    }

    private void savePaymentDetails(Map<String, PRPaymentDetail> paymentDetailMap,
                                    Map<String, PRPaymentDetailData> paymentDetailDataMap, List<PaymentRequest> paymentRequests, Boolean isForceUploaded, String utrUploadedBy, Integer userId)
            throws SumoException {
        Date time = SCMUtil.getCurrentTimestamp();
        for (String utr : paymentDetailMap.keySet()) {
            PRPaymentDetailData prPaymentDetailData = new PRPaymentDetailData();
            PRPaymentDetail prPaymentDetail = paymentDetailMap.get(utr);
            prPaymentDetailData.setUtrNumber(utr);
            prPaymentDetailData.setProposedAmount(prPaymentDetail.getProposedAmount());
            prPaymentDetailData.setCreatedBy(prPaymentDetail.getCreatedBy().getId());
            prPaymentDetailData.setVendorName(prPaymentDetail.getVendorName());
            prPaymentDetailData.setVendorId(prPaymentDetail.getVendorId());
            if (prPaymentDetail.getActualDate() != null) {
                prPaymentDetailData.setActualDate(prPaymentDetail.getActualDate());
            }
            if (prPaymentDetail.getRemarks() != null) {
                prPaymentDetailData.setRemarks(prPaymentDetail.getRemarks());
            }
            if (prPaymentDetail.getPaymentType() != null) {
                prPaymentDetailData.setPaymentType(prPaymentDetail.getPaymentType().value());
            }
            prPaymentDetailData.setPaidAmount(prPaymentDetail.getPaidAmount());
            prPaymentDetailData.setDebitBank(prPaymentDetail.getDebitBank());
            if (prPaymentDetail.getDebitAccount() != null) {
                prPaymentDetailData.setDebitAccountNumber(prPaymentDetail.getDebitAccount());
            }
            prPaymentDetailData.setBeneficiaryIFSCode(prPaymentDetail.getBeneficiaryIfscCode());
            prPaymentDetailData.setBeneficiaryAccountNumber(prPaymentDetail.getBeneficiaryAccountNumber());
            prPaymentDetailData.setPaymentDate(time);
            prPaymentDetailData.setVendorPaymentDate(prPaymentDetail.getVendorPaymentDate());
            prPaymentDetailData.setUtrUploadedBy(utrUploadedBy);
            prPaymentDetailData.setUtrUploadedTime(time);
            prPaymentDetailData.setIsForcedUtrUpload(isForceUploaded ? AppConstants.YES : AppConstants.NO);
            prPaymentDetailData = paymentRequestManagementDao.add(prPaymentDetailData, true);
            if (prPaymentDetailData != null) {
                paymentDetailDataMap.put(utr, prPaymentDetailData);
                paymentDetailMap.put(utr, SCMDataConverter.convert(prPaymentDetailData,
                        masterDataCache.getEmployee(prPaymentDetailData.getCreatedBy())));
            }
        }

        for (PaymentRequest paymentRequest : paymentRequests) {
            PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class,
                    paymentRequest.getPaymentRequestId());
            paymentRequestData
                    .setPrPaymentDetailData(paymentDetailDataMap.get(paymentRequest.getPaymentDetail().getUtrNumber()));
            String currentStatus = paymentRequestData.getCurrentStatus();
            paymentRequestData.setCurrentStatus(PaymentRequestStatus.PAID.value());
            paymentRequestData.setLastUpdated(time);
            paymentRequestData = paymentRequestManagementDao.update(paymentRequestData, true);
            if (paymentRequestData != null) {
                PaymentRequestStatusChangeVO changeRequest = new PaymentRequestStatusChangeVO();
                changeRequest.setCurrentStatus(PaymentRequestStatus.valueOf(currentStatus));
                changeRequest.setNewStatus(PaymentRequestStatus.PAID);
                changeRequest.setPaymentRequestId(paymentRequest.getPaymentRequestId());
                changeRequest.setUpdatedBy(paymentRequestData.getPrPaymentDetailData().getCreatedBy());
                logPaymentRequestStatusChange(changeRequest);
                String logData = "Payment request id: " + paymentRequestData.getId() + " settled by "
                        + masterDataCache.getEmployee(changeRequest.getUpdatedBy()) + "[" + changeRequest + "]";
                logPaymentRequestData(logData, paymentRequestData.getId());
                if (paymentRequestData.getType().equalsIgnoreCase(PaymentRequestType.SERVICE_RECEIVED.value())) {
                    updateBudgetAuditDetails(paymentRequestData);
                }
                if (paymentRequestData.getType().equalsIgnoreCase(PaymentRequestType.GOODS_RECEIVED.value())) {
                    updateBudgetDetailsForPaidPOV2(paymentRequestData);
                }
            } else {
                throw new SumoException("Could not update payment request.");
            }
        }
    }

    private List<String> findExceptionalPrs(List<PaymentRequest> paymentRequests) {
        try {
            List<String> exceptions = new ArrayList<>();
            Set<Integer> exceptionPrIds = new HashSet<>();
            for (PaymentRequest paymentRequest : paymentRequests) {
                PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class, paymentRequest.getPaymentRequestId());
                if (paymentRequestData.getType().equalsIgnoreCase(PaymentRequestType.SERVICE_RECEIVED.value())) {
                    LOG.info("checking for exceptional prs for type {}", PaymentRequestType.SERVICE_RECEIVED.value());
                    PaymentInvoiceData paymentInvoiceData = paymentRequestManagementDao.findPaymentInvoice(paymentRequestData.getId());
                    for (PaymentInvoiceItemData invoiceItemData : paymentInvoiceData.getPaymentInvoiceItemData()) {
                        if (invoiceItemData.getServiceReceivedItemId() != null) {
                            ServiceReceivedItemData serviceRecItem = paymentRequestManagementDao.find(ServiceReceivedItemData.class, invoiceItemData.getServiceReceivedItemId());
                            ServiceOrderItemData serviceOrderItemData = paymentRequestManagementDao.find(ServiceOrderItemData.class, serviceRecItem.getServiceOrderItemId());
                            if (serviceOrderItemData.getType() != null && serviceOrderItemData.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                                List<BusinessCostCenter> bccs = getBusinessCostCentersData();
                                CostElementData costElement = serviceOrderManagementDao.find(CostElementData.class,
                                        serviceOrderItemData.getCostElementId());
                                BusinessCostCenter bcc = bccs.stream()
                                        .filter(b -> b.getId().equals(serviceOrderItemData.getBusinessCostCenterId())).findFirst()
                                        .orElse(null);
                                CapexAuditDetailData capexAuditDetail = serviceOrderManagementDao.findCapexAuditData(Integer.parseInt(bcc.getCode()));
                                if (capexAuditDetail == null) {
                                    String message = "Can not find Capex audit detail for unit id : <b>" + Integer.parseInt(bcc.getCode()) + "(" + bcc.getName() + ")" +
                                            "</b>. Please Upload Budget....! Otherwise " +
                                            "can not settle Payment with <b>PR Id: " + paymentRequestData.getId() + "</b> SO Item Id is : " + serviceOrderItemData.getId() + "<br>";
                                    exceptions.add(message);
                                    exceptionPrIds.add(paymentRequestData.getId());
                                } else {
                                    CapexBudgetDetailData capexBudgetData = serviceOrderManagementDao
                                            .findBudgetUnit(Integer.parseInt(bcc.getCode()), costElement.getDepartment().getName());
                                    if (capexBudgetData == null) {
                                        String message = "Can not find Capex Budget detail for unit id : <b>" + Integer.parseInt(bcc.getCode()) + "(" + bcc.getName() + ")" +
                                                "</b>. Please Upload Budget of department <b>(" + costElement.getDepartment().getName() + ")</b>....! Otherwise " +
                                                "can not settle Payment with <b>PR Id: " + paymentRequestData.getId() + "</b> SO Item Id is : " + serviceOrderItemData.getId() + "<br>";
                                        exceptions.add(message);
                                        exceptionPrIds.add(paymentRequestData.getId());
                                    } else {
                                        BigDecimal budgetPaidAmount = capexBudgetData.getPaidAmount();
                                        if (budgetPaidAmount == null) {
                                            String message = "Capex Budget Paid Amount is <b>NULL</b> for unit id : <b>" + Integer.parseInt(bcc.getCode()) + "(" + bcc.getName() + ")" +
                                                    "</b>. Please Upload Budget(If uploaded Check for Paid Amount) of department (" + costElement.getDepartment().getName() +
                                                    ")....! Otherwise can not settle Payment with <b>PR Id: " + paymentRequestData.getId() + "</b> SO Item Id is : " + serviceOrderItemData.getId() + "<br>";
                                            exceptions.add(message);
                                            exceptionPrIds.add(paymentRequestData.getId());
                                        }
                                        BigDecimal invoiceAmount = invoiceItemData.getTotalAmount();
                                        if (invoiceAmount == null) {
                                            String message = "Invoice amount(Total Amount) for invoice Item data id : <b>" + invoiceItemData.getId() + "(" + bcc.getName() + ")" +
                                                    "</b>. Please Check for Total Amount" +
                                                    "....! Otherwise can not settle Payment with <b>PR Id: " + paymentRequestData.getId() + "</b> SO Item Id is : " + serviceOrderItemData.getId() + "<br>";
                                            exceptions.add(message);
                                            exceptionPrIds.add(paymentRequestData.getId());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                if (paymentRequestData.getType().equalsIgnoreCase(PaymentRequestType.GOODS_RECEIVED.value())) {
                    LOG.info("checking for exceptional prs for type {}", PaymentRequestType.GOODS_RECEIVED.value());
                    List<VendorGoodsReceivedData> vendorGrs = paymentRequestManagementDao.findVendorGRFromPaymentRequest(paymentRequestData.getId());
                    if (vendorGrs.size() == 0) {
                        String message = "Can not find Vendor Gr's can not settle Payment with <b>PR Id: " + paymentRequestData.getId() + "</b><br>";
                        exceptions.add(message);
                        exceptionPrIds.add(paymentRequestData.getId());
                    } else {
                        Set<String> departments = new HashSet<>();
                        for (VendorGoodsReceivedData data : vendorGrs) {
                            if (data.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                                departments.add(data.getVendorGRType());
                            }
                        }
                        if (departments.size() > 0) {
                            CapexAuditDetailData capexAuditDetail = serviceOrderManagementDao.findCapexAuditData(paymentRequestData.getRequestingUnit());
                            if (capexAuditDetail == null) {
                                String message = "Can not find Capex audit detail for unit id : <b>" + paymentRequestData.getRequestingUnit() + "(" +
                                        masterDataCache.getUnit(paymentRequestData.getRequestingUnit()).getName() + ")" + "</b>. Please Upload Budget....! Otherwise " +
                                        "can not settle Payment with <b>PR Id: " + paymentRequestData.getId() + "</b><br>";
                                exceptions.add(message);
                                exceptionPrIds.add(paymentRequestData.getId());
                            } else {
                                for (String dept : departments) {
                                    String isFAOrGoods = dept.equalsIgnoreCase(AppConstants.FIXED_ASSET_ORDER) ? AppConstants.FIXED_ASSETS : AppConstants.GOODS;
                                    CapexBudgetDetailData capexBudgetData = serviceOrderManagementDao.findBudgetUnit(paymentRequestData.getRequestingUnit(), isFAOrGoods);
                                    if (capexBudgetData == null) {
                                        String message = "Can not find Capex Budget detail for unit id : <b>" + paymentRequestData.getRequestingUnit() + "(" +
                                                masterDataCache.getUnit(paymentRequestData.getRequestingUnit()).getName() + ")" + "</b>. Please Upload Budget" +
                                                "of department <b>(" + isFAOrGoods + ")</b>....! Otherwise can not settle Payment with <b>PR Id: " + paymentRequestData.getId() + "</b><br>";
                                        exceptions.add(message);
                                        exceptionPrIds.add(paymentRequestData.getId());
                                    } else {
                                        BigDecimal budgetPaidAmount = capexBudgetData.getPaidAmount();
                                        if (budgetPaidAmount == null) {
                                            String message = "Capex Budget Paid Amount is <b>NULL</b> for unit id : <b>" + paymentRequestData.getRequestingUnit() + "(" +
                                                    masterDataCache.getUnit(paymentRequestData.getRequestingUnit()).getName() + ")" + "</b>. Please Upload Budget" +
                                                    "of department (" + isFAOrGoods + ")....! Otherwise can not settle Payment with <b>PR Id: " + paymentRequestData.getId() + "</b><br>";
                                            exceptions.add(message);
                                            exceptionPrIds.add(paymentRequestData.getId());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            LOG.info("Total Exceptions got for the uploaded sheet of Payment is {}", exceptions.size());
            LOG.info("Total Exception PR ID's got for the uploaded sheet of Payment is {}", exceptionPrIds.size());
            if (exceptionPrIds.size() > 0) {
                String listOfPrs = "<br><b>" + Arrays.toString(exceptionPrIds.toArray()) + "</b>";
                exceptions.add(listOfPrs);
            }
            return exceptions;
        } catch (Exception e) {
            LOG.error("Exception Occurred while Checking for Exceptional PR's ::: ", e);
            return new ArrayList<>();
        }
    }

    private Integer getDepartmentId(String departmentName) {
        ValidationUtil.notBlank(departmentName, "Found Blank Department Name while getting Department Id");
        if (departmentName.equalsIgnoreCase(AppConstants.FIXED_ASSETS)) {
            return 37;
        }
        return 38;
    }

    private void updateBudgetDetailsForPaidPOV2(PaymentRequestData paymentRequestData) throws SumoException {
        try {
            List<VendorGoodsReceivedData> vendorGrs = paymentRequestManagementDao.findVendorGRFromPaymentRequest(paymentRequestData.getId());
            ValidationUtil.notEmpty(vendorGrs, "No Vendor GR's found for the Payment Request Id: " + paymentRequestData.getId());
            updateBudgetAuditDetailsForPR(paymentRequestData, vendorGrs);
        } catch (Exception e) {
            LOG.error("Error while updating budget details for paid PO", e);
            throw new SumoException("Error while updating budget details for paid PO", e);
        }
    }

    private void updateBudgetAuditDetailsForPR(PaymentRequestData paymentRequestData, List<VendorGoodsReceivedData> vendorGrs) {
        Map<Integer, CapexBudgetDetailData> capexBudgetDetailMap = getCapexBudgetDetailByDepartment(paymentRequestData.getRequestingUnit(), vendorGrs);
        CapexAuditDetailData capexAuditDetail = serviceOrderManagementDao.findCapexAuditData(paymentRequestData.getRequestingUnit());
        List<BudgetAuditDetailData> BADInsertList = new ArrayList<>();
        for(VendorGoodsReceivedData data : vendorGrs) {
            handleVendorGrProcessing(paymentRequestData, capexBudgetDetailMap, capexAuditDetail, BADInsertList, data);
        }
        serviceOrderManagementDao.insertInBulk(BADInsertList);
    }

    private void handleVendorGrProcessing(PaymentRequestData paymentRequestData, Map<Integer, CapexBudgetDetailData> capexBudgetDetailMap,
                                          CapexAuditDetailData capexAuditDetail, List<BudgetAuditDetailData> BADInsertList,
                                          VendorGoodsReceivedData data) {
        for (VendorGoodsReceivedItemData itemData : data.getGrItemList()) {
            CapexBudgetDetailData capexBudgetData = capexBudgetDetailMap.get(itemData.getDepartmentId());

            BudgetAuditDetailData budgetAuditData = getBudgetAuditDetailData(capexBudgetData, capexAuditDetail, itemData.getTotalAmount(), paymentRequestData);
            budgetAuditData.setKeyValue(itemData.getItemId());
            BADInsertList.add(budgetAuditData);

            capexBudgetData.setPaidAmount(capexBudgetData.getPaidAmount().add(itemData.getTotalAmount()));
            capexBudgetData = serviceOrderManagementDao.update(capexBudgetData, true);
            ValidationUtil.isTrue(purchaseOrderService.validateBudgetAmounts(capexBudgetData), "Budget Values went into negative...! Please Check");
        }
        if (data.getExtraCharges() != null && data.getExtraCharges().compareTo(BigDecimal.ZERO) > 0) {
            CapexBudgetDetailData capexBudgetData = capexBudgetDetailMap.get(getDepartmentId(data.getVendorGRType()));

            BudgetAuditDetailData budgetAuditData = getBudgetAuditDetailData(capexBudgetData, capexAuditDetail, data.getExtraCharges(), paymentRequestData);
            budgetAuditData.setComment("Budget updated with Extra Charges for VGR Id " + data.getGoodsReceivedId() + " and PR Id: " + paymentRequestData.getId());
            budgetAuditData.setKeyType(BudgetAuditActions.VGR_ID.value());
            budgetAuditData.setKeyValue(data.getGoodsReceivedId());
            BADInsertList.add(budgetAuditData);

            capexBudgetData.setPaidAmount(capexBudgetData.getPaidAmount().add(data.getExtraCharges()));
            capexBudgetData = serviceOrderManagementDao.update(capexBudgetData, true);
            ValidationUtil.isTrue(purchaseOrderService.validateBudgetAmounts(capexBudgetData), "Budget Values went into negative...! Please Check");
        }
    }

    private Map<Integer, CapexBudgetDetailData> getCapexBudgetDetailByDepartment(Integer deliveryUnitId, List<VendorGoodsReceivedData> vendorGrs) {
        List<Integer> departmentIds = vendorGrs.stream()
                .filter(Objects::nonNull)
                .flatMap(gr -> gr.getGrItemList().stream())
                .map(VendorGoodsReceivedItemData::getDepartmentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        ValidationUtil.notEmpty(departmentIds, "No Department Ids found in Vendor GR Item Data for Delivery Unit Id: " + deliveryUnitId);
        departmentIds.add(SCMConstant.FA_EQUIPMENT_DEPARTMENT_ID);
        departmentIds.add(SCMConstant.NRE_CONSUMABLE_DEPARTMENT_ID);
        List<CapexBudgetDetailData> capexBudgetDetailDataList = serviceOrderManagementDao.findBudgetUnitV2(deliveryUnitId, departmentIds);
        Map<Integer, CapexBudgetDetailData> capexBudgetDetailDataMap = new HashMap<>();
        for( CapexBudgetDetailData cbd : capexBudgetDetailDataList) {
            capexBudgetDetailDataMap.put( cbd.getDepartmentId(), cbd );
        }
        return capexBudgetDetailDataMap;
    }

    private BudgetAuditDetailData getBudgetAuditDetailData(CapexBudgetDetailData capexBudgetData, CapexAuditDetailData capexAuditDetail, BigDecimal totalAmount, PaymentRequestData paymentRequestData) {
        BudgetAuditDetailData budgetAuditData = new BudgetAuditDetailData();
        budgetAuditData.setCapexBudgetDetailId(capexBudgetData.getId());
        budgetAuditData.setCapexAuditId(capexAuditDetail.getId());
        budgetAuditData.setAmountType(BudgetAuditActions.PAID_AMOUNT.value());
        budgetAuditData.setPreviousValue(capexBudgetData.getPaidAmount());
        budgetAuditData.setFinalValue(capexBudgetData.getPaidAmount().add(totalAmount));
        budgetAuditData.setActionType(BudgetAuditActions.ADDITION.value());
        budgetAuditData.setAction(BudgetAuditActions.CREATED.value());
        budgetAuditData.setKeyType(BudgetAuditActions.VGR_ITEM_ID.value());
        budgetAuditData.setKeyValue(paymentRequestData.getId());
        budgetAuditData.setActionBy(paymentRequestData.getCreatedBy());
        budgetAuditData.setActionTime(SCMUtil.getCurrentTimestamp());
        budgetAuditData.setComment("Budget updated with PR Id: " + paymentRequestData.getId());
        return budgetAuditData;
    }


    private Boolean updateBudgetDetailsForPaidPO(PaymentRequestData paymentRequestData) {
        LOG.info("Started Updating budget of Paid PO");
        try {
            List<VendorGoodsReceivedData> vendorGrs = paymentRequestManagementDao.findVendorGRFromPaymentRequest(paymentRequestData.getId());
            if (paymentRequestData.getGrDocType().equalsIgnoreCase(InvoiceDocType.DELIVERY_CHALLAN.value())) {
                LOG.info("Updating Budget For Delivery Challan PR.");
                HashMap<String, BigDecimal> departments = new HashMap<>();
                for (VendorGoodsReceivedData data : vendorGrs) {
                    if (data.getType() != null && data.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                        if (departments.containsKey(data.getVendorGRType())) {
                            BigDecimal addedAmount = departments.get(data.getVendorGRType()).add(data.getTotalAmount());
                            if (data.getExtraCharges() != null) {
                                addedAmount = addedAmount.add(data.getExtraCharges());
                            }
                            departments.put(data.getVendorGRType(), addedAmount);
                        } else {
                            BigDecimal addedAmount = data.getTotalAmount();
                            if (data.getExtraCharges() != null) {
                                addedAmount = addedAmount.add(data.getExtraCharges());
                            }
                            departments.put(data.getVendorGRType(), addedAmount);
                        }
                    }
                }
                if (departments.size() > 0) {
                    for (Map.Entry entry : departments.entrySet()) {
                        String orderType = (String) entry.getKey();
                        BigDecimal orderValue = (BigDecimal) entry.getValue();
                        String isFAOrGoods = orderType.equalsIgnoreCase(AppConstants.FIXED_ASSET_ORDER) ? AppConstants.FIXED_ASSETS : AppConstants.GOODS;
                        LOG.info("Updating budget for {} department and order value is {}", isFAOrGoods, orderValue);
                        CapexAuditDetailData capexAuditDetail = serviceOrderManagementDao.findCapexAuditData(paymentRequestData.getRequestingUnit());
                        CapexBudgetDetailData capexBudgetData = serviceOrderManagementDao.findBudgetUnit(paymentRequestData.getRequestingUnit(), isFAOrGoods);
                        BudgetAuditDetailData budgetAuditData = new BudgetAuditDetailData();
                        budgetAuditData.setCapexBudgetDetailId(capexBudgetData.getId());
                        budgetAuditData.setCapexAuditId(capexAuditDetail.getId());
                        budgetAuditData.setAmountType(BudgetAuditActions.PAID_AMOUNT.value());
                        budgetAuditData.setPreviousValue(capexBudgetData.getPaidAmount());
                        budgetAuditData.setFinalValue(capexBudgetData.getPaidAmount().add(orderValue));
                        budgetAuditData.setActionType(BudgetAuditActions.ADDITION.value());
                        budgetAuditData.setAction(BudgetAuditActions.CREATED.value());
                        budgetAuditData.setKeyType(BudgetAuditActions.PR_ID.value());
                        budgetAuditData.setKeyValue(paymentRequestData.getId());
                        budgetAuditData.setActionBy(paymentRequestData.getCreatedBy());
                        budgetAuditData.setActionTime(SCMUtil.getCurrentTimestamp());
                        serviceOrderManagementDao.add(budgetAuditData, true);
                        capexBudgetData.setPaidAmount(capexBudgetData.getPaidAmount().add(orderValue));
                        CapexBudgetDetailData finalData = serviceOrderManagementDao.update(capexBudgetData, true);
                        if (!purchaseOrderService.validateBudgetAmounts(finalData)) {
                            LOG.info("Budget Values went into negative...! Please Check");
                            return false;
                        }
                    }
                }
                return true;
            } else {
                if (vendorGrs.get(0).getType() != null && vendorGrs.get(0).getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                    LOG.info("Updating Budget For Invoice PR.");
                    CapexAuditDetailData capexAuditDetail = serviceOrderManagementDao.findCapexAuditData(paymentRequestData.getRequestingUnit());
                    String isFAOrGoods = vendorGrs.get(0).getVendorGRType().equalsIgnoreCase(AppConstants.FIXED_ASSET_ORDER) ? AppConstants.FIXED_ASSETS : AppConstants.GOODS;
                    CapexBudgetDetailData capexBudgetData = serviceOrderManagementDao.findBudgetUnit(paymentRequestData.getRequestingUnit(), isFAOrGoods);
                    BudgetAuditDetailData budgetAuditData = new BudgetAuditDetailData();
                    budgetAuditData.setCapexBudgetDetailId(capexBudgetData.getId());
                    budgetAuditData.setCapexAuditId(capexAuditDetail.getId());
                    budgetAuditData.setAmountType(BudgetAuditActions.PAID_AMOUNT.value());
                    budgetAuditData.setPreviousValue(capexBudgetData.getPaidAmount());
                    budgetAuditData.setFinalValue(capexBudgetData.getPaidAmount().add(paymentRequestData.getDuplicatePaidAmount()));
                    budgetAuditData.setActionType(BudgetAuditActions.ADDITION.value());
                    budgetAuditData.setAction(BudgetAuditActions.CREATED.value());
                    budgetAuditData.setKeyType(BudgetAuditActions.PR_ID.value());
                    budgetAuditData.setKeyValue(paymentRequestData.getId());
                    budgetAuditData.setActionBy(paymentRequestData.getCreatedBy());
                    budgetAuditData.setActionTime(SCMUtil.getCurrentTimestamp());
                    serviceOrderManagementDao.add(budgetAuditData, true);
                    capexBudgetData.setPaidAmount(capexBudgetData.getPaidAmount().add(paymentRequestData.getPaidAmount()));
                    CapexBudgetDetailData finalData = serviceOrderManagementDao.update(capexBudgetData, true);
                    if (!purchaseOrderService.validateBudgetAmounts(finalData)) {
                        LOG.info("Budget Values went into negative...! Please Check");
                        return false;
                    }
                }
                return true;
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred while updating budget for Paid PO ::: ", e);
            return false;
        }
    }

    private void updateBudgetAuditDetails(PaymentRequestData paymentRequestData) throws SumoException {
        LOG.info("Started submitting payment with PR id : {}", paymentRequestData.getId());
        PaymentInvoiceData paymentInvoiceData = paymentRequestManagementDao
                .findPaymentInvoice(paymentRequestData.getId());
        for (PaymentInvoiceItemData invoiceItemData : paymentInvoiceData.getPaymentInvoiceItemData()) {
            if (invoiceItemData.getServiceReceivedItemId() != null) {
                ServiceReceivedItemData serviceRecItem = paymentRequestManagementDao.find(ServiceReceivedItemData.class,
                        invoiceItemData.getServiceReceivedItemId());
                ServiceOrderItemData serviceOrderItemData = paymentRequestManagementDao.find(ServiceOrderItemData.class,
                        serviceRecItem.getServiceOrderItemId());
                if (serviceOrderItemData.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                    List<BusinessCostCenter> bccs = getBusinessCostCentersData();
                    CostElementData costElement = serviceOrderManagementDao.find(CostElementData.class,
                            serviceOrderItemData.getCostElementId());
                    BusinessCostCenter bcc = bccs.stream()
                            .filter(b -> b.getId().equals(serviceOrderItemData.getBusinessCostCenterId())).findFirst()
                            .orElse(null);
                    CapexAuditDetailData capexAuditDetail = serviceOrderManagementDao
                            .findCapexAuditData(Integer.parseInt(bcc.getCode()));
                    CapexBudgetDetailData capexBudgetData = serviceOrderManagementDao
                            .findBudgetUnit(Integer.parseInt(bcc.getCode()), costElement.getDepartment().getName());
                    BudgetAuditDetailData budgetAuditDetailData = new BudgetAuditDetailData();
                    budgetAuditDetailData.setCapexAuditId(capexAuditDetail.getId());
                    budgetAuditDetailData.setCapexBudgetDetailId(capexBudgetData.getId());
                    budgetAuditDetailData.setAmountType(BudgetAuditActions.PAID_AMOUNT.value());
                    budgetAuditDetailData.setPreviousValue(capexBudgetData.getPaidAmount());
                    budgetAuditDetailData
                            .setFinalValue(capexBudgetData.getPaidAmount().add(invoiceItemData.getTotalAmount()));
                    budgetAuditDetailData.setAction(BudgetAuditActions.PAID.value());
                    budgetAuditDetailData.setActionType(BudgetAuditActions.ADDITION.value());
                    budgetAuditDetailData.setKeyType(BudgetAuditActions.PR_ID.value());
                    budgetAuditDetailData.setKeyValue(paymentRequestData.getId());
                    budgetAuditDetailData.setActionBy(paymentRequestData.getPrPaymentDetailData().getCreatedBy());
                    budgetAuditDetailData.setActionTime(SCMUtil.getCurrentTimestamp());
                    paymentRequestManagementDao.add(budgetAuditDetailData, true);
                    capexBudgetData
                            .setPaidAmount(capexBudgetData.getPaidAmount().add(invoiceItemData.getTotalAmount()));
                    paymentRequestManagementDao.update(capexBudgetData, true);
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<BusinessCostCenter> getBusinessCostCentersData() {
        List<BusinessCostCenter> list = new ArrayList<>();
        for (BusinessCostCenterData d : paymentRequestManagementDao.findAll(BusinessCostCenterData.class)) {
            if (AppConstants.ACTIVE.equals(d.getStatus())) {
                list.add(SCMDataConverter.convertToIdCodeName(d, masterDataCache));
            }
        }
        return list;
    }

    private void sendPaymentNotification(List<PaymentRequest> paymentRequests,
                                         Map<String, PRPaymentDetail> paymentDetailMap, String selectedCompany) throws EmailGenerationException {
        for (PaymentRequest paymentRequest : paymentRequests) {
            Set<String> emailSet = getCostCenterMailData(paymentRequest);
            String utrNumber = paymentRequest.getPaymentDetail().getUtrNumber();
            VendorDetailData vendorDetailData = paymentRequestManagementDao.find(VendorDetailData.class,
                    paymentDetailMap.get(utrNumber).getVendorId());
            VendorDetail vendorDetail = SCMDataConverter.convertVendor(vendorDetailData, "", "");
            String paymentDate = "";
            if (!paymentRequest.getPaymentDetail().getDebitBank().equals("ICICI")) {
                paymentDate = SCMUtil.getFormattedDate(paymentDetailMap.get(utrNumber).getPaymentDate());
            } else {
                paymentDate = SCMUtil.getFormatedDate(paymentDetailMap.get(utrNumber).getPaymentDate());
            }
            String actualDate = "";
            if (!paymentRequest.getPaymentDetail().getDebitBank().equals("ICICI")) {
                actualDate = SCMUtil.getFormattedDate(paymentDetailMap.get(utrNumber).getActualDate());
            } else {
                actualDate = SCMUtil.getFormatedDate(paymentDetailMap.get(utrNumber).getActualDate());
            }
            // String actualDate = SCMUtil.getFormattedDate(paymentDetailMap.get(utrNumber).getActualDate());
            Company company = masterDataCache.getCompany(paymentRequest.getCompanyId());
            PaymentInvoiceData paymentInvoice = paymentRequestManagementDao.findPaymentInvoice(paymentRequest.getPaymentRequestId());
            VendorPaymentEmailNotificationTemplate vendorPaymentEmailNotificationTemplate = new VendorPaymentEmailNotificationTemplate(
                    vendorDetail, paymentRequest, paymentDetailMap.get(utrNumber), props.getBasePath(), paymentDate, company, selectedCompany, actualDate, paymentInvoice);
            if (emailSet != null && emailSet.size() > 0) {
                String[] emails = new String[emailSet.size()];
                emailSet.toArray(emails);
                VendorPaymentEmailNotification vendorPaymentEmailNotification = new VendorPaymentEmailNotification(
                        vendorPaymentEmailNotificationTemplate, props.getEnvType(), emails);
                vendorPaymentEmailNotification.sendEmail();
            } else {
                String[] emails = {"<EMAIL>"};
                VendorPaymentEmailNotification vendorPaymentEmailNotification = new VendorPaymentEmailNotification(
                        vendorPaymentEmailNotificationTemplate, props.getEnvType(), emails);
                vendorPaymentEmailNotification.sendEmail();
            }
        }

    }

    private String getGrStatus(PaymentRequestData paymentRequestData,
                               PaymentRequestItemMappingData paymentRequestItemMappingData) {
        if (!PaymentRequestStatus.PAID.value().equals(paymentRequestData.getCurrentStatus())
                && !PaymentRequestStatus.CANCELLED.value().equals(paymentRequestData.getCurrentStatus())
                && !PaymentRequestStatus.REJECTED.value().equals(paymentRequestData.getCurrentStatus())) {
            return GRProcessingStatus.IN_PROCESS.value();
        } else if (PaymentRequestStatus.PAID.value().equals(paymentRequestData.getCurrentStatus())) {
            return GRProcessingStatus.PROCESSED.value();
        } else {
            if (paymentRequestItemMappingData.getLinkedPaymentRequestId() == null) {
                VendorGoodsReceivedData vendorGoodsReceivedData = paymentRequestManagementDao
                        .find(VendorGoodsReceivedData.class, paymentRequestItemMappingData.getPaymentRequestItemId());
                if (vendorGoodsReceivedData != null
                        && SCMServiceConstants.SCM_CONSTANT_NO.equals(vendorGoodsReceivedData.getToBePaid())) {
                    return GRProcessingStatus.PROCESSED.value();
                } else {
                    return GRProcessingStatus.PENDING.value();
                }
            } else {
                List<PaymentRequestItemMappingData> paymentRequestItemMappingData1 = paymentRequestManagementDao
                        .findMappedItemsByGRId(paymentRequestItemMappingData.getPaymentRequestItemId(),
                                PaymentRequestType.fromValue(paymentRequestData.getType()));
                if (paymentRequestItemMappingData1 != null && paymentRequestItemMappingData1.size() > 0) {
                    PaymentRequestData paymentRequestData1 = paymentRequestManagementDao.find(PaymentRequestData.class,
                            paymentRequestItemMappingData1.get(0).getPaymentRequestId());
                    return getGrStatus(paymentRequestData1, paymentRequestItemMappingData1.get(0));
                } else {
                    return GRProcessingStatus.PENDING.value();
                }
            }
        }
    }

    private BigDecimal getDebitBalance(VendorDetail vendor) {
        BigDecimal debitBalance = new BigDecimal(0);
        for (VendorDebitBalanceVO vo : vendor.getVos()) {
            debitBalance = AppUtils.add(debitBalance, vo.getDebitBalance());
        }
        return debitBalance;
    }

    private BigDecimal getDebitBalance(VendorDetailData data) {
        BigDecimal debitBalance = new BigDecimal(0);
        for (VendorCompanyDebitMapping mapping : data.getDebitMappings()) {
            debitBalance = AppUtils.add(debitBalance, mapping.getDebitBalance());
        }
        return debitBalance;
    }

    private List<VendorDebitBalanceVO> getDebitBalanceVOs(VendorDetailData vendorDetailData) {
        List<VendorDebitBalanceVO> vendorDebitBalanceVOS = new ArrayList<>();
        vendorDetailData.getDebitMappings().stream().forEach(vendorCompanyDebitMapping -> {
            VendorDebitBalanceVO vendorDebitBalanceVO = new VendorDebitBalanceVO();
            vendorDebitBalanceVO.setCompanyId(vendorCompanyDebitMapping.getCompanyId());
            vendorDebitBalanceVO.setCompanyName(vendorCompanyDebitMapping.getCompanyName());
            vendorDebitBalanceVO.setDebitBalance(vendorCompanyDebitMapping.getDebitBalance());
            vendorDebitBalanceVO.setEntityName(vendorDetailData.getEntityName());
            vendorDebitBalanceVO.setVendorId(vendorDetailData.getVendorId());
            vendorDebitBalanceVO.setVendorStatus(vendorDetailData.getStatus());
            vendorDebitBalanceVOS.add(vendorDebitBalanceVO);
        });
        return vendorDebitBalanceVOS;
    }

    private List<PaymentRequest> convertToPaymentRequestList(List<PaymentRequestData> paymentRequestDataList) {
        List<PaymentRequest> paymentRequests = new ArrayList<>();
        paymentRequestDataList.forEach(paymentRequestData -> {
            VendorDetailData vendorDetailData = paymentRequestManagementDao.find(VendorDetailData.class,
                    paymentRequestData.getVendorId());
            BigDecimal debitBalance = getDebitBalance(vendorDetailData);

            String vendor = scmCache.getVendorDetail(paymentRequestData.getVendorId()).getEntityName();
            String blockedBy = "";
            if (paymentRequestData.getBlockedBy() != null) {
                blockedBy = masterDataCache.getEmployee(paymentRequestData.getBlockedBy());
            }
            String createdBy = masterDataCache.getEmployee(paymentRequestData.getCreatedBy());
            String requestingUnit = null;
            if (paymentRequestData.getRequestingUnit() != null) {
                requestingUnit = scmCache.getUnitDetail(paymentRequestData.getRequestingUnit()).getUnitName();
            }
            String detailBy = paymentRequestData.getPrPaymentDetailData() != null
                    ? masterDataCache.getEmployee(paymentRequestData.getPrPaymentDetailData().getCreatedBy())
                    : "";
            PaymentRequest paymentRequest = SCMDataConverter.convert(paymentRequestData, null, blockedBy, createdBy, requestingUnit,
                    vendor, null, detailBy, debitBalance, vendorDetailData.getCompanyDetails().getCreditDays(), null,
                    scmCache, new ArrayList<>());
            if (Objects.nonNull(paymentRequestData.getBusinessCostCentreName())) {
                paymentRequest.setBusinessCostDetailData(paymentRequestData.getBusinessCostCentreName());
            } else if (Objects.nonNull(paymentRequest.getRequestingUnit())) {
                paymentRequest.setBusinessCostDetailData(paymentRequest.getRequestingUnit().getName());
            }
            if (Objects.nonNull(paymentRequestData.getAdvancePaymentData())) {
                VendorAdvancePayment vendorAdvancePayment = VendorAdvancePayment.builder()
                        .advanceStatus("ALL").build();
                if (paymentRequestData.getType().equalsIgnoreCase("SERVICE_RECEIVED")) {
                    vendorAdvancePayment.setAdvanceType(SCMServiceConstants.SO_ADVANCE);
                }
                if (paymentRequestData.getType().equalsIgnoreCase("GOODS_RECEIVED")) {
                    vendorAdvancePayment.setAdvanceType(SCMServiceConstants.PO_ADVANCE);
                }
                VendorAdvancePayment advance = null;
                try {
                    AdvancePaymentData advancePaymentData = paymentRequestData.getAdvancePaymentData();
                    if (Objects.isNull(vendorAdvancePayment.getAdvanceType())) {
                        vendorAdvancePayment.setAdvanceType(advancePaymentData.getAdvanceType());
                    }
                    if (vendorAdvancePayment.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                        if (Objects.nonNull(advancePaymentData.getPurchaseOrderData())) {
                            advance = getVendorAdvancePayment(vendorAdvancePayment, true, advancePaymentData);
                        }
                    } else {
                        if (Objects.nonNull(advancePaymentData.getServiceOrderData())) {
                            advance = getVendorAdvancePayment(vendorAdvancePayment, true, advancePaymentData);
                        }
                    }
                } catch (Exception e) {
                    LOG.info("Exception during Vendor Advance ..!", e);
                }
                paymentRequest.setAdvancePayment(advance);
                paymentRequest.setAdvanceAmount(paymentRequestData.getAdvanceAmount());
            }
            paymentRequests.add(paymentRequest);
        });
        return paymentRequests;
    }

    private Date getPaymentDate(PaymentRequest paymentRequest, VendorDetailData vendorDetailData) {
        Integer creditDays = 0;
        if (vendorDetailData != null && vendorDetailData.getCompanyDetails() != null && vendorDetailData.getCompanyDetails().getCreditDays() != null) {
            creditDays = vendorDetailData.getCompanyDetails().getCreditDays();
        }
        Date paymentDate = SCMUtil.addDays(paymentRequest.getPaymentInvoice().getInvoiceDate(), creditDays);
        if (creditDays == 0) {
            paymentDate = paymentRequest.getPaymentInvoice().getInvoiceDate();
        }
//        if (paymentDate.compareTo(SCMUtil.getCurrentDateTimeIST()) <= 0) {
//            paymentDate = SCMUtil.addDays(SCMUtil.getCurrentDateTimeIST(), 5);
//        }
        if (paymentDate.compareTo(SCMUtil.addDays(SCMUtil.getCurrentDateTimeIST(), 5)) <= 0) {
            paymentDate = SCMUtil.addDays(SCMUtil.getCurrentDateTimeIST(), 5);
        }
        return paymentDate;
    }

    private Date getDateAfterRemovingThreshold(Date date) {
        int threshold = props.getPaymentThresholdDays();
        return SCMUtil.getPreviousBusinessDateIST(date, threshold);
    }

    private Date getWorkingDay(Date date) {
        //check for sunday
        LOG.info("Current Date is :: {}", date);
        LOG.info("checking for sunday on date of : {}", date);
        Boolean sundayCheck = SCMUtil.isSunday(date);
        if (sundayCheck == true) {
            LOG.info("It is sunday : {}", date);
            return getWorkingDay(SCMUtil.getPreviousBusinessDateIST(date, 1));
        } else {
            LOG.info("checking for second saturday on date of : {}", date);
            List<Date> secondAndFourthsaturdays = SCMUtil.getSecondOrFourthSaturday(date);
            for (Date d : secondAndFourthsaturdays) {
                LOG.info("sec and f sats are : {}", d);
            }
            Boolean secondOrFourthSaturdayCheck = secondAndFourthsaturdays.contains(date);
            if (secondOrFourthSaturdayCheck == true) {
                LOG.info("it is second saturday going to before date : {}", date);
                return getWorkingDay(SCMUtil.getPreviousBusinessDateIST(date, 1));
            } else {
                //check from database for holiday
                Boolean isHolidayInDatabase = paymentRequestManagementDao.isHoliday(date);
                if (isHolidayInDatabase == true) {
                    return getWorkingDay(SCMUtil.getPreviousBusinessDateIST(date, 1));
                } else {
                    return date;
                }
            }
        }
    }

    private void saveGrTopaymentmapping(VendorGoodsReceivedData vendorGoodsReceivedData, PaymentRequestStatus paymentRequestStatus) throws SumoException {
        GoodsRecievedToPaymentRequestMapping goodsRecievedToPaymentRequestMapping = new GoodsRecievedToPaymentRequestMapping();
        goodsRecievedToPaymentRequestMapping.setPaymentRequestId(vendorGoodsReceivedData.getPaymentRequestData().getId());
        goodsRecievedToPaymentRequestMapping.setGoodsRecievedId(vendorGoodsReceivedData.getGoodsReceivedId());
        goodsRecievedToPaymentRequestMapping.setCurrentStatus(paymentRequestStatus.value());
        goodsRecievedToPaymentRequestMapping.setLastUpdated(SCMUtil.getCurrentTimestamp());
        paymentRequestManagementDao.add(goodsRecievedToPaymentRequestMapping, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updatePrFilingNo(Integer prId, String filingNo) throws SumoException {
        PaymentRequestData paymentRequestData = paymentRequestManagementDao.find(PaymentRequestData.class, prId);
        if (paymentRequestData != null && paymentRequestData.getCurrentStatus().equals((PaymentRequestStatus.PAID.value()))) {
            paymentRequestData.setFilingNumber(filingNo);
            paymentRequestData.setCurrentStatus(PaymentRequestStatus.CLOSED.value());
            paymentRequestData = paymentRequestManagementDao.update(paymentRequestData, false);
            PaymentRequestStatusChangeVO changeRequest = new PaymentRequestStatusChangeVO();
            changeRequest.setCurrentStatus(PaymentRequestStatus.PAID);
            changeRequest.setNewStatus(PaymentRequestStatus.CLOSED);
            changeRequest.setPaymentRequestId(prId);
            changeRequest.setUpdatedBy(paymentRequestData.getPrPaymentDetailData().getCreatedBy());
            logPaymentRequestStatusChange(changeRequest);
            String logData = "Payment request id: " + paymentRequestData.getId() + " closed by "
                    + masterDataCache.getEmployee(changeRequest.getUpdatedBy()) + "[" + changeRequest + "]";
            logPaymentRequestData(logData, paymentRequestData.getId());
            return true;
        }

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addHoliday(HolidayDetails holidayDetails) {
        return paymentRequestManagementDao.addHoliday(holidayDetails);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<HolidayDetails> listOfHolidays(String holidayType, Integer year) {
        return paymentRequestManagementDao.listOfHolidays(holidayType, year);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean activateDeActivateHoliday(HolidayActivateDeactivate request) {
        try {
            HolidaysListData holidaysListData = paymentRequestManagementDao.find(HolidaysListData.class, request.getHolidayListId());
            if (Objects.nonNull(holidaysListData)) {
                if (request.getActivateOrDeactivate()) {
                    holidaysListData.setStatus(AppConstants.ACTIVE);
                } else {
                    holidaysListData.setStatus(AppConstants.IN_ACTIVE);
                }
                holidaysListData.setUpdatedBy(request.getUpdatedBy());
                holidaysListData.setUpdatedAt(AppUtils.getCurrentTimestamp());
            }
            paymentRequestManagementDao.update(holidaysListData, true);
            return true;
        } catch (Exception e) {
            LOG.error("Exception Occurred While activateDeActivateHoliday ::: ", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail uploadSoBreachApproval(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file, String docName) throws DocumentException, IOException {
        String fileName = docName + "_" + SCMUtil.getCurrentTimeISTStringWithNoColons() + "."
                + mimeType.name().toLowerCase();
        String baseDir = "SO_BREACH_APPROVAL" + File.separator + SCMUtil.getCurrentYear() + File.separator
                + SCMUtil.getCurrentMonthName() + File.separator + SCMUtil.getCurrentDayofMonth();
        return uploadDocument(type, mimeType, docType, userId, file, fileName, baseDir);
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    private PrProcessMetaData getPrProcessMetaData() {
        try {
            return PrProcessMetaData.builder()
                    .tdsLedgerRatesList(
                            PrMetaDataMapper.INSTANCE.toTdsLegerRateDomain(paymentRequestManagementDao.findAll(TdsLedgerRate.class)))
                    .gstStateMetaDataList(
                            PrMetaDataMapper.INSTANCE.toGstStateMetaDataDomain(paymentRequestManagementDao.findAll(GstStateMetaData.class)))
                    .gstOfStplsList(
                            PrMetaDataMapper.INSTANCE.toGstOfStplDomain(paymentRequestManagementDao.findAll(GstOfStpl.class))).build();
        } catch (Exception e) {
            LOG.error("Error while getting Pr Process MetaData , msg  : {}", e.getMessage());
            return null;
        }
    }
}
