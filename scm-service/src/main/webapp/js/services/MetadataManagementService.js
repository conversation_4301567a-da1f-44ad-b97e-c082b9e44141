/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 16-05-2016.
 */

angular.module('scmApp').service('metaDataService', ['$rootScope', '$http', 'appUtil', 'apiJson','$toastService',
    function ($rootScope, $http, appUtil, apiJson,$toastService) {

    var service = {};

    service.getCategories = getCategories;
    service.getAttrDefinitions = getAttrDefinitions;
    service.getSCMMetaData = getSCMMetaData;
    service.getUOMDefinitions = getUOMDefinitions;
    service.getProfileDefinitions = getProfileDefinitions;
    service.getAttributeValues = getAttributeValues;
    service.getAllPackagingMappings = getAllPackagingMappings;
    service.getAllPackagings = getAllPackagings;
    service.getCategoryAttributeMappings = getCategoryAttributeMappings;
    service.getMenuProductCategories = getMenuProductCategories;
    service.getTaxProfiles = getTaxProfiles;
    service.getTaxHsnCodes = getTaxHsnCodes;
    service.getUnitData = getUnitData;
    service.getScmProductDetails = getScmProductDetails;
    service.getSkuProductMap = getSkuProductMap;
    service.getPackagingMap = getPackagingMap;
    service.getCategoryAttributeValues = getCategoryAttributeValues;
    service.getVendors = getVendors;
    service.getTrimmedVendors = getTrimmedVendors;
    service.getVendorDetail = getVendorDetail;
    service.getVendorsForUnit = getVendorsForUnit;
    service.getVendorsForUnitTrimmed = getVendorsForUnitTrimmed;
    service.getVendorsForUnitAndType = getVendorsForUnitAndType;
        service.getBusinessTypes = getBusinessTypes;
    service.getSkuPricesAndTaxes = getSkuPricesAndTaxes;
    service.getSkuPricesAndTaxesForProfile = getSkuPricesAndTaxesForProfile;
    service.getInventoryLists = getInventoryList;
    service.uploadFile = uploadDocument;
    service.uploadGenericFile = uploadDocumentGeneric;
    service.downloadDocument = downloadDocument;
    service.downloadDocumentById = downloadDocumentById;
    service.getBusinessDate = getBusinessDate;
    service.getUnitToSkuMappings = getActiveUnitToSkuMappings;
    service.getUnitList = getUnitList;
    service.getUnitProductData = getUnitProductData;
    service.getInitiatedDayCloseEvent = getInitiatedDayCloseEvent;
    service.getRegularOrderingEvents = getRegularOrderingEvents;
    service.getAllVendors = getAllVendors;
    service.createManualBillBookEntry = createManualBillBookEntry;
    service.cancelManualBillBookEntry = cancelManualBillBookEntry;
    service.getSkuListForUnit = getSkuListForUnit;
    service.getSkusMapped = getSkusMapped;
    service.getFileExtension = getFileExtension;
    service.isImage = isImage;
    service.getCompanyList = getCompanyList;
    service.getBrandList = getBrandList;
    service.getPurchaseRoles = getPurchaseRoles;
    service.getServiceVendors = getServiceVendors;
    service.getCostCenters = getCostCenters;
    service.getVendorLocations = getVendorLocations;
    service.getLocations = getLocations;
    service.getStates = getStates;
    service.getAllBusinessCostCenters = getAllBusinessCostCenters;
    service.getBusinessCostCenters = getBusinessCostCenters;
    service.downloadExcell = downloadExcell;
    service.getVendorAdvancePayment = getVendorAdvancePayment;

        function getVendorAdvancePayment(vendorAdvancePayment, callBack, error) {
            $http({
                method: "POST",
                url: apiJson.urls.paymentRequestManagement.getVendorAdvancePayment,
                data: vendorAdvancePayment
            }).then(function success(response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    callBack(response.data);
                } else {
                    callBack(null);
                }
            }, function error(response) {
                console.log("error:" + response);
                error(response);
            });
        }

        function getTrimmedVendors(callBack) {
            $http({
                url: apiJson.urls.vendorManagement.vendorsTrimmed,
                method: 'GET'
            }).success(function (response) {
                callBack(response);
            }).error(function (response) {
                var emptyList = [];
                callBack(emptyList);
                console.log("Error Occurred :: ",response);
            });
        }

        function downloadExcell(data , params , skipColumns){
            $http({
                url: apiJson.urls.scmMetadata.downloadExcel,
                method: "POST",
                responseType: 'arraybuffer',
                data: {
                    "data" : data,
                    "skipColumns" : skipColumns
                },
                params : params
            }).success(function (data) {
                if (appUtil.checkEmpty(data)) {
                    var fileName = "download_" + appUtil.formatDate(new Date(), "dd-MM-yyyy")  + ".xlsx";
                    var blob = new Blob([data], {
                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }, fileName);
                    saveAs(blob, fileName);
                } else {
                    $toastService.create("Could not Excell View. Please try again later");
                }
            }).error(function (err) {
                console.log("Error during getting data", err);
            });
        }


    function getLocations (type,callback) {
        var active = !!(!appUtil.isEmptyObject(type) && type === "active");
        var url = active ? apiJson.urls.scmMetadata.activeDeliveryLocations
                          : apiJson.urls.scmMetadata.deliveryLocations;
        $http({
            method: "GET",
            url: url,
        }).then(function success(response) {
            if(!appUtil.isEmptyObject(response)){
                if(callback!=undefined && typeof callback == "function"){
                    callback(response.data);
                }
            }else{
                $toastService.create("Error while fetching delivery locations!");
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function getStates (type,callback) {
        var active = !!(!appUtil.isEmptyObject(type) && type === "active");
        var url = active ? apiJson.urls.scmMetadata.activeStates : apiJson.urls.scmMetadata.states;
        $http({
            method: "GET",
            url: url,
        }).then(function success(response) {
            if(!appUtil.isEmptyObject(response)){
                if(callback!=undefined && typeof callback == "function"){
                    callback(response.data);
                }
            }else{
                $toastService.create("Error while fetching delivery states!");
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function getAllBusinessCostCenters(callback) {
        $http({
            method: 'GET',
            url: apiJson.urls.serviceReceivedManagement.bcc
        }).then(function success(response) {
            if (response.data != null) {
                var bcc = response.data;
                callback(bcc);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }
    function getBusinessCostCenters(costCenterId,callback) {
        $http({
            method: 'GET',
            url: apiJson.urls.serviceOrderManagement.businessCostCenter,
            params : {empId: appUtil.getCurrentUser().userId, costCenterId : costCenterId}
        }).then(function success(response) {
            if (response.data != null) {
                var bcc = response.data;
                callback(bcc);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function getCostCenters(callback) {
        $http({
            url: apiJson.urls.serviceOrderManagement.costCenters,
            method: 'GET',
            params : {
            	empId : appUtil.getCurrentUser().userId
            }
        }).then(function (response) {
            if(!appUtil.isEmptyObject(response.data) && typeof callback=="function"){
                callback(response.data);
            }
        }, function (response) {
            console.log("got error", response);
        });
    }

    function getServiceVendors(callback) {
        $http({
            url: apiJson.urls.vendorManagement.serviceVendors,
            method: 'GET',
        }).then(function (response) {
            if(!appUtil.isEmptyObject(response.data) && typeof callback=="function"){
                callback(response.data);
            }
        }, function (response) {
            console.log("got error", response);
        });
    }

    function getVendorLocations(vendorId, callback) {
        $http({
            url: apiJson.urls.vendorManagement.dispatchLocations,
            method: 'GET',
            params:{vendorId: vendorId}
        }).then(function (response) {
            if(!appUtil.isEmptyObject(response.data) && typeof callback=="function"){
                var locations = response.data.filter(function (l) {
                    return l.status == "ACTIVE";
                });
                callback(locations);
            }
        }, function (response) {
            console.log("got error", response);
        });
    }

    function getPurchaseRoles(empId, callback) {
        $http({
            url: apiJson.urls.users.purchaseRoles,
            method: 'GET',
            params: {
                empId:empId
            }
        }).then(function (response) {
            if(!appUtil.isEmptyObject(response.data) && typeof callback=="function"){
                callback(response.data);
            }
        }, function (response) {
            console.log("got error", response);
        });
    }

    function getSkuListForUnit(unitId, callback) {
        $http({
            url: apiJson.urls.skuMapping.getSkuForUnit,
            method: 'POST',
            data: unitId
        }).then(function (response) {
            if(!appUtil.isEmptyObject(response) && typeof callback == "function"){
                callback(response.data);
            }else {
                $toastService.create("Could not find SKUs mapped to the selected Unit");
            }
        }, function (response) {
            console.log("got error", response);
        });
    }

    function getAllVendors(){
        return $http({
            method : "GET",
            url : apiJson.urls.skuMapping.getAllVendors
        }).then(function (response) {
            return !appUtil.isEmptyObject(response.data) ? response.data : null;
        }, function (response) {
            console.log("Response from backend :::",response);
        });
    }

    function getInitiatedDayCloseEvent(unitId) {
        $http({
            method:"GET",
            url:apiJson.urls.warehouseClosing.dayClose+"/"+unitId
        }).then(function(response){
            if(!appUtil.isEmptyObject(response.data) && !appUtil.isEmptyObject(response.data)){
                if(response.data.status=="INITIATED"){
                    $rootScope.dayCloseEvent = response.data;
                    $rootScope.restrictAll = true;
                }
            }else{
                $rootScope.restrictAll = false;
            }
        },function(error){
            console.log("Got error while getting day close");
        });
    }

        function getRegularOrderingEvents(unitId,callBack) {
            $http({
                method:"GET",
                url:apiJson.urls.warehouseClosing.getRegularOrderingEvents,
                params: {
                    "unitId":unitId
                }
            }).then(function(response){
                $rootScope.orderingEvents = response.data;
                if (callBack != undefined && callBack != null) {
                    callBack(response.data);
                }
            },function(error){
                console.log("Got error while getting day close",error);
                $rootScope.orderingEvents = [];
                if (callBack != undefined && callBack != null) {
                    callBack($rootScope.orderingEvents);
                }
            });
        }

    function getUnitList(callback){
        $http({
            method : "GET",
            url : apiJson.urls.skuMapping.getAllUnit
        }).then(function success(response) {
            callback(response.data);
        }, function error(response) {
            console.log("error:" + response);
        });
    }


    function getCategoryAttributeValues(){
        return $http({
            method:"GET",
            url:apiJson.urls.categoryManagement.attributeValuesByCategory
        }).then(function(response){
            appUtil.setCategoryAttributeValues(response.data);
            return response.data;
        },function(response){
            console.log("Got error while loading attribute values",response);
        });
    }

    function getCategories(callback) {
        if (appUtil.isEmptyObject(appUtil.getMetadata().categoryDefinitions)) {
            $http({
                method: 'GET',
                url: apiJson.urls.categoryManagement.categories
            }).then(function success(response) {
                appUtil.getMetadata().categoryDefinitions = response.data;
                appUtil.setMetadata(appUtil.getMetadata());
            }, function error(response) {
                console.log("error:" + response);
            }).finally(function () {
                callback(appUtil.getMetadata().categoryDefinitions);
            });
        } else {
            callback(appUtil.getMetadata().categoryDefinitions);
        }
    }

    function getInventoryList(){
        if (appUtil.isEmptyObject(appUtil.getMetadata().inventoryList)) {
            $http({
                method: 'GET',
                url: apiJson.urls.scmMetadata.inventoryList
            }).then(function success(response) {
                appUtil.getMetadata().inventoryList = response.data;
                appUtil.setMetadata(appUtil.getMetadata());
            }, function error(response) {
                console.log("error:" + response);
            });
        }
    }

    function getCompanyList(callback){
    	$http({
    		method: 'GET',
    		url: apiJson.urls.scmMetadata.companyList
    	}).then(function success(response) {
            var companies = response.data;
            appUtil.setCompanyList(companies);
            if(callback!==undefined && typeof callback == "function"){
                callback(companies);
            }
    	}, function error(response) {
    		console.log("error:" + response);
    	});
    }

        function getBrandList(callback){
            $http({
                method: 'GET',
                url: apiJson.urls.scmMetadata.brandList
            }).then(function success(response) {
                var brands = response.data;
                appUtil.setBrandList(brands);
                if(callback!==undefined && typeof callback == "function"){
                    callback(brands);
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        }

    function getAttrDefinitions(callback) {
        if (appUtil.isEmptyObject(appUtil.getMetadata().attributeDefinitions)) {
            $http({
                method: 'GET',
                url: apiJson.urls.attributeManagement.definitions
            }).then(function success(response) {
                appUtil.getMetadata().attributeDefinitions = response.data;
                appUtil.setMetadata(appUtil.getMetadata());
            }, function error(response) {
                console.log("error:" + response);
            }).finally(function () {
                callback(appUtil.getMetadata().attributeDefinitions);
            });
        } else {
            callback(appUtil.getMetadata().attributeDefinitions);
        }
    }

    function getSCMMetaData(callback, reload) {
        if (reload || appUtil.isEmptyObject(appUtil.getMetadata())) {
            // getting default metadata after login for the user
            $http({
                method: 'GET',
                url: apiJson.urls.scmMetadata.metadata
            }).then(function success(response) {
                //console.log(response);
                appUtil.setMetadata(response.data);
            }, function error(response) {
                console.log("error:" + response);
            }).finally(function () {
                callback(appUtil.getMetadata());
            });
        } else {
            callback(appUtil.getMetadata());
        }
    }

    function getUOMDefinitions(callback) {
        if (appUtil.isEmptyObject(appUtil.getMetadata()) || appUtil.isEmptyObject(appUtil.getMetadata().uomMetadata)) {
            // getting default metadata after login for the user
            $http({
                method: 'GET',
                url: apiJson.urls.scmMetadata.uomMetadata
            }).then(function success(response) {
                var metadata = appUtil.getMetadata();
                metadata = appUtil.isEmptyObject(metadata) ? {} : metadata;
                metadata.uomMetadata = response.data;
                appUtil.setMetadata(metadata);
            }, function error(response) {
                console.log("error:" + response);
            }).finally(function () {
                callback(appUtil.getMetadata().uomMetadata);
            });
        } else {
            callback(appUtil.getMetadata().uomMetadata);
        }
    }

    function getProfileDefinitions(callback) {
        if(appUtil.isEmptyObject(appUtil.getMetadata()) || appUtil.isEmptyObject(appUtil.getMetadata().profileDefinitions)) {
            $http({
                method: 'GET',
                url: apiJson.urls.profileManagement.profileDefinition
            }).then(function success(response) {
                console.log(response);
                var metadata = appUtil.getMetadata();
                metadata = appUtil.isEmptyObject(metadata) ? {} : metadata;
                metadata.profileDefinitions = response.data;
                appUtil.setMetadata(metadata);
            }, function error(response) {
                console.log("error:" + response);
            }).finally(function () {
                callback(appUtil.getMetadata().profileDefinitions);
            });
        } else {
            callback(appUtil.getMetadata().profileDefinitions);
        }

    }

    function getVendorsForUnit(unitId,callback){
        var reqObj = {unitId:unitId};
        $http({
            method: "GET",
            url: apiJson.urls.skuMapping.getVendorsForUnit,
            params:reqObj
        }).then(function success(response) {
            callback(response.data);
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function getVendorsForUnitTrimmed(unitId,callback){
        var reqObj = {unitId:unitId};
        $http({
            method: "GET",
            url: apiJson.urls.skuMapping.getVendorsForUnitTrimmed,
            params:reqObj
        }).then(function success(response) {
            callback(response.data);
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function getVendorsForUnitAndType(unitId, type, callback) {
        var reqObj = {unitId:unitId, type:type};
        $http({
            method: "GET",
            url: apiJson.urls.skuMapping.getVendorsForUnit,
            params:reqObj
        }).then(function success(response) {
            callback(response.data);
        }, function error(response) {
            console.log("error:" + response);
        });
    }

        function getBusinessTypes(callback) {
            $http({
                method: "GET",
                url: apiJson.urls.skuMapping.businessTypes
            }).then(function success(response) {
                callback(response.data);
            }, function error(response) {
                console.log("error:" + response);
            });
        }

    function getVendors(callback){
        $http({
            method: "GET",
            url: apiJson.urls.vendorManagement.vendors
        }).then(function success(response) {
            if(typeof callback == 'function'){
                callback(response.data);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function getVendorDetail(vendorId, callback){
    	var reqObj = {vendorId:vendorId};
        $http({
            method: "GET",
            url: apiJson.urls.vendorManagement.vendor,
            params:reqObj
        }).then(function success(response) {
            if(typeof callback == 'function'){
                callback(response.data);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function getSkusMapped(vendorId,unitId,callback) {
        $http({
            method: "GET",
            url: apiJson.urls.skuMapping.skusMappedToVendor,
            params:{
                vendorId:vendorId,
                unitId:unitId
            }
        }).then(function success(response) {
            if(typeof callback == 'function'){
                callback(response.data);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function getSkuPricesAndTaxesForProfile(vendorId,dispatchId,unitId,roleIds,toPickFromMapping,callback) {

            $http({
                method: "GET",
                url: apiJson.urls.skuMapping.skuPriceAndTaxForProfile,
                params:{
                    vendorId:vendorId,
                    dispatchId:dispatchId,
                    deliveryUnitId:unitId,
                    roleIds: roleIds,
                    toPickFromMapping : toPickFromMapping
                }
            }).then(function success(response) {
                if(typeof callback == 'function'){
                    callback(response.data);
                }
            }, function error(response) {
                console.log("error:" + response);
            });
    }

    function getSkuPricesAndTaxes(vendorId,dispatchId,unitId,toPickFromMapping,callback){
        $http({
            method: "GET",
            url: apiJson.urls.skuMapping.skuPriceAndTaxList,
            params:{
                vendorId:vendorId,
                dispatchId:dispatchId,
                deliveryUnitId:unitId,
                toPickFromMapping : toPickFromMapping
            }
        }).then(function success(response) {
            if(typeof callback == 'function'){
                callback(response.data);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function createManualBillBookEntry(url,dataObject){
    	var promise=$http({
			method: "POST",
			url: url,
			data: dataObject
		}).then(function success(response) {
			return response.data;
		}, function error(response) {
			$toastService.create("Could not create manual bill book details. Please try again later");
			console.log("error:" , response);
		});
    	return promise;
    }

    function cancelManualBillBookEntry(url,transferOrderId){
    	var promise=$http({
			method: "POST",
			url: url,
			data: transferOrderId
		}).then(function success(response) {
			return response.data;
		}, function error(response) {
			$toastService.create("Could not cancel manual bill book details. Please try again later");
			console.log("error:" ,  response);
		});
    	return promise;
    }

    function getAttributeValues(callback){
        //if(appUtil.isEmptyObject(appUtil.getMetadata().attributeValues)){
            $http({
                method: 'GET',
                url: apiJson.urls.attributeManagement.values
            }).then(function (response) {
                //console.log(response.data);
                appUtil.getMetadata().attributeValues = response.data;
                appUtil.setMetadata(appUtil.metadata);
            }, function (response) {
                console.log(response);
            }).finally(function () {
                callback(appUtil.getMetadata().attributeValues);
            });
       /* }else{
            callback(appUtil.getMetadata().attributeValues);
        }*/
    }


    function getAllPackagingMappings(callback,refresh){
        if(appUtil.isEmptyObject(appUtil.getMetadata().packagingMappings) || refresh){
            $http({
                method: 'GET',
                url: apiJson.urls.productManagement.packagingMappings
            }).then(function (response) {
                //console.log(response.data);
                appUtil.getMetadata().packagingMappings = response.data;
                appUtil.setMetadata(appUtil.getMetadata());
            }, function (response) {
                console.log(response);
            }).finally(function () {
                callback(appUtil.getMetadata().packagingMappings);
            });
        }else{
            callback(appUtil.getMetadata().packagingMappings);
        }
    }

    function getAllPackagings(callback){
        if(appUtil.isEmptyObject(appUtil.getMetadata().productPackagings)){
            $http({
                method: 'GET',
                url: apiJson.urls.productManagement.packagings
            }).then(function (response) {
                //console.log(response.data);
                appUtil.getMetadata().productPackagings = response.data;
                appUtil.setMetadata(appUtil.getMetadata());
            }, function (response) {
                console.log(response);
            }).finally(function () {
                callback(appUtil.getMetadata().productPackagings);
            });
        }else{
            callback(appUtil.getMetadata().productPackagings);
        }
    }

    function getCategoryAttributeMappings(callback){
        if(appUtil.isEmptyObject(appUtil.getMetadata().categoryAttributeMappings)){
            $http({
                method: 'GET',
                url: apiJson.urls.categoryManagement.attributeMappings
            }).then(function success(response) {
                appUtil.getMetadata().categoryAttributeMappings = response.data;
                appUtil.setMetadata(appUtil.getMetadata());
            }, function error(response) {
                console.log("error:" + response);
            }).finally(function () {
                callback(appUtil.getMetadata().categoryAttributeMappings);
            });
        }else{
            callback(appUtil.getMetadata().categoryAttributeMappings);
        }
    }

    function getMenuProductCategories(){
        $http({
            method: "POST",
            url: apiJson.urls.unitMetadata.metadataCategories,
            data: appUtil.getCurrentUser().unitId
        }).then(function success(response) {
            appUtil.setMenuProductCategories(response.data.categories);
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function getTaxProfiles(){
        $http({
            method: "GET",
            url: apiJson.urls.unitMetadata.getAllTaxCategories
        }).then(function success(response) {
            var taxes = response.data;
            var filteredTaxes = [];
		for(var i in taxes){
		    if(taxes[i].status == 'ACTIVE'){
			    filteredTaxes.push(taxes[i]);
		    }
		}
            appUtil.setTaxProfiles(filteredTaxes);
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function getTaxHsnCodes(catId,countryId,taxId){
    	var promise = $http({
            method: "GET",
            url: apiJson.urls.taxMetadata.getTaxCodes,
            params: {
            	countryId:countryId,
            	categoryId:catId,
            	taxId:countryId

            }
        }).then(function success(response) {
            var tax = response.data;
            var filteredTaxes = [];
		for(var i in tax.taxes){
			    filteredTaxes.push(tax.taxes[i]);
		}
		// appUtil.setTaxHsnList(filteredTaxes);
		 return filteredTaxes;
        }, function error(response) {
            console.log("error:" + response);
        });
    	return promise;
    }


    function getBusinessDate(){
        /*$http({
            method: "GET",
            url: apiJson.urls.scmUnitManagement.businessDate
        }).then(function success(response) {
            //appUtil.setCurrentBusinessDate(response.data);

        }, function error(response) {
            console.log("error:" + response);
        });*/
    }

    function getActiveUnitToSkuMappings(unitId,callback){
        $http({
            method: "GET",
            url: apiJson.urls.warehouseClosing.getSkuIdsForUnit,
            params: {
                unitId:unitId
            }
        }).then(function success(response) {
            callback(response.data);
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function getUnitData(callback){
        $http({
            method: "POST",
            url: apiJson.urls.unitMetadata.unitData,
            data: appUtil.getCurrentUser().unitId
        }).then(function success(response) {
            var unit = response.data;
            appUtil.setUnitData(unit);
            if(callback && typeof callback=="function"){
                callback(unit);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    }


    function getUnitProductData(callback){
        $http({
            method: "POST",
            url: apiJson.urls.unitMetadata.unitProductData,
            data: appUtil.getCurrentUser().unitId
        }).then(function success(response) {
            var unit = appUtil.getUnitData();
            unit.products = response.data;
            if(callback && typeof callback=="function"){
                callback(unit);
            }
        },function(error){
            console.log("error:" + error);
        });
    }


    function getFileExtension(fileName) {
        var re = /(?:\.([^.]+))?$/;
        return re.exec(fileName)[1];
    }

    function isImage(fileExt){
        return fileExt=="jpg" || fileExt == "jpeg" || fileExt=="png";
    }

    function uploadDocument(type,docType,file,callback){
        if (file == null) {
            $toastService.create('File cannot be empty');
            return;
        }
        var fileExt = getFileExtension(file.name);
        if (fileExt.toLowerCase() == 'pdf' || isImage(fileExt.toLowerCase())) {
            var mimeType = fileExt.toUpperCase();
            var fd = new FormData();
            fd.append('type', type);
            fd.append('docType',docType);
            fd.append('mimeType',fileExt.toUpperCase());
            fd.append('userId',appUtil.getCurrentUser().userId);
            fd.append('file', file);
            $http.post(apiJson.urls.goodsReceivedManagement.uploadGR, fd, {
                transformRequest: angular.identity,
                headers: {
                    'Content-Type': undefined
                }
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                if (!appUtil.isEmptyObject(response)) {
                    $toastService.create("Upload successful");
                    callback(response);
                }else{
                    $toastService.create("Upload failed");
                }
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                $toastService.create("Upload failed");
            });
        } else {
            $toastService.create('Upload Failed , File Format not Supported');
        }
    }

    function uploadDocumentGeneric(type, docType, file, fileName, baseDir, callback){
        if (file == null) {
            $toastService.create('File cannot be empty');
            return;
        }
        var fileExt = getFileExtension(file.name);
        if (fileExt.toLowerCase() == 'pdf' || isImage(fileExt.toLowerCase())) {
            var mimeType = fileExt.toUpperCase();
            var fd = new FormData();
            fd.append('type', type);
            fd.append('docType',docType);
            fd.append('mimeType',fileExt.toUpperCase());
            fd.append('userId',appUtil.getCurrentUser().userId);
            fd.append('fileName',fileName);
            fd.append('baseDir',baseDir);
            fd.append('file', file);
            $http.post(apiJson.urls.genericResourceManagement.uploadDocumentGeneric, fd, {
                transformRequest: angular.identity,
                headers: {
                    'Content-Type': undefined
                }
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                if (!appUtil.isEmptyObject(response)) {
                    $toastService.create("Upload successful");
                    callback(response);
                }else{
                    $toastService.create("Upload failed");
                }
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                $toastService.create("Upload failed");
            });
        } else {
            $toastService.create('Upload Failed , File Format not Supported');
        }
    }

    function getScmProductDetails(){
        var unit = appUtil.getCurrentUser().unitId;
        return $http({
                method: "GET",
                url: apiJson.urls.productManagement.productDetails,
                params:{
                    unitId:unit
                }
            }).then(function success(response) {
                if(response.data != undefined){
                    appUtil.setScmProductDetails(response.data);
                    return response.data;
                }
            }, function error(response) {
                console.log("error:" + response);
            });
    }

    function getSkuProductMap(){
        return $http({
            method: "GET",
            url: apiJson.urls.productManagement.skuDetailsByProduct
        }).then(function success(response) {
            appUtil.setSkuProductMap(response.data);
            return response.data;
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    function downloadDocumentById(id){
        var params = {
            documentId : id
        };
        if(!appUtil.isEmptyObject(id)){
            $http({
                method:"GET",
                url:apiJson.urls.vendorManagement.getDocumentDetailById,
                params : params,
            }).then(function(response){
                downloadDocument(response.data);
            },function(error){
                $toastService.create("Could not fetch the document detail... Please try again");
            });
        }else{
            $toastService.create("Not a valid document id... Please check");
        }
    }

    function downloadDocument(value){
        if(!appUtil.isEmptyObject(value.documentLink)){
            $http({
                method:"POST",
                url:apiJson.urls.vendorManagement.downloadDocument,
                data: value,
                responseType: 'arraybuffer'
            }).then(function(response){
                var blob = new Blob([response.data], {type: appUtil.mimeTypes[value.mimeType]}, value.documentLink);
                saveAs(blob, value.documentLink);
            },function(error){
                $toastService.create("Could not download the document... Please try again");
            });
        }else{
            $toastService.create("Not a valid document... Please check");
        }
    }

    function getPackagingMap(){
        $http({
            method: "GET",
            url: apiJson.urls.productManagement.packagingMap
        }).then(function success(response) {
            appUtil.setPackagingMap(response.data);
        }, function error(response) {
            console.log("error:" + response);
        });
    }

    return service;
}]);
