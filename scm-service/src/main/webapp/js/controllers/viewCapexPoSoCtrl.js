'use strict';
angular.module('scmApp').controller('viewCapexPoSoCtrl', ['$rootScope', '$stateParams', '$scope', 'apiJson', '$http',
    'appUtil', '$toastService', '$alertService', 'metaDataService', 'Popeye', '$window', '$timeout', 'uiGridConstants',
    function ($rootScope, $stateParams, $scope, apiJson, $http, appUtil, $toastService, $alertService, metaDataService, Popeye, $window, $timeout, $uiGridConstants) {
        $scope.init = function (capexId, isExternalBtn) {
            $scope.capexRequestId = appUtil.isEmptyObject(capexId) ? angular.copy($stateParams.capexId) : capexId;
            $scope.summaryDepartmentList = [];
            $scope.capexSummaryShow = false;
            $scope.capexSummaryLoading = false;
            $scope.gridOptions = appUtil.getGridOptions($scope);
            $scope.emptyMsg = false;
            $scope.isExternalBtn = isExternalBtn;
            $scope.selectedItem = null
            $scope.poSoAmountDetails = null;
            if(!appUtil.isEmptyObject(angular.copy($stateParams.capexId))) {
                $scope.fetchBudgetSummary();
            }
        }

        $scope.changeCapexId = function(capexId) {
            $scope.capexRequestId = capexId;
        }

        $scope.fetchBudgetSummary = function () {
            $stateParams.capexId = null;
            $scope.init($scope.capexRequestId, $scope.isExternalBtn);
            $scope.isExternal = $scope.isExternalBtn;
            $scope.capexSummaryLoading = true;
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.fetchBudgetDetails,
                params: {
                    capexRequestId: $scope.capexRequestId,
                    isExternal: $scope.isExternal
                }
            }).then(function success(response) {
                if (response) {
                    if (response != null && response.data != null && response.data.length > 0) {
                        $scope.summaryDepartmentList = response.data.filter(function (el) { return (el.totalSO != 0 || el.totalPO != 0 || el.totalTO != 0 || el.totalRO != 0) });
                        if($scope.summaryDepartmentList.length != 0) {
                            $scope.capexSummaryShow = true;
                            $scope.emptyMsg = false;
                        } else {
                            $scope.emptyMsg = true;
                        }
                    } else if (response.data.length == 0) {
                        $scope.emptyMsg = true;
                        $toastService.create("No Orders found!");
                    }
                } else {
                    $scope.emptyMsg = true;
                    $toastService.create("Error in showing Capex Summary.");
                }
                $scope.getSoPoSummary();
                $scope.capexSummaryLoading = false;
            }, function error(response) {
                console.log("error:" + response);
                $scope.emptyMsg = false;
                $scope.capexSummaryLoading = false;
                $toastService.create("Error while getting Capex Summary.");
            });
        }

        $scope.getSoPoSummary = function () {
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.getOrderLevelSummary,
                params: {
                    capexId: $scope.capexRequestId,
                    isExternal: $scope.isExternal
                }
            }).then(function success(response) {
                $scope.poSoAmountDetails = response.data;
            }), function error(response) {
                console.log("error : ", response);
            }
        }

        $scope.showSOLevelDetail = function (item) {
            if (item.expanded) {
                item.expanded = !item.expanded;
                $scope.selectedItem = null
                return;
            }
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.getSoLevelSummaryByCapex,
                params: {
                    capexId: $scope.capexRequestId,
                    departmentId: item.departmentId
                }
            }).then(function (response) {
                if (response.data != null) {
                    if (response.data.length == 0) {
                        $toastService.create("No Data Found");
                        return;
                    }
                    $scope.soList = response.data;
                    $scope.selectedItem = item.departmentName + "_SO";
                    if (item.expanded != null) {
                        item.expanded = !item.expanded;
                    } else {
                        item.expanded = true;
                    }
                    var gridItemaArr = [];
                    $scope.soList.forEach(function (so) {
                        gridItemaArr.push(so);
                    });
                    if (item.expanded === true) {
                        $scope.gridOptions = {
                            showColumnFooter: true
                        };
                        $scope.gridOptions.data = gridItemaArr;
                        $scope.gridOptions.columnDefs = $scope.showSOLevel();
                    }
                    if (item.expanded === false) {
                        $scope.selectedItem = null;
                    }
                }
            })
        }

        $scope.showToOrRoLevelDetail = function (item, type) {
            if (item.expanded) {
                item.expanded = !item.expanded;
                $scope.selectedItem = null
                return;
            }
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.getToRoLevelByCapex,
                params: {
                    capexId: $scope.capexRequestId,
                    departmentId: item.departmentId,
                    orderType: type
                }
            }).then(function (response) {
                if (response.data != null) {
                    if (response.data.length == 0) {
                        $toastService.create("No Data Found");
                        return;
                    }
                    $scope.toRoList = response.data;
                    $scope.selectedItem = item.departmentName + "_" + type;
                    if (item.expanded != null) {
                        item.expanded = !item.expanded;
                    } else {
                        item.expanded = true;
                    }
                    if (item.expanded === true) {
                        $scope.gridOptions = {
                            showColumnFooter: true
                        };
                        var gridItemaArr = [];
                        if(type == "TO") {
                            $scope.toRoList.forEach(function (toRo) {
                                if(toRo.transferOrderItems != null) {
                                    toRo.transferOrderItems.forEach(function (item) {
                                        gridItemaArr.push(item);
                                    })
                                }
                            });
                            $scope.gridOptions.columnDefs = $scope.showTOLevel();
                        } else {
                            $scope.toRoList.forEach(function (toRo) {
                                if(toRo.requestOrderItems != null) {
                                    toRo.requestOrderItems.forEach(function (item) {
                                        gridItemaArr.push(item);
                                    })
                                }
                            });
                            $scope.gridOptions.columnDefs = $scope.showROLevel()
                        }
                        if(gridItemaArr.length == 0) {
                            $scope.selectedItem = null;
                            item.expanded = false;
                            $toastService.create("No Data Found");
                            return;
                        }
                        $scope.gridOptions.data = gridItemaArr;
                    }
                    if (item.expanded === false) {
                        $scope.selectedItem = null;
                    }
                }
            })
        }

        $scope.isSoSelected = function (name) {
            return ($scope.selectedItem == name + "_SO");
        }

        $scope.isPoSelected = function (name) {
            return ($scope.selectedItem == name + "_PO");
        }

        $scope.isToOrRoSelected = function(name, type) {
            if(type == null) {
                return ($scope.selectedItem == name + "_TO" || $scope.selectedItem == name + "_RO");
            } else {
                return ($scope.selectedItem == (name + type))
            }
        }

        $scope.isPoOrSoSelected = function (name) {
            return ($scope.selectedItem == name + "_PO" || $scope.selectedItem == name + "_SO");
        }

        $scope.showPOLevelDetail = function (item) {
            if (item.expanded) {
                item.expanded = !item.expanded;
                $scope.selectedItem = null
                return;
            }
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.getPOLevelSummaryByCapex,
                params: {
                    capexId: $scope.capexRequestId,
                    departmentId: item.departmentId
                }
            }).then(function (response) {
                if (response.data != null) {
                    if (response.data.length == 0) {
                        $toastService.create("No Data Found");
                        return;
                    }
                    $scope.poList = response.data;
                    $scope.selectedItem = item.departmentName + "_PO";
                    if (item.expanded != null) {
                        item.expanded = !item.expanded;
                    } else {
                        item.expanded = true;
                    }
                    var gridItemaArr = [];
                    $scope.poList.forEach(function (po) {
                        gridItemaArr.push(po);
                    });
                    if (item.expanded === true) {
                        $scope.gridOptions = {
                            showColumnFooter: true
                        };
                        $scope.gridOptions.data = gridItemaArr;
                        $scope.gridOptions.columnDefs = $scope.showPOLevel();
                    }
                    if (item.expanded === false) {
                        $scope.selectedItem = null;
                    }
                }
            });

        }

        $scope.showPOLevel = function () {
            return [
                {
                    field: 'id',
                    name: 'id',
                    enableCellEdit: false,
                    displayName: 'PO Id'
                },
                , {
                    field: 'dispatchLocation.vendorDetail.name',
                    name: 'dispatchLocation.vendorDetail.name',
                    enableCellEdit: false,
                    displayName: 'Vendor Name'
                }, {
                    field: 'billAmount',
                    name: 'billAmount',
                    enableCellEdit: false,
                    displayName: 'Cost',
                    cellFilter: 'number: 2'
                }, {
                    field: 'totalTaxes',
                    name: 'totalTaxes',
                    enableCellEdit: false,
                    displayName: 'Tax',
                    cellFilter: 'number: 2'
                },
                {
                    field: 'paidAmount',
                    name: 'paidAmount',
                    enableCellEdit: false,
                    displayName: 'Total Amount',
                    cellFilter: 'number: 2',
                    aggregationType: $uiGridConstants.aggregationTypes.sum

                },
                {
                    field: 'prAmount',
                    name: 'prAmount',
                    displayName: 'PR Amount',
                    enableCellEdit: false,
                    cellFilter: 'number: 2'
                },
                {
                    field: 'status',
                    name: 'status',
                    enableCellEdit: false,
                    displayName: 'Status',
                }
            ];
        };

        $scope.showSOLevel = function () {
            return [
                {
                    field: 'id',
                    name: 'id',
                    enableCellEdit: false,
                    displayName: 'SO Id'
                },
                , {
                    field: 'vendor.name',
                    name: 'vendor.name',
                    enableCellEdit: false,
                    displayName: 'Vendor Name'
                }, {
                    field: 'totalCost',
                    name: 'totalCost',
                    enableCellEdit: false,
                    displayName: 'Cost',
                    cellFilter: 'number: 2'
                }, {
                    field: 'totalTaxes',
                    name: 'totalTaxes',
                    enableCellEdit: false,
                    displayName: 'Tax',
                    cellFilter: 'number: 2'
                },
                {
                    field: 'totalAmount',
                    name: 'totalAmount',
                    enableCellEdit: false,
                    displayName: 'Total Amount',
                    cellFilter: 'number: 2',
                    aggregationType: $uiGridConstants.aggregationTypes.sum

                },
                {
                    field: 'prAmount',
                    name: 'prAmount',
                    displayName: 'PR Amount',
                    enableCellEdit: false,
                    cellFilter: 'number: 2',
                    aggregationType: $uiGridConstants.aggregationTypes.sum
                },
                {
                    field: 'status',
                    name: 'status',
                    enableCellEdit: false,
                    displayName: 'Status',
                }
            ];
        };

        $scope.showTOLevel = function () {
            return [
                {
                    field: 'id',
                    name: 'id',
                    enableCellEdit: false,
                    displayName: 'TO Item Id',
                }, {
                    field: 'skuName',
                    name: 'skuName',
                    enableCellEdit: false,
                    displayName: 'Sku Name',
                }, {
                    field: 'uom',
                    name: 'uom',
                    enableCellEdit: false,
                    displayName: 'UOM',
                }, {
                    field: 'unitPrice',
                    name: 'unitPrice',
                    enableCellEdit: false,
                    displayName: 'Unit Price',
                }, {
                    field: 'price',
                    name: 'price',
                    enableCellEdit: false,
                    displayName: 'Price',
                }, {
                    field: 'negotiatedUnitPrice',
                    name: 'negotiatedUnitPrice',
                    enableCellEdit: false,
                    displayName: 'Negotiated Price',
                }
            ];
        };

        $scope.showROLevel = function () {
            return [
                {
                    field: 'id',
                    name: 'id',
                    enableCellEdit: false,
                    displayName: 'RO Item Id',
                }, {
                    field: 'skuName',
                    name: 'skuName',
                    enableCellEdit: false,
                    displayName: 'Sku Name',
                }, {
                    field: 'uom',
                    name: 'uom',
                    enableCellEdit: false,
                    displayName: 'UOM',
                }, {
                    field: 'unitPrice',
                    name: 'unitPrice',
                    enableCellEdit: false,
                    displayName: 'Unit Price',
                }, {
                    field: 'price',
                    name: 'price',
                    enableCellEdit: false,
                    displayName: 'Price',
                }, {
                    field: 'negotiatedUnitPrice',
                    name: 'negotiatedUnitPrice',
                    enableCellEdit: false,
                    displayName: 'Negotiated Price',
                }
            ];
        };

        $scope.exportDataToSheetForSO = function (item) {
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.getSOByDepartment,
                params: {
                    capexRequestId: $scope.capexRequestId,
                    departmentId: item.departmentId
                }
            }).then(function (response) {
                if (response.data != null) {
                    $scope.soList = response.data;

                    var soItemLevelData = [];
                    $scope.soList.forEach(function (so) {
                        so.orderItems.forEach(function (soItem) {
                            var receivingPrice = soItem.receivedQuantity * soItem.unitPrice;
                            soItem.receivingAmount = (receivingPrice + (receivingPrice * soItem.taxRate) / 100);
                            var item = [soItem.serviceOrderId, soItem.costElementName, so.vendorName, soItem.serviceDescription, soItem.totalCost, soItem.totalTax, so.amountPaid, soItem.receivingAmount, soItem.paidAmount, so.status ];
                            soItemLevelData.push(item);
                        })
                    });
                    var headerNames = ["SO Id","Cost Element Name","Vendor Name","Service Description","Cost","Tax","Total Amount","Receiving Amount","Paid Amount","Status"];
                    $scope.downloadItemData(soItemLevelData, headerNames, "SoItemData");

                }
            });

        }

        $scope.exportDataToSheetForPO = function (item) {
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.getPOByDepartment,
                params: {
                    capexId: $scope.capexRequestId,
                    departmentId: item.departmentId
                }
            }).then(function (response) {
                if (response.data != null) {
                    $scope.poList = response.data;
                    $scope.poArrayBody = [];
                    $scope.poList.forEach(function(po) {
                        var poHead = [po.purchaseOrderId, po.purchaseOrderItemId, po.skuId, po.skuName, po.requestedQuantity, po.amountPaid, po.goodsReceivedId, po.goodsReceivedItemId, po.totalAmount, po.currentStatus];
                        $scope.poArrayBody.push(poHead);
                    })
                    var headerNames = ["Purchase Order Id","Purchase Order Item Id","Sku Id","Sku Name","Requested Quantity","Amount Paid","Goods Received Id","Goods Received Item Id","Total Amount","Current Status"];
                    $scope.downloadItemData($scope.poArrayBody, headerNames, "PoItemData");
                }
            });

        }

        $scope.dowloadToOrRoItemData = function(item, type) {
            $http({
                method: "GET",
                url: apiJson.urls.capexManagement.downlaodToRoByCapex,
                params: {
                    capexId: $scope.capexRequestId,
                    departmentId: item.departmentId,
                    orderType: type
                },
                responseType: 'arraybuffer',
            }).success(function (response) {
                console.log("RESPONSE :", response);
                var fName = type === 'TO' ? "TransferOrderData.xlsx" : "RequestOrderData.xlsx";
                var blob = new Blob([response],
                    {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;'}, fName);
                saveAs(blob, fName);
            }).error(function (response) {
                $toastService.create("Download failed");
                console.log("Error downloading : ", response);
            });
        }
        

        $scope.downloadItemData =function(body, headerNames, fileName) {
            $scope.excelRequestData = {
                fileName,
                headerNames,
                body
            }
            $http({
                method: "POST",
                url: apiJson.urls.genericResourceManagement.downloadExcelAny,
                data: $scope.excelRequestData,
                responseType: 'arraybuffer',
            }).success(function (response) {
                console.log("RESPONSE :", response);
                var fName = fileName + ".xlsx";
                var blob = new Blob([response],
                    {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;'}, fName);
                saveAs(blob, fName);
            }).error(function (response) {
                $toastService.create("Download failed");
                console.log("Error downloading : ", response);
            });
        }



    }
]);